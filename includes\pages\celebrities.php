<?php
/**
 * Celebrities Page
 */

// Check if celebrities table exists
$check_table = $conn->query("SHOW TABLES LIKE 'celebrities'");
$table_exists = $check_table->num_rows > 0;

// Create celebrities table if it doesn't exist
if (!$table_exists) {
    $sql = "CREATE TABLE celebrities (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        name_ar VARCHAR(100) NOT NULL,
        bio TEXT,
        bio_ar TEXT,
        image VARCHAR(255) NOT NULL,
        video VARCHAR(255),
        featured TINYINT(1) DEFAULT 0,
        role VARCHAR(100),
        role_ar VARCHAR(100),
        facebook VARCHAR(255),
        twitter VARCHAR(255),
        instagram VARCHAR(255),
        youtube VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        // Insert sample celebrities
        $sample_celebrities = [
            // Featured celebrities
            ['John Smith', 'جون سميث', 'Popular social media influencer with over 5 million followers.', 'مؤثر شهير على وسائل التواصل الاجتماعي مع أكثر من 5 مليون متابع.', 'celebrity1.jpg', '', 1, 'Social Media Influencer', 'مؤثر وسائل التواصل الاجتماعي', 'johnsmith', 'johnsmith', 'johnsmith', 'johnsmith'],
            ['Sarah Johnson', 'سارة جونسون', 'Award-winning actress and social media personality.', 'ممثلة حائزة على جوائز وشخصية مؤثرة على وسائل التواصل الاجتماعي.', 'celebrity2.jpg', '', 1, 'Actress & Influencer', 'ممثلة ومؤثرة', 'sarahjohnson', 'sarahjohnson', 'sarahjohnson', ''],

            // Regular celebrities
            ['Michael Brown', 'مايكل براون', 'TV presenter and social media expert.', 'مقدم تلفزيوني وخبير في وسائل التواصل الاجتماعي.', 'celebrity3.jpg', '', 0, 'TV Presenter', 'مقدم تلفزيوني', '', 'michaelbrown', 'michaelbrown', 'michaelbrown'],
            ['Emily Davis', 'إيميلي ديفيس', 'Fashion and lifestyle influencer.', 'مؤثرة في مجال الموضة ونمط الحياة.', 'celebrity4.jpg', '', 0, 'Fashion Influencer', 'مؤثرة في مجال الموضة', 'emilydavis', 'emilydavis', 'emilydavis', ''],
            ['Ahmed Hassan', 'أحمد حسن', 'Actor and content creator with a focus on comedy.', 'ممثل ومنشئ محتوى متخصص في الكوميديا.', 'celebrity5.jpg', '', 0, 'Actor & Content Creator', 'ممثل ومنشئ محتوى', 'ahmedhassan', 'ahmedhassan', 'ahmedhassan', 'ahmedhassan'],
            ['Layla Ali', 'ليلى علي', 'Singer and social media star.', 'مغنية ونجمة وسائل التواصل الاجتماعي.', 'celebrity6.jpg', '', 0, 'Singer', 'مغنية', 'laylaali', 'laylaali', 'laylaali', 'laylaali'],
            ['David Wilson', 'ديفيد ويلسون', 'Film director and social media consultant.', 'مخرج سينمائي ومستشار وسائل التواصل الاجتماعي.', 'celebrity7.jpg', '', 0, 'Director', 'مخرج', 'davidwilson', '', 'davidwilson', 'davidwilson'],
            ['Nora Khan', 'نورا خان', 'Writer and digital content strategist.', 'كاتبة واستراتيجية محتوى رقمي.', 'celebrity8.jpg', '', 0, 'Writer', 'كاتبة', 'norakhan', 'norakhan', 'norakhan', '']
        ];

        $insert_sql = "INSERT INTO celebrities (name, name_ar, bio, bio_ar, image, video, featured, role, role_ar, facebook, twitter, instagram, youtube) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);

        foreach ($sample_celebrities as $celebrity) {
            $insert_stmt->bind_param("ssssssissssss", $celebrity[0], $celebrity[1], $celebrity[2], $celebrity[3], $celebrity[4], $celebrity[5], $celebrity[6], $celebrity[7], $celebrity[8], $celebrity[9], $celebrity[10], $celebrity[11], $celebrity[12]);
            $insert_stmt->execute();
        }
    }
}

// Get celebrities from database
function get_celebrities_by_role($role = '', $page = 1, $per_page = 6) { // Reduced per_page to 6 to show more pages
    global $conn;

    // Calculate offset
    $offset = ($page - 1) * $per_page;

    // Base query
    $sql_count = "SELECT COUNT(*) as total FROM celebrities";
    $sql = "SELECT * FROM celebrities";

    // Add role filter if specified
    if (!empty($role)) {
        $where_clause = " WHERE role = ?";
        $sql_count .= $where_clause;
        $sql .= $where_clause;
    }

    // Add order and limit
    $sql .= " ORDER BY featured DESC, name ASC LIMIT ?, ?";

    // Get total count
    if (!empty($role)) {
        $count_stmt = $conn->prepare($sql_count);
        $count_stmt->bind_param("s", $role);
    } else {
        $count_stmt = $conn->prepare($sql_count);
    }

    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total = $count_result->fetch_assoc()['total'];

    // Get celebrities
    if (!empty($role)) {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sii", $role, $offset, $per_page);
    } else {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $offset, $per_page);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $celebrities = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $celebrities[] = $row;
        }
    }

    return [
        'celebrities' => $celebrities,
        'total' => $total
    ];
}

// We'll use the existing get_featured_celebrities function from functions.php

// Get all roles
function get_celebrity_roles() {
    global $conn;

    $sql = "SELECT DISTINCT role FROM celebrities WHERE role IS NOT NULL ORDER BY role ASC";
    $result = $conn->query($sql);
    $roles = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $roles[] = $row['role'];
        }
    }

    return $roles;
}

// Get selected role from URL
$selected_role = isset($_GET['role']) ? $_GET['role'] : '';

// Get current page from URL
$current_page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
$per_page = 6; // Reduced number of celebrities per page to show more pages

// Get celebrities with pagination
$result = get_celebrities_by_role($selected_role, $current_page, $per_page);
$celebrities = $result['celebrities'];
$total_celebrities = $result['total'];

// Calculate total pages
$total_pages = ceil($total_celebrities / $per_page);

// Ensure current page is valid
if ($current_page < 1) {
    $current_page = 1;
} elseif ($current_page > $total_pages && $total_pages > 0) {
    $current_page = $total_pages;
}

// Get featured celebrities
$featured_celebrities = get_featured_celebrities();

// Get all roles
$roles = get_celebrity_roles();

// Page title
$page_title = get_content('celebrities', 'title', 'Festival Stars');
?>
<style>
    .celebrities-title{    margin-top: 5%;}
@media (max-width: 768px) {
    .celebrities-title {
        margin-top: 17%;
    }
}
</style>
<!-- Celebrities Page -->
<section class="celebrities-section">
    <div class="container">
        <div class="celebrities-header">
            <h1 class="celebrities-title"><?php echo $page_title; ?></h1>
            <p class="celebrities-description"><?php echo get_content('celebrities', 'description', 'Meet the stars and celebrities of the Social Media Festival. Discover their profiles and achievements.'); ?></p>
        </div>

        <!-- Featured Celebrities -->
        <?php if (!empty($featured_celebrities)): ?>
        <div class="featured-celebrities">
            <div class="featured-title">
                <h2 class="category-title"><?php echo get_content('celebrities', 'featured', 'Featured Stars'); ?></h2>
            </div>
            <div class="featured-slider">
                <div class="swiper-container featured-swiper">
                    <div class="swiper-wrapper">
                        <?php foreach ($featured_celebrities as $celebrity): ?>
                        <div class="swiper-slide">
                            <div class="featured-slide">
                                <div class="featured-image">
                                    <img src="<?php echo asset_url('uploads/' . (isset($celebrity['image']) ? $celebrity['image'] : 'default-celebrity.jpg')); ?>" alt="<?php echo htmlspecialchars($celebrity['name']); ?>">
                                </div>
                                <div class="featured-info">
                                    <h3 class="featured-name"><?php echo $current_lang === 'ar' ? $celebrity['name_ar'] : $celebrity['name']; ?></h3>
                                    <div class="featured-role"><?php echo $current_lang === 'ar' ? $celebrity['role_ar'] : $celebrity['role']; ?></div>
                                    <div class="featured-actions">
                                        <a href="<?php echo base_url('?page=celebrity&id=' . $celebrity['id']); ?>" class="btn-view">
                                            <i class="fas fa-user"></i> <?php echo get_content('celebrities', 'view_profile', 'View Profile'); ?>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Roles Filter -->
        <div class="celebrities-filter">
            <div class="filter-tabs">
                <a href="<?php echo base_url('?page=celebrities'); ?>" class="filter-tab <?php echo empty($selected_role) ? 'active' : ''; ?>">
                    <?php echo get_content('celebrities', 'all_categories', 'All Roles'); ?>
                </a>

                <?php foreach ($roles as $role): ?>
                <a href="<?php echo base_url('?page=celebrities&role=' . urlencode($role)); ?>" class="filter-tab <?php echo $selected_role === $role ? 'active' : ''; ?>">
                    <?php echo get_content('celebrities', strtolower(str_replace(' ', '_', $role)), $role); ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Celebrities Grid -->
        <?php if (!empty($celebrities)): ?>
        <div class="celebrities-grid">
            <?php foreach ($celebrities as $celebrity): ?>
            <div class="celebrity-card">
                <div class="celebrity-image">
                    <img src="<?php echo asset_url('uploads/' . (isset($celebrity['image']) ? $celebrity['image'] : 'default-celebrity.jpg')); ?>" alt="<?php echo htmlspecialchars($celebrity['name']); ?>">
                    <div class="celebrity-category">
                        <?php echo $current_lang === 'ar' ? $celebrity['role_ar'] : $celebrity['role']; ?>
                    </div>
                </div>
                <div class="celebrity-info">
                    <h3 class="celebrity-name"><?php echo $current_lang === 'ar' ? $celebrity['name_ar'] : $celebrity['name']; ?></h3>
                    <div class="celebrity-description">
                        <?php
                        $bio = $current_lang === 'ar' ?
                            (isset($celebrity['bio_ar']) ? $celebrity['bio_ar'] : '') :
                            (isset($celebrity['bio']) ? $celebrity['bio'] : '');
                        echo mb_substr($bio, 0, 100) . (mb_strlen($bio) > 100 ? '...' : '');
                        ?>
                    </div>
                    <div class="celebrity-meta">
                        <div class="celebrity-social">
                            <?php if (!empty($celebrity['instagram'])): ?>
                            <a href="https://instagram.com/<?php echo $celebrity['instagram']; ?>" target="_blank" class="social-icon">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($celebrity['twitter'])): ?>
                            <a href="https://twitter.com/<?php echo $celebrity['twitter']; ?>" target="_blank" class="social-icon">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($celebrity['facebook'])): ?>
                            <a href="https://facebook.com/<?php echo $celebrity['facebook']; ?>" target="_blank" class="social-icon">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($celebrity['youtube'])): ?>
                            <a href="https://youtube.com/<?php echo $celebrity['youtube']; ?>" target="_blank" class="social-icon">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                        <div class="celebrity-actions">
                            <a href="<?php echo base_url('?page=celebrity&id=' . $celebrity['id']); ?>" class="btn-view">
                                <i class="fas fa-user"></i> <?php echo get_content('celebrities', 'view_profile', 'View Profile'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="celebrities-pagination">
            <?php if ($current_page > 1): ?>
            <a href="<?php echo base_url('?page=celebrities' . ($selected_role ? '&role=' . urlencode($selected_role) : '') . '&p=' . ($current_page - 1)); ?>" class="page-item">
                <i class="fas fa-chevron-left"></i>
            </a>
            <?php endif; ?>

            <?php
            // Simplified pagination - show fewer numbers
            $start_page = max(1, $current_page - 1);
            $end_page = min($total_pages, $current_page + 1);

            if ($start_page > 1) {
                echo '<a href="' . base_url('?page=celebrities' . ($selected_role ? '&role=' . urlencode($selected_role) : '') . '&p=1') . '" class="page-item">1</a>';
                if ($start_page > 2) {
                    echo '<span class="page-item">...</span>';
                }
            }

            for ($i = $start_page; $i <= $end_page; $i++) {
                echo '<a href="' . base_url('?page=celebrities' . ($selected_role ? '&role=' . urlencode($selected_role) : '') . '&p=' . $i) . '" class="page-item ' . ($i == $current_page ? 'active' : '') . '">' . $i . '</a>';
            }

            if ($end_page < $total_pages) {
                if ($end_page < $total_pages - 1) {
                    echo '<span class="page-item">...</span>';
                }
                echo '<a href="' . base_url('?page=celebrities' . ($selected_role ? '&role=' . urlencode($selected_role) : '') . '&p=' . $total_pages) . '" class="page-item">' . $total_pages . '</a>';
            }
            ?>

            <?php if ($current_page < $total_pages): ?>
            <a href="<?php echo base_url('?page=celebrities' . ($selected_role ? '&role=' . urlencode($selected_role) : '') . '&p=' . ($current_page + 1)); ?>" class="page-item">
                <i class="fas fa-chevron-right"></i>
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="no-celebrities">
            <i class="fas fa-search"></i>
            <p><?php echo get_content('celebrities', 'no_celebrities', 'No celebrities found in this category'); ?></p>
        </div>
        <?php endif; ?>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Swiper for featured celebrities
    new Swiper('.featured-swiper', {
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        breakpoints: {
            640: {
                slidesPerView: 2,
                spaceBetween: 20,
            },
            992: {
                slidesPerView: 3,
                spaceBetween: 30,
            }
        },
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        }
    });

    // Add animations to celebrity cards
    const celebrityCards = document.querySelectorAll('.celebrity-card');

    celebrityCards.forEach((card, index) => {
        // Stagger animation delay
        const delay = index * 0.1;

        // Apply animation with GSAP
        gsap.from(card, {
            y: 50,
            duration: 0.8,
            delay: delay,
            ease: 'power2.out'
        });
    });
});
</script>
