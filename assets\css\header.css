/* Header Styles */
.language-switcher {
    display: flex;
    align-items: center;
    margin-right: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    padding: 5px 10px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.language-switcher a {
    color: var(--color-white);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    padding: 2px 8px;
    border-radius: 15px;
}

.language-switcher a:hover {
    color: var(--color-gold);
    transform: translateY(-2px);
}

.language-switcher a.active {
    color: var(--color-black);
    font-weight: bold;
    background: var(--color-gold);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.language-switcher .separator {
    margin: 0 2px;
    color: var(--color-light);
}

/* Mobile Header Styles */
@media (max-width: 768px) {
    .header-inner {
        padding: 10px 0;
    }

    .language-switcher {
        margin-right: 10px;
        padding: 3px 8px;
    }

    .language-switcher a {
        font-size: 0.8rem;
        padding: 2px 6px;
    }

    .auth-buttons {
        display: flex;
    }

    .btn-login, .btn-register {
        padding: 5px 10px;
        font-size: 0.9rem;
    }

    .user-menu {
        font-size: 0.9rem;
    }

    .user-toggle i {
        font-size: 1.2rem;
    }

    /* Fix for RTL on mobile */
    html[dir="rtl"] .header-inner {
        flex-direction: row-reverse;
    }

    html[dir="rtl"] .language-switcher {
        margin-right: 0;
        margin-left: 10px;
    }

    html[dir="rtl"] .nav-menu {
        right: -100%;
        left: auto;
    }

    html[dir="rtl"] .nav-menu.active {
        right: 0;
        left: auto;
    }
}
