/* Compare Page Styles */
.compare-section {
    padding: 60px 0;
    background: linear-gradient(to bottom, #000000, #111111);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.compare-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(255, 215, 0, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(255, 215, 0, 0.05), transparent 70%);
    pointer-events: none;
}

.compare-header {
    text-align: center;
    margin-bottom: 40px;
}

.compare-header .section-title {
    color: var(--color-gold);
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.compare-header .section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, transparent, var(--color-gold), transparent);
}

.compare-breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
    color: var(--color-light);
}

.compare-breadcrumb a {
    color: var(--color-light);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.compare-breadcrumb a:hover {
    color: var(--color-gold);
}

.compare-breadcrumb .separator {
    margin: 0 10px;
    color: var(--color-gold);
    font-size: 0.8rem;
}

.alert {
    max-width: 600px;
    margin: 20px auto;
    padding: 15px 20px;
    border-radius: var(--border-radius-md);
    text-align: center;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.5);
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.5);
    color: #dc3545;
}

.compare-selectors {
    max-width: 800px;
    margin: 0 auto 40px;
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    padding: 30px;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.compare-form {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: flex-end;
}

.compare-form .form-group {
    flex: 1;
    min-width: 200px;
}

.compare-form label {
    display: block;
    margin-bottom: 10px;
    color: var(--color-gold);
    font-weight: 500;
}

.compare-form .form-control {
    width: 100%;
    padding: 12px 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 215, 0, 0.2);
    border-radius: var(--border-radius-sm);
    color: var(--color-white);
    font-size: 1rem;
    transition: all var(--transition-normal);
}

.compare-form .form-control:focus {
    border-color: var(--color-gold);
    box-shadow: 0 0 0 2px rgba(255, 215, 0, 0.2);
    outline: none;
}

.compare-form .form-actions {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-top: 10px;
}

.compare-form .btn-primary {
    background: linear-gradient(145deg, var(--color-gold), var(--color-gold-dark));
    color: var(--color-black);
    border: none;
    padding: 12px 25px;
    border-radius: var(--border-radius-md);
    font-weight: bold;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.compare-form .btn-primary i {
    margin-right: 8px;
}

.compare-form .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.compare-content {
    max-width: 1000px;
    margin: 0 auto;
}

.compare-progress {
    margin-bottom: 40px;
}

.progress-bar {
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    display: flex;
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-fill {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    font-weight: bold;
    transition: width 1s ease-in-out;
}

.progress-fill.nominee1 {
    background: linear-gradient(145deg, var(--color-gold), var(--color-gold-dark));
    border-right: 1px solid rgba(0, 0, 0, 0.2);
}

.progress-fill.nominee2 {
    background: linear-gradient(145deg, #e6c200, #b39700);
}

.progress-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    color: var(--color-light);
    font-size: 0.9rem;
}

.compare-cards {
    display: flex;
    align-items: center;
    gap: 20px;
}

.compare-card {
    flex: 1;
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 215, 0, 0.1);
    transition: transform var(--transition-normal);
}

.compare-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 215, 0, 0.3);
}

.nominee-image {
    height: 300px;
    position: relative;
    overflow: hidden;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.compare-card:hover .nominee-image img {
    transform: scale(1.05);
}

.nominee-image::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    pointer-events: none;
}

.nominee-votes {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    padding: 8px 15px;
    border-radius: 20px;
    color: var(--color-white);
    display: flex;
    align-items: center;
    z-index: 1;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.nominee-votes i {
    color: var(--color-gold);
    margin-right: 5px;
}

.votes-count {
    font-weight: bold;
    margin-right: 5px;
}

.votes-label {
    font-size: 0.8rem;
    color: var(--color-light);
}

.nominee-info {
    padding: 20px;
    text-align: center;
}

.nominee-info h3 {
    color: var(--color-gold);
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.nominee-description {
    color: var(--color-light);
    margin-bottom: 20px;
    max-height: 100px;
    overflow-y: auto;
    font-size: 0.9rem;
    line-height: 1.6;
}

.voted-badge {
    display: inline-block;
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.voted-badge i {
    margin-right: 5px;
}

.btn-vote, .btn-login {
    background: linear-gradient(145deg, var(--color-gold), var(--color-gold-dark));
    color: var(--color-black);
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius-md);
    font-weight: bold;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn-vote i, .btn-login i {
    margin-right: 8px;
}

.btn-vote:hover, .btn-login:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-login {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    color: var(--color-white);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.compare-vs {
    width: 60px;
    height: 60px;
    background: linear-gradient(145deg, var(--color-gold), var(--color-gold-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.compare-placeholder {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.placeholder-icon {
    font-size: 4rem;
    color: var(--color-gold);
    margin-bottom: 20px;
    opacity: 0.7;
}

.compare-placeholder h2 {
    color: var(--color-white);
    margin-bottom: 15px;
}

.compare-placeholder p {
    color: var(--color-light);
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.debug-info {
    margin-top: 30px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 0, 0, 0.2);
    text-align: left;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.debug-info h3 {
    color: #ff6b6b;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.debug-info ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.debug-info li {
    color: #ddd;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-family: monospace;
    font-size: 0.9rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .compare-cards {
        flex-direction: column;
    }

    .compare-vs {
        margin: -30px 0;
    }

    .nominee-image {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .compare-section {
        padding: 40px 0;
    }

    .compare-header .section-title {
        font-size: 2rem;
    }

    .compare-selectors {
        padding: 20px;
    }

    .compare-form {
        flex-direction: column;
    }

    .compare-form .form-group {
        width: 100%;
    }

    .nominee-image {
        height: 200px;
    }
}
