# Enable rewriting
RewriteEngine On

# Make sure assets are accessible
RewriteRule ^assets/ - [L]
RewriteRule ^favicon\.ico - [L]

# Set default charset
AddDefaultCharset UTF-8

# Protect against XSS attacks
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    # Removed Content-Security-Policy temporarily to debug CSS/JS issues
    # Header set Content-Security-Policy "default-src 'self' https: data: 'unsafe-inline' 'unsafe-eval';"
</IfModule>

# Enable GZIP compression - محسن
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
    AddOutputFilterByType DEFLATE application/rss+xml application/atom+xml image/svg+xml
    AddOutputFilterByType DEFLATE application/font-woff application/font-woff2 font/woff font/woff2

    # تحسين الضغط للملفات الكبيرة
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|webp)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:exe|t?gz|zip|bz2|sit|rar)$ no-gzip dont-vary
    SetEnvIfNoCase Request_URI \
        \.(?:pdf|mov|avi|mp3|mp4|rm)$ no-gzip dont-vary
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType video/mp4 "access plus 1 year"
    ExpiresByType video/webm "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-font-ttf "access plus 1 year"
    ExpiresByType application/x-font-woff "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType audio/mp3 "access plus 1 month"
    ExpiresByType audio/mpeg "access plus 1 month"
    ExpiresByType audio/wav "access plus 1 month"
</IfModule>

# Cache Control Headers محسنة
<IfModule mod_headers.c>
    # الصور والملفات الثابتة - سنة كاملة
    <FilesMatch "\.(ico|pdf|flv|jpg|jpeg|png|gif|webp|svg|woff|woff2|ttf|otf|eot)$">
        Header set Cache-Control "max-age=31536000, public, immutable"
        Header set Vary "Accept-Encoding"
    </FilesMatch>

    # الفيديوهات والصوتيات - شهر
    <FilesMatch "\.(mp4|webm|ogg|mp3|wav|mpeg|avi|mov)$">
        Header set Cache-Control "max-age=2592000, public"
        Header set Vary "Accept-Encoding"
    </FilesMatch>

    # CSS و JavaScript - أسبوع
    <FilesMatch "\.(css|js)$">
        Header set Cache-Control "max-age=604800, public"
        Header set Vary "Accept-Encoding"
    </FilesMatch>

    # HTML - ساعة واحدة
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "max-age=3600, public, must-revalidate"
    </FilesMatch>

    # إزالة ETags لتحسين الكاش
    Header unset ETag
    FileETag None

    # تحسين الأمان
    Header always set X-Content-Type-Options nosniff
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Protect sensitive files
<FilesMatch "^\.(?!well-known\/).*|(?:config\.php|\.env|composer\.json|composer\.lock)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Disable directory browsing
Options -Indexes

# Protect wp-config.php
<Files wp-config.php>
    Order allow,deny
    Deny from all
</Files>

# Protect .htaccess
<Files .htaccess>
    Order allow,deny
    Deny from all
</Files>

# Allow access to includes/assets but protect PHP files

# Allow access to config/assets but protect PHP files
<IfModule mod_rewrite.c>
    RewriteCond %{REQUEST_URI} ^/config/.*\.php$ [NC]
    RewriteRule ^ - [F,L]
</IfModule>

# Prevent access to PHP files in the uploads directory
<IfModule mod_rewrite.c>
    RewriteRule ^uploads/.*\.(?:php[1-7]?|pht|phtml?|phps)$ - [NC,F]
</IfModule>

# Prevent script injections
Options +FollowSymLinks
RewriteCond %{QUERY_STRING} (<|%3C).*script.*(>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|[|%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|[|%[0-9A-Z]{0,2})
RewriteRule ^(.*)$ index.php [F,L]

# PHP settings
<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_value error_log /path/to/error.log
    php_value upload_max_filesize 512M
    php_value post_max_size 512M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value memory_limit 512M
</IfModule>

# Custom error pages
ErrorDocument 404 /index.php?page=404
ErrorDocument 403 /index.php?page=403
ErrorDocument 500 /index.php?page=500
