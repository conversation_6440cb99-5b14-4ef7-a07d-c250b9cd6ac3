.nominee-card {
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    border-radius: var(--premium-border-radius);
    overflow: hidden;
    transition: var(--premium-transition);
    border: 1px solid #333;
    height: 100%;
    position: relative;
    box-shadow: var(--premium-shadow);
    display: flex;
    flex-direction: column;
}

.nominee-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, transparent, var(--premium-gold), transparent);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.nominee-card:hover {
    transform: translateY(-10px);
    border-color: var(--premium-gold);
    box-shadow: var(--premium-gold-shadow);
}

.nominee-card:hover::before {
    opacity: 1;
}

.nominee-link {
    display: flex;
    flex-direction: column;
    text-decoration: none;
    height: 100%;
    color: white;
    flex: 1;
}

.nominee-image {
    height: 220px;
    overflow: hidden;
    position: relative;
}

.nominee-image::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
    pointer-events: none;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

.nominee-card:hover .nominee-image::after {
    opacity: 0.4;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.nominee-card:hover .nominee-image img {
    transform: scale(1.1);
}

.nominee-rank {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(145deg, var(--premium-gold), var(--premium-gold-dark));
    color: var(--premium-black);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
    z-index: 2;
}

.nominee-info {
    padding: 15px;
    text-align: center;
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.nominee-info h3 {
    color: var(--premium-gold);
    font-size: 1rem;
    margin-bottom: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    display: inline-block;
}

.nominee-info h3::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--premium-gold);
    transition: width 0.3s ease;
}

.nominee-card:hover .nominee-info h3::after {
    width: 100%;
}

.nominee-votes {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    background: rgba(0, 0, 0, 0.2);
    padding: 5px 10px;
    border-radius: 20px;
    display: inline-flex;
}

.nominee-votes i {
    color: var(--premium-gold);
    font-size: 0.9rem;
    margin-right: 5px;
}

.votes-count {
    color: white;
    font-size: 1rem;
    font-weight: bold;
    margin-right: 5px;
}

.votes-label {
    color: #aaa;
    font-size: 0.7rem;
}

.vote-status {
    margin: 5px 0;
}

.voted-badge {
    display: inline-block;
    background: linear-gradient(145deg, rgba(40, 167, 69, 0.2), rgba(40, 167, 69, 0.1));
    color: #28a745;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 0.7rem;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.voted-badge i {
    margin-right: 3px;
}

.nominee-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: auto;
    padding-top: 10px;
}

.quick-vote-form {
    width: 100%;
}

.btn-vote-small {
    background: linear-gradient(145deg, var(--premium-gold), var(--premium-gold-dark));
    color: var(--premium-black);
    border: none;
    padding: 8px 15px;
    font-size: 0.9rem;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    font-weight: bold;
}

.btn-vote-small i {
    margin-right: 5px;
}

.btn-vote-small:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.btn-vote-small.voted {
    background: linear-gradient(145deg, #28a745, #218838);
    cursor: default;
}

.btn-vote-small.voted:hover {
    transform: none;
}

.btn-login-small {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 8px 15px;
    font-size: 0.9rem;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    text-decoration: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-login-small i {
    margin-right: 5px;
}

.btn-login-small:hover {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.btn-compare-small {
    background: linear-gradient(145deg, rgba(70, 130, 180, 0.3), rgba(70, 130, 180, 0.2));
    color: white;
    border: 1px solid rgba(70, 130, 180, 0.3);
    padding: 8px 15px;
    font-size: 0.9rem;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    text-decoration: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-compare-small i {
    margin-right: 5px;
}

.btn-compare-small:hover {
    background: linear-gradient(145deg, rgba(70, 130, 180, 0.4), rgba(70, 130, 180, 0.3));
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .nominee-image {
        height: 200px;
    }
}

@media (max-width: 768px) {
    .main-category-item {
        width: 90px;
    }

    .category-icon {
        width: 90px;
        height: 90px;
    }

    .subcategory-item {
        width: 80px;
        height: 80px;
    }

    .subcategory-icon {
        width: 80px;
        height: 80px;
    }

    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
        gap: 15px;
    }

    .nominee-image {
        height: 180px;
    }
}

@media (max-width: 576px) {
    .main-category-item {
        width: 80px;
    }

    .category-icon {
        width: 80px;
        height: 80px;
    }

    .subcategory-item {
        width: 70px;
        height: 70px;
    }

    .subcategory-icon {
        width: 70px;
        height: 70px;
    }

    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
    }

    .nominee-image {
        height: 160px;
    }

    .nominee-info {
        padding: 10px;
    }

    .btn-vote-small, .btn-login-small {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}
