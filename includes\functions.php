<?php
/**
 * Common functions for the Social Media Festival website
 */

// Language switcher function is defined in config/i18n.php

/**
 * Check if user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function is_logged_in() {
    $is_logged_in = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    error_log("is_logged_in() called. Result: " . ($is_logged_in ? 'true' : 'false'));
    error_log("Session data: " . print_r($_SESSION, true));
    return $is_logged_in;
}

/**
 * Check if user is admin
 *
 * @return bool True if user is admin, false otherwise
 */
function is_admin() {
    return is_logged_in() && isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

/**
 * Get user by ID
 *
 * @param int $user_id User ID
 * @return array|null User data or null if not found
 */
function get_user($user_id) {
    global $conn;

    $user_id = (int) $user_id;
    $sql = "SELECT id, username, email, full_name, phone, is_admin, created_at FROM users WHERE id = $user_id LIMIT 1";
    $result = $conn->query($sql);

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Get nominee by ID
 *
 * @param int $nominee_id Nominee ID
 * @return array|null Nominee data or null if not found
 */
function get_nominee($nominee_id) {
    global $conn;

    $nominee_id = (int) $nominee_id;

    // Use prepared statement for better security
    $sql = "SELECT * FROM nominees WHERE id = ? LIMIT 1";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $nominee_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Get all nominees
 *
 * @param string $category Optional category filter
 * @return array Array of nominees
 */
function get_nominees($category = '') {
    global $conn;

    $sql = "SELECT * FROM nominees";

    if (!empty($category)) {
        $category = $conn->real_escape_string($category);
        $sql .= " WHERE category = '$category'";
    }

    $sql .= " ORDER BY votes DESC, name ASC";

    $result = $conn->query($sql);
    $nominees = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $nominees[] = $row;
        }
    }

    return $nominees;
}

/**
 * Get all sponsors
 *
 * @param string $type Optional sponsor type filter (platinum, gold, silver)
 * @return array Array of sponsors
 */
function get_sponsors($type = '') {
    global $conn;

    $sql = "SELECT * FROM sponsors";

    if (!empty($type)) {
        $type = $conn->real_escape_string($type);
        $sql .= " WHERE type = '$type'";
    }

    $sql .= " ORDER BY FIELD(type, 'platinum', 'gold', 'silver'), name ASC";

    $result = $conn->query($sql);
    $sponsors = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $sponsors[] = $row;
        }
    }

    return $sponsors;
}

/**
 * Get all celebrities
 *
 * @return array Array of celebrities
 */
function get_celebrities() {
    global $conn;

    $sql = "SELECT * FROM celebrities ORDER BY name ASC";
    $result = $conn->query($sql);
    $celebrities = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $celebrities[] = $row;
        }
    }

    return $celebrities;
}

/**
 * Get all stories
 *
 * @return array Array of stories
 */
function get_stories() {
    global $conn;

    $sql = "SELECT * FROM stories ORDER BY created_at DESC";
    $result = $conn->query($sql);
    $stories = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $stories[] = $row;
        }
    }

    return $stories;
}

/**
 * Check if user has voted for a nominee
 *
 * @param int $user_id User ID
 * @param int $nominee_id Nominee ID
 * @return bool True if user has voted for nominee, false otherwise
 */
function has_voted($user_id, $nominee_id) {
    global $conn;

    $user_id = (int) $user_id;
    $nominee_id = (int) $nominee_id;

    $sql = "SELECT id FROM votes WHERE user_id = $user_id AND nominee_id = $nominee_id LIMIT 1";
    $result = $conn->query($sql);

    return ($result && $result->num_rows > 0);
}

/**
 * Vote for a nominee
 *
 * @param int $user_id User ID
 * @param int $nominee_id Nominee ID
 * @return bool True if vote was successful, false otherwise
 */
function vote_for_nominee($user_id, $nominee_id) {
    global $conn;

    $user_id = (int) $user_id;
    $nominee_id = (int) $nominee_id;

    // Check if user has already voted for this nominee
    if (has_voted($user_id, $nominee_id)) {
        return false;
    }

    // Start transaction
    $conn->begin_transaction();

    try {
        // Add vote record
        $sql = "INSERT INTO votes (user_id, nominee_id) VALUES ($user_id, $nominee_id)";
        $conn->query($sql);

        // Update nominee vote count
        $sql = "UPDATE nominees SET votes = votes + 1 WHERE id = $nominee_id";
        $conn->query($sql);

        // Commit transaction
        $conn->commit();

        return true;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        return false;
    }
}

/**
 * Format date
 *
 * @param string $date Date string
 * @param string $format Date format
 * @return string Formatted date
 */
function format_date($date, $format = 'F j, Y') {
    return date($format, strtotime($date));
}

/**
 * Get nominee name based on language
 *
 * @param array $nominee Nominee data
 * @return string Nominee name
 */
function get_nominee_name($nominee) {
    global $current_lang;

    return $current_lang === 'ar' ? $nominee['name_ar'] : $nominee['name'];
}

/**
 * Get sponsor name based on language
 *
 * @param array $sponsor Sponsor data
 * @return string Sponsor name
 */
function get_sponsor_name($sponsor) {
    global $current_lang;

    return $current_lang === 'ar' ? $sponsor['name_ar'] : $sponsor['name'];
}

/**
 * Get celebrity name based on language
 *
 * @param array $celebrity Celebrity data
 * @return string Celebrity name
 */
function get_celebrity_name($celebrity) {
    global $current_lang;

    return $current_lang === 'ar' ? $celebrity['name_ar'] : $celebrity['name'];
}

/**
 * Get all reels
 *
 * @return array Array of reels
 */
function get_reels() {
    global $conn;

    $sql = "SELECT * FROM reels ORDER BY created_at DESC";
    $result = $conn->query($sql);
    $reels = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $reels[] = $row;
        }
    }

    return $reels;
}

/**
 * Get reel title based on language
 *
 * @param array $reel Reel data
 * @return string Reel title
 */
function get_reel_title($reel) {
    global $current_lang;

    return $current_lang === 'ar' ? $reel['title_ar'] : $reel['title'];
}

/**
 * Get featured celebrities
 *
 * @param int $limit Optional limit of celebrities to return (0 for all)
 * @return array Array of featured celebrities
 */
function get_featured_celebrities($limit = 0) {
    global $conn;

    $sql = "SELECT * FROM celebrities WHERE featured = 1 ORDER BY id ASC";

    if ($limit > 0) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $result = $conn->query($sql);
    $celebrities = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $celebrities[] = $row;
        }
    }

    return $celebrities;
}

/**
 * Get celebrity role based on current language
 *
 * @param array $celebrity Celebrity data
 * @return string Celebrity role
 */
function get_celebrity_role($celebrity) {
    global $current_lang;

    // Check if role exists in the celebrity data
    if (!isset($celebrity['role']) || !isset($celebrity['role_ar'])) {
        return '';
    }

    return $current_lang === 'ar' ? $celebrity['role_ar'] : $celebrity['role'];
}

/**
 * Get all categories
 *
 * @return array Array of categories
 */
function get_categories() {
    global $conn;

    $sql = "SELECT * FROM categories ORDER BY display_order ASC";
    $result = $conn->query($sql);
    $categories = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $categories[] = $row;
        }
    }

    return $categories;
}

/**
 * Get category name based on current language
 *
 * @param array $category Category data
 * @return string Category name
 */
function get_category_name($category) {
    global $current_lang;

    return $current_lang === 'ar' ? $category['name_ar'] : $category['name'];
}

/**
 * Get CEO information
 *
 * @return array|false CEO data or false if not found
 */
function get_ceo_info() {
    global $conn;

    $sql = "SELECT * FROM ceo LIMIT 1";
    $result = $conn->query($sql);

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Get team members
 *
 * @param int $limit Optional limit of team members to return (0 for all)
 * @return array Array of team members
 */
function get_team_members($limit = 0) {
    global $conn;

    $sql = "SELECT * FROM team_members ORDER BY display_order ASC";

    if ($limit > 0) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $result = $conn->query($sql);
    $members = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $members[] = $row;
        }
    }

    return $members;
}

/**
 * Get invitations
 *
 * @param int $limit Optional limit of invitations to return (0 for all)
 * @return array Array of invitations
 */
function get_invitations($limit = 0) {
    global $conn;

    $sql = "SELECT * FROM invitations ORDER BY display_order ASC";

    if ($limit > 0) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $result = $conn->query($sql);
    $invitations = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $invitations[] = $row;
        }
    }

    return $invitations;
}

/**
 * Get all voting categories
 *
 * @return array Array of voting categories
 */
function get_main_categories() {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_categories'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return empty array
        return [];
    }

    $sql = "SELECT * FROM voting_categories ORDER BY display_order ASC";
    $result = $conn->query($sql);
    $categories = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $categories[] = $row;
        }
    }

    return $categories;
}

/**
 * Get voting category by ID
 *
 * @param int $id Category ID
 * @return array|false Category data or false if not found
 */
function get_main_category($id) {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_categories'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return false
        return false;
    }

    $sql = "SELECT * FROM voting_categories WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Get voting category name based on current language
 *
 * @param array $category Category data
 * @return string Category name
 */
function get_main_category_name($category) {
    global $current_lang;

    return $current_lang === 'ar' ? $category['name_ar'] : $category['name'];
}

/**
 * Get subcategories by category ID
 *
 * @param int $category_id Category ID
 * @return array Array of subcategories
 */
function get_subcategories($category_id) {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_subcategories'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return empty array
        return [];
    }

    $sql = "SELECT * FROM voting_subcategories WHERE category_id = ? ORDER BY display_order ASC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $category_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $subcategories = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $subcategories[] = $row;
        }
    }

    return $subcategories;
}

/**
 * Get subcategory by ID
 *
 * @param int $id Subcategory ID
 * @return array|false Subcategory data or false if not found
 */
function get_subcategory($id) {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_subcategories'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return false
        return false;
    }

    $sql = "SELECT * FROM voting_subcategories WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * Get subcategory name based on current language
 *
 * @param array $subcategory Subcategory data
 * @return string Subcategory name
 */
function get_subcategory_name($subcategory) {
    global $current_lang;

    return $current_lang === 'ar' ? $subcategory['name_ar'] : $subcategory['name'];
}

/**
 * Get nominees by subcategory ID
 *
 * @param int $subcategory_id Subcategory ID
 * @return array Array of nominees
 */
function get_nominees_by_subcategory($subcategory_id) {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_nominees'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return empty array
        return [];
    }

    $sql = "SELECT * FROM voting_nominees WHERE subcategory_id = ? ORDER BY name ASC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $subcategory_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $nominees = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $nominees[] = $row;
        }
    }

    return $nominees;
}

/**
 * Get votes count for a nominee
 *
 * @param int $nominee_id Nominee ID
 * @return int Number of votes
 */
function get_nominee_votes_count($nominee_id) {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_votes'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return 0
        return 0;
    }

    $sql = "SELECT COUNT(*) as count FROM voting_votes WHERE nominee_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $nominee_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return (int)$row['count'];
    }

    return 0;
}

/**
 * Add a vote for a nominee
 *
 * @param int $user_id User ID
 * @param int $nominee_id Nominee ID
 * @return bool True if vote was added successfully, false otherwise
 */
function add_vote($user_id, $nominee_id) {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_votes'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return false
        return false;
    }

    // Start transaction
    $conn->begin_transaction();

    try {
        // Insert vote
        $sql = "INSERT INTO voting_votes (user_id, nominee_id, created_at) VALUES (?, ?, NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $user_id, $nominee_id);
        $result = $stmt->execute();

        if ($result) {
            // Update nominee votes count
            $update_sql = "UPDATE voting_nominees SET votes_count = votes_count + 1 WHERE id = ?";
            $update_stmt = $conn->prepare($update_sql);
            $update_stmt->bind_param("i", $nominee_id);
            $update_stmt->execute();
        }

        // Commit transaction
        $conn->commit();
        return $result;
    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollback();
        return false;
    }
}

/**
 * Get current URL
 *
 * @return string Current URL
 */
function current_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = strtok($_SERVER['REQUEST_URI'], '?');

    return $protocol . '://' . $host . $uri;
}

/**
 * Check if user has voted for a nominee
 *
 * @param int $user_id User ID
 * @param int $nominee_id Nominee ID
 * @return bool True if user has voted for the nominee, false otherwise
 */
function has_voted_for_nominee($user_id, $nominee_id) {
    global $conn;

    if (!$user_id) {
        return false;
    }

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_votes'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return false
        return false;
    }

    $sql = "SELECT COUNT(*) as count FROM voting_votes WHERE user_id = ? AND nominee_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ii", $user_id, $nominee_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return (int)$row['count'] > 0;
    }

    return false;
}

/**
 * Get featured influencers
 */
function get_featured_influencers($limit = 0) {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'influencers'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return empty array
        return [];
    }

    $sql = "SELECT * FROM influencers WHERE status = 'active' AND featured = 1 ORDER BY display_order ASC, influence_score DESC, created_at DESC";

    if ($limit > 0) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $result = $conn->query($sql);

    $influencers = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $influencers[] = $row;
        }
    }

    return $influencers;
}

/**
 * Get all influencers
 */
function get_all_influencers($limit = 0) {
    global $conn;

    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'influencers'");
    if (!$check_table || $check_table->num_rows == 0) {
        // Table doesn't exist, return empty array
        return [];
    }

    $sql = "SELECT * FROM influencers WHERE status = 'active' ORDER BY influence_score DESC, followers_count DESC, created_at DESC";

    if ($limit > 0) {
        $sql .= " LIMIT " . (int)$limit;
    }

    $result = $conn->query($sql);

    $influencers = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $influencers[] = $row;
        }
    }

    return $influencers;
}

/**
 * Get influencer name based on current language
 */
function get_influencer_name($influencer) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($influencer['name_ar'])) {
        return $influencer['name_ar'];
    }

    return $influencer['name'];
}

/**
 * Get influencer title based on current language
 */
function get_influencer_title($influencer) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($influencer['title_ar'])) {
        return $influencer['title_ar'];
    }

    return $influencer['title'];
}

/**
 * Get influencer bio based on current language
 */
function get_influencer_bio($influencer) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($influencer['bio_ar'])) {
        return $influencer['bio_ar'];
    }

    return $influencer['bio'];
}

/**
 * Format followers count for display
 */
function format_followers_count($count) {
    // Convert to integer to handle null/string values
    $count = (int)$count;

    // Handle zero or negative values
    if ($count <= 0) {
        return '0';
    }

    if ($count >= 1000000) {
        $formatted = $count / 1000000;
        // Avoid showing 0.0M for small numbers
        if ($formatted < 0.1) {
            return round($count / 1000, 1) . 'K';
        }
        return round($formatted, 1) . 'M';
    } elseif ($count >= 1000) {
        return round($count / 1000, 1) . 'K';
    }

    return number_format($count);
}

/**
 * Get platform name in Arabic
 */
function get_platform_name_ar($platform) {
    $platform_names = [
        'Instagram' => 'إنستغرام',
        'YouTube' => 'يوتيوب',
        'TikTok' => 'تيك توك',
        'Twitter' => 'تويتر',
        'Facebook' => 'فيسبوك',
        'LinkedIn' => 'لينكد إن'
    ];

    return $platform_names[$platform] ?? $platform;
}

/**
 * Get followers count text in Arabic
 */
function get_followers_text_ar($count) {
    if ($count == 1) {
        return 'متابع واحد';
    } elseif ($count == 2) {
        return 'متابعان';
    } elseif ($count <= 10) {
        return 'متابعين';
    } else {
        return 'متابع';
    }
}

/**
 * Get Arabic text for common terms
 */
function get_arabic_text($key) {
    $texts = [
        'followers' => 'متابع',
        'influence_score' => 'درجة التأثير',
        'follow' => 'متابعة',
        'featured' => 'مميز',
        'all_platforms' => 'جميع المنصات',
        'search_placeholder' => 'البحث عن بلوجر...',
        'sort_by_influence' => 'الأعلى تأثيراً',
        'sort_by_followers' => 'الأكثر متابعة',
        'sort_by_name' => 'ترتيب أبجدي',
        'no_results' => 'لا توجد نتائج',
        'no_results_desc' => 'لم يتم العثور على بلوجرز يطابقون معايير البحث',
        'view_all' => 'عرض جميع البلوجرز',
        'clear_filters' => 'مسح الفلاتر',
        'search' => 'بحث',
        'results' => 'النتائج'
    ];

    return $texts[$key] ?? $key;
}

/**
 * Get featured podcasts
 */
function get_featured_podcasts($limit = 6) {
    global $conn;

    $sql = "SELECT id, title, title_ar, description, description_ar, audio_file, thumbnail, featured, status, created_at FROM podcasts WHERE status = 'active' ORDER BY featured DESC, created_at DESC";
    if ($limit > 0) {
        $sql .= " LIMIT " . intval($limit);
    }

    $result = $conn->query($sql);
    $podcasts = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $podcasts[] = $row;
        }
    }

    return $podcasts;
}

/**
 * Get all podcasts
 */
function get_all_podcasts($limit = 0) {
    global $conn;

    $sql = "SELECT * FROM podcasts WHERE status = 'active' ORDER BY published_date DESC";
    if ($limit > 0) {
        $sql .= " LIMIT " . intval($limit);
    }

    $result = $conn->query($sql);
    $podcasts = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $podcasts[] = $row;
        }
    }

    return $podcasts;
}

/**
 * Get podcast title based on current language
 */
function get_podcast_title($podcast) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($podcast['title_ar'])) {
        return $podcast['title_ar'];
    }

    return $podcast['title'];
}

/**
 * Get podcast description based on current language
 */
function get_podcast_description($podcast) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($podcast['description_ar'])) {
        return $podcast['description_ar'];
    }

    return $podcast['description'];
}

/**
 * Get podcast host name based on current language
 */
function get_podcast_host($podcast) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($podcast['host_name_ar'])) {
        return $podcast['host_name_ar'];
    }

    return $podcast['host_name'];
}

/**
 * Get podcast guest name based on current language
 */
function get_podcast_guest($podcast) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($podcast['guest_name_ar'])) {
        return $podcast['guest_name_ar'];
    }

    return $podcast['guest_name'];
}

/**
 * Get podcast category based on current language
 */
function get_podcast_category($podcast) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($podcast['category_ar'])) {
        return $podcast['category_ar'];
    }

    return $podcast['category'];
}

/**
 * Get media coverage by type
 */
function get_media_coverage($type = null) {
    global $conn;

    $sql = "SELECT * FROM media_coverage WHERE status = 'active'";
    if ($type) {
        $sql .= " AND type = '" . $conn->real_escape_string($type) . "'";
    }
    $sql .= " ORDER BY sort_order ASC, id ASC";

    $result = $conn->query($sql);

    $coverage = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $coverage[] = $row;
        }
    }

    return $coverage;
}

/**
 * Get coverage title based on current language
 */
function get_coverage_title($coverage) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($coverage['title_ar'])) {
        return $coverage['title_ar'];
    }

    return $coverage['title'];
}

/**
 * Get coverage description based on current language
 */
function get_coverage_description($coverage) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($coverage['description_ar'])) {
        return $coverage['description_ar'];
    }

    return $coverage['description'];
}

/**
 * Get gallery images
 */
function get_gallery_images($category = null, $featured_only = false, $limit = null) {
    global $conn;

    $sql = "SELECT * FROM gallery WHERE status = 'active'";

    if ($category) {
        $sql .= " AND category = '" . $conn->real_escape_string($category) . "'";
    }

    if ($featured_only) {
        $sql .= " AND featured = 1";
    }

    $sql .= " ORDER BY sort_order ASC, id DESC";

    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }

    $result = $conn->query($sql);

    $images = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $images[] = $row;
        }
    }

    return $images;
}

/**
 * Get gallery image title based on current language
 */
function get_gallery_title($image) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($image['title_ar'])) {
        return $image['title_ar'];
    }

    return $image['title'];
}

/**
 * Get gallery image description based on current language
 */
function get_gallery_description($image) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($image['description_ar'])) {
        return $image['description_ar'];
    }

    return $image['description'];
}

/**
 * Get gallery category name based on current language
 */
function get_gallery_category($image) {
    global $current_lang;

    if ($current_lang === 'ar' && !empty($image['category_ar'])) {
        return $image['category_ar'];
    }

    return $image['category'];
}

/**
 * Get gallery categories
 */
function get_gallery_categories() {
    global $conn;

    $sql = "SELECT DISTINCT category, category_ar FROM gallery WHERE status = 'active' ORDER BY category ASC";
    $result = $conn->query($sql);

    $categories = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $categories[] = $row;
        }
    }

    return $categories;
}



