<?php
/**
 * Social Login Handler
 */

// Include config and functions
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get provider from URL
$provider = isset($_GET['provider']) ? $_GET['provider'] : '';

// Redirect URL
$redirect_url = base_url('includes/social-login-callback.php?provider=' . $provider);

// Handle different providers
switch ($provider) {
    case 'google':
        // Google OAuth configuration
        $client_id = '*************-852kdts3okvrbpulkgkomv5cccd40aol.apps.googleusercontent.com'; // Replace with your Google Client ID
        $client_secret = '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'; // Replace with your Google Client Secret

        // Create Google OAuth URL
        $auth_url = 'https://accounts.google.com/o/oauth2/v2/auth';
        $params = [
            'client_id' => $client_id,
            'redirect_uri' => $redirect_url,
            'response_type' => 'code',
            'scope' => 'email profile',
            'access_type' => 'online',
            'prompt' => 'select_account'
        ];

        // Store state in session
        $_SESSION['oauth_state'] = bin2hex(random_bytes(16));
        $params['state'] = $_SESSION['oauth_state'];

        // Redirect to Google OAuth
        $auth_url .= '?' . http_build_query($params);
        header('Location: ' . $auth_url);
        exit;

    case 'facebook':
        // Facebook OAuth configuration
        $app_id = '****************'; // Replace with your Facebook App ID
        $app_secret = '********************************'; // Replace with your Facebook App Secret

        // Create Facebook OAuth URL
        $auth_url = 'https://www.facebook.com/v12.0/dialog/oauth';
        $params = [
            'client_id' => $app_id,
            'redirect_uri' => $redirect_url,
            'response_type' => 'code',
            'scope' => 'email,public_profile'
        ];

        // Store state in session
        $_SESSION['oauth_state'] = bin2hex(random_bytes(16));
        $params['state'] = $_SESSION['oauth_state'];

        // Redirect to Facebook OAuth
        $auth_url .= '?' . http_build_query($params);
        header('Location: ' . $auth_url);
        exit;

    default:
        // Invalid provider
        $_SESSION['error'] = 'Invalid social login provider';
        header('Location: ' . base_url('?page=login'));
        exit;
}
