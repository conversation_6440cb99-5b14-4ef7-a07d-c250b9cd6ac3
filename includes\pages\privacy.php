<?php
/**
 * Privacy Policy Page for the Social Media Festival website
 */

// Get content from database
$title = get_content('privacy', 'title', 'Privacy Policy');
$last_updated = get_content('privacy', 'last_updated', 'Last Updated: May 22, 2025');
?>

<section class="privacy-policy-section">
    <div class="container">
        <div class="section-header">
            <h1><?php echo $title; ?></h1>
            <p class="last-updated"><?php echo $last_updated; ?></p>
        </div>

        <div class="privacy-content">
            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'introduction_title', 'Introduction'); ?></h2>
                <p><?php echo get_content('privacy', 'introduction_text', 'Welcome to the Social Media Festival website. We respect your privacy and are committed to protecting your personal data. This privacy policy will inform you about how we look after your personal data when you visit our website and tell you about your privacy rights and how the law protects you.'); ?></p>
                <p><?php echo get_content('privacy', 'introduction_text2', 'This privacy policy applies to all users of our website, including participants, nominees, voters, and visitors.'); ?></p>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'data_collection_title', 'Information We Collect'); ?></h2>
                <p><?php echo get_content('privacy', 'data_collection_text', 'We may collect, use, store and transfer different kinds of personal data about you which we have grouped together as follows:'); ?></p>
                
                <ul>
                    <li><?php echo get_content('privacy', 'data_identity', '<strong>Identity Data</strong> includes first name, last name, username or similar identifier.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_contact', '<strong>Contact Data</strong> includes email address and telephone numbers.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_technical', '<strong>Technical Data</strong> includes internet protocol (IP) address, your login data, browser type and version, time zone setting and location, browser plug-in types and versions, operating system and platform, and other technology on the devices you use to access this website.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_profile', '<strong>Profile Data</strong> includes your username and password, your interests, preferences, feedback and survey responses.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_usage', '<strong>Usage Data</strong> includes information about how you use our website.'); ?></li>
                </ul>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'data_use_title', 'How We Use Your Information'); ?></h2>
                <p><?php echo get_content('privacy', 'data_use_text', 'We will only use your personal data when the law allows us to. Most commonly, we will use your personal data in the following circumstances:'); ?></p>
                
                <ul>
                    <li><?php echo get_content('privacy', 'data_use_1', 'To register you as a new user.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_use_2', 'To process and deliver the services you have requested, including managing your voting activities.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_use_3', 'To manage our relationship with you, including notifying you about changes to our terms or privacy policy.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_use_4', 'To administer and protect our business and this website.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_use_5', 'To deliver relevant website content to you and measure or understand the effectiveness of the content we serve to you.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_use_6', 'To use data analytics to improve our website, products/services, marketing, customer relationships and experiences.'); ?></li>
                </ul>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'data_sharing_title', 'Information Sharing and Disclosure'); ?></h2>
                <p><?php echo get_content('privacy', 'data_sharing_text', 'We may share your personal information with:'); ?></p>
                
                <ul>
                    <li><?php echo get_content('privacy', 'data_sharing_1', '<strong>Service Providers:</strong> We may share your information with service providers who perform services on our behalf, such as web hosting, data analysis, payment processing, and customer service.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_sharing_2', '<strong>Business Partners:</strong> We may share information with our business partners, such as sponsors and co-organizers of the festival.'); ?></li>
                    <li><?php echo get_content('privacy', 'data_sharing_3', '<strong>Legal Requirements:</strong> We may disclose your information if required to do so by law or in response to valid requests by public authorities.'); ?></li>
                </ul>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'data_security_title', 'Data Security'); ?></h2>
                <p><?php echo get_content('privacy', 'data_security_text', 'We have implemented appropriate security measures to prevent your personal data from being accidentally lost, used, or accessed in an unauthorized way, altered, or disclosed. In addition, we limit access to your personal data to those employees, agents, contractors, and other third parties who have a business need to know.'); ?></p>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'data_retention_title', 'Data Retention'); ?></h2>
                <p><?php echo get_content('privacy', 'data_retention_text', 'We will only retain your personal data for as long as necessary to fulfill the purposes we collected it for, including for the purposes of satisfying any legal, accounting, or reporting requirements.'); ?></p>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'your_rights_title', 'Your Rights'); ?></h2>
                <p><?php echo get_content('privacy', 'your_rights_text', 'Under certain circumstances, you have rights under data protection laws in relation to your personal data, including the right to:'); ?></p>
                
                <ul>
                    <li><?php echo get_content('privacy', 'rights_1', '<strong>Request access</strong> to your personal data.'); ?></li>
                    <li><?php echo get_content('privacy', 'rights_2', '<strong>Request correction</strong> of your personal data.'); ?></li>
                    <li><?php echo get_content('privacy', 'rights_3', '<strong>Request erasure</strong> of your personal data.'); ?></li>
                    <li><?php echo get_content('privacy', 'rights_4', '<strong>Object to processing</strong> of your personal data.'); ?></li>
                    <li><?php echo get_content('privacy', 'rights_5', '<strong>Request restriction of processing</strong> your personal data.'); ?></li>
                    <li><?php echo get_content('privacy', 'rights_6', '<strong>Request transfer</strong> of your personal data.'); ?></li>
                    <li><?php echo get_content('privacy', 'rights_7', '<strong>Right to withdraw consent</strong> where we are relying on consent to process your personal data.'); ?></li>
                </ul>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'cookies_title', 'Cookies'); ?></h2>
                <p><?php echo get_content('privacy', 'cookies_text', 'Our website uses cookies to distinguish you from other users of our website. This helps us to provide you with a good experience when you browse our website and also allows us to improve our site.'); ?></p>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'changes_title', 'Changes to This Privacy Policy'); ?></h2>
                <p><?php echo get_content('privacy', 'changes_text', 'We may update our privacy policy from time to time. We will notify you of any changes by posting the new privacy policy on this page and updating the "last updated" date at the top of this privacy policy.'); ?></p>
            </div>

            <div class="privacy-section">
                <h2><?php echo get_content('privacy', 'contact_title', 'Contact Us'); ?></h2>
                <p><?php echo get_content('privacy', 'contact_text', 'If you have any questions about this privacy policy or our privacy practices, please contact us at:'); ?></p>
                <p><strong>Email:</strong> <?php echo get_content('privacy', 'contact_email', '<EMAIL>'); ?></p>
                <p><strong>Address:</strong> <?php echo get_content('privacy', 'contact_address', 'Social Media Festival Headquarters, Cairo, Egypt'); ?></p>
            </div>
        </div>
    </div>
</section>

<style>
    .privacy-policy-section {
        padding: 80px 0;
        background-color: var(--color-black);
        color: var(--color-white);
    }

    .privacy-policy-section .section-header {
        text-align: center;
        margin-bottom: 50px;
    }

    .privacy-policy-section h1 {
        color: var(--color-gold);
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .last-updated {
        color: var(--color-light-gray);
        font-style: italic;
    }

    .privacy-content {
        max-width: 800px;
        margin: 0 auto;
    }

    .privacy-section {
        margin-bottom: 40px;
    }

    .privacy-section h2 {
        color: var(--color-gold);
        font-size: 1.8rem;
        margin-bottom: 20px;
        border-bottom: 1px solid rgba(212, 175, 55, 0.3);
        padding-bottom: 10px;
    }

    .privacy-section p {
        margin-bottom: 15px;
        line-height: 1.6;
    }

    .privacy-section ul {
        margin-left: 20px;
        margin-bottom: 20px;
    }

    .privacy-section li {
        margin-bottom: 10px;
        line-height: 1.6;
    }

    .privacy-section strong {
        color: var(--color-gold);
    }

    /* RTL Support */
    html[dir="rtl"] .privacy-section ul {
        margin-left: 0;
        margin-right: 20px;
    }
</style>
