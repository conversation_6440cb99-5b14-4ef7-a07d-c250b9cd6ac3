/**
 * 3D Animations using Three.js for Social Media Festival
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if Three.js is loaded
    if (typeof THREE === 'undefined') {
        console.warn('Three.js is not loaded');
        return;
    }
    
    // Initialize 3D effects
    initHeroParticles();
    initSponsorEffects();
    init3DButtons();
});

/**
 * Initialize 3D particle effect for hero section
 */
function initHeroParticles() {
    const heroSection = document.querySelector('.hero-section');
    
    if (!heroSection) return;
    
    // Create container for particles
    const particlesContainer = document.createElement('div');
    particlesContainer.className = 'particles-container';
    particlesContainer.style.position = 'absolute';
    particlesContainer.style.top = '0';
    particlesContainer.style.left = '0';
    particlesContainer.style.width = '100%';
    particlesContainer.style.height = '100%';
    particlesContainer.style.overflow = 'hidden';
    particlesContainer.style.zIndex = '0';
    
    heroSection.insertBefore(particlesContainer, heroSection.firstChild);
    
    // Set up scene
    const scene = new THREE.Scene();
    
    // Set up camera
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 5;
    
    // Set up renderer
    const renderer = new THREE.WebGLRenderer({ alpha: true });
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    particlesContainer.appendChild(renderer.domElement);
    
    // Create particles
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 1000;
    
    const posArray = new Float32Array(particlesCount * 3);
    const colorsArray = new Float32Array(particlesCount * 3);
    
    for (let i = 0; i < particlesCount * 3; i++) {
        // Position
        posArray[i] = (Math.random() - 0.5) * 10;
        
        // Color (gold particles)
        if (i % 3 === 0) colorsArray[i] = 0.83; // R: 212/255
        if (i % 3 === 1) colorsArray[i] = 0.69; // G: 175/255
        if (i % 3 === 2) colorsArray[i] = 0.22; // B: 55/255
    }
    
    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
    particlesGeometry.setAttribute('color', new THREE.BufferAttribute(colorsArray, 3));
    
    // Material
    const particlesMaterial = new THREE.PointsMaterial({
        size: 0.02,
        transparent: true,
        opacity: 0.8,
        vertexColors: true,
        blending: THREE.AdditiveBlending
    });
    
    // Create points
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);
    
    // Animation
    function animate() {
        requestAnimationFrame(animate);
        
        particlesMesh.rotation.x += 0.0005;
        particlesMesh.rotation.y += 0.0005;
        
        renderer.render(scene, camera);
    }
    
    animate();
    
    // Handle window resize
    window.addEventListener('resize', function() {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });
    
    // Mouse movement effect
    document.addEventListener('mousemove', function(event) {
        const mouseX = (event.clientX / window.innerWidth) * 2 - 1;
        const mouseY = -(event.clientY / window.innerHeight) * 2 + 1;
        
        particlesMesh.rotation.x += mouseY * 0.0005;
        particlesMesh.rotation.y += mouseX * 0.0005;
    });
}

/**
 * Initialize 3D effects for sponsor logos
 */
function initSponsorEffects() {
    const sponsorItems = document.querySelectorAll('.sponsor-item');
    
    sponsorItems.forEach(item => {
        item.addEventListener('mousemove', function(e) {
            const rect = item.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const deltaX = (x - centerX) / centerX;
            const deltaY = (y - centerY) / centerY;
            
            const rotateX = deltaY * 10;
            const rotateY = -deltaX * 10;
            
            item.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
        });
        
        item.addEventListener('mouseleave', function() {
            item.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) scale3d(1, 1, 1)';
        });
    });
}

/**
 * Initialize 3D effects for buttons
 */
function init3DButtons() {
    const buttons = document.querySelectorAll('.btn-primary, .btn-vote-hero');
    
    buttons.forEach(button => {
        // Create 3D effect container
        const effectContainer = document.createElement('span');
        effectContainer.className = 'btn-3d-container';
        effectContainer.style.position = 'absolute';
        effectContainer.style.top = '0';
        effectContainer.style.left = '0';
        effectContainer.style.width = '100%';
        effectContainer.style.height = '100%';
        effectContainer.style.overflow = 'hidden';
        effectContainer.style.zIndex = '-1';
        
        // Create 3D effect
        const effect = document.createElement('span');
        effect.className = 'btn-3d-effect';
        effectContainer.appendChild(effect);
        
        // Add to button
        button.style.position = 'relative';
        button.appendChild(effectContainer);
        
        // Mouse movement effect
        button.addEventListener('mousemove', function(e) {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const deltaX = (x - centerX) / centerX;
            const deltaY = (y - centerY) / centerY;
            
            const rotateX = deltaY * 10;
            const rotateY = -deltaX * 10;
            
            button.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
            effect.style.opacity = '0.3';
        });
        
        button.addEventListener('mouseleave', function() {
            button.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
            effect.style.opacity = '0';
        });
    });
}
