/* Nominee Profile Page Styles */
.nominee-page {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(to bottom, #000000, #111111);
    min-height: 80vh;
}

.nominee-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.nominee-header h1 {
    color: var(--color-gold);
    font-size: 3rem;
    margin-bottom: var(--spacing-sm);
    text-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

.nominee-category {
    display: inline-flex;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.3);
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.category-link, .subcategory-link {
    color: var(--color-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.category-link:hover, .subcategory-link:hover {
    color: var(--color-gold);
}

.separator {
    color: var(--color-gold);
    margin: 0 8px;
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.5);
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.5);
    color: #dc3545;
}

.nominee-profile-content {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid rgba(255, 215, 0, 0.1);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.nominee-profile-image {
    flex: 0 0 350px;
    height: 500px;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    border: 2px solid rgba(255, 215, 0, 0.3);
    position: relative;
}

.nominee-profile-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 70%, rgba(0, 0, 0, 0.7));
    pointer-events: none;
}

.nominee-profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.nominee-profile-content:hover .nominee-profile-image img {
    transform: scale(1.05);
}

.nominee-profile-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.nominee-bio {
    margin-bottom: var(--spacing-lg);
}

.nominee-bio h2 {
    color: var(--color-gold);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    position: relative;
    display: inline-block;
}

.nominee-bio h2::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--color-gold);
    transition: width 0.3s ease;
}

.nominee-profile-content:hover .nominee-bio h2::after {
    width: 100%;
}

.nominee-bio p {
    color: var(--color-white);
    line-height: 1.8;
    font-size: 1.1rem;
}

.nominee-stats {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    background-color: rgba(0, 0, 0, 0.3);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    display: inline-flex;
}

.votes-count {
    display: flex;
    align-items: center;
}

.votes-count i {
    color: var(--color-gold);
    font-size: 1.5rem;
    margin-right: 10px;
}

.votes-count span {
    color: var(--color-white);
    font-size: 1.5rem;
    font-weight: bold;
    margin-right: 5px;
}

.votes-label {
    color: var(--color-light);
    font-size: 1rem;
}

.nominee-social {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.nominee-social .social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.nominee-social .social-icon:hover {
    background-color: var(--color-gold);
    color: var(--color-black);
    transform: translateY(-3px);
}

.nominee-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    align-items: center;
    margin-top: auto;
}

.btn-vote {
    background-color: var(--color-gold);
    color: var(--color-black);
    border: none;
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
}

.btn-vote i {
    margin-right: 8px;
}

.btn-vote:hover {
    background-color: var(--color-gold-light);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.btn-vote.voted {
    background-color: #28a745;
    cursor: default;
}

.btn-vote.voted:hover {
    transform: none;
    box-shadow: none;
}

.login-prompt {
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.login-prompt p {
    margin-bottom: var(--spacing-sm);
}

.share-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.share-buttons span {
    color: var(--color-light);
}

.share-button {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    transition: all 0.3s ease;
}

.share-button.facebook {
    background-color: #3b5998;
}

.share-button.twitter {
    background-color: #1da1f2;
}

.share-button.whatsapp {
    background-color: #25d366;
}

.share-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* Other Nominees Section */
.other-nominees {
    margin-top: var(--spacing-xl);
}

.other-nominees h2 {
    color: var(--color-white);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-lg);
    text-align: center;
    position: relative;
    display: inline-block;
}

.other-nominees h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--color-gold), transparent);
}

.nominees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.nominee-card {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 100%;
}

.nominee-card:hover {
    transform: translateY(-10px);
    border-color: var(--color-gold);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.nominee-link {
    display: block;
    text-decoration: none;
    height: 100%;
}

.nominee-image {
    height: 300px;
    overflow: hidden;
    position: relative;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.nominee-card:hover .nominee-image img {
    transform: scale(1.1);
}

.nominee-info {
    padding: var(--spacing-md);
    text-align: center;
}

.nominee-info h3 {
    color: var(--color-gold);
    font-size: 1.3rem;
    margin-bottom: var(--spacing-sm);
}

.nominee-votes {
    display: flex;
    align-items: center;
    justify-content: center;
}

.nominee-votes i {
    color: var(--color-gold);
    font-size: 1.2rem;
    margin-right: 5px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .nominee-profile-content {
        flex-direction: column;
    }
    
    .nominee-profile-image {
        flex: 0 0 auto;
        height: 400px;
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }
    
    .nominee-header h1 {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .nominee-profile-image {
        height: 350px;
    }
    
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .nominee-image {
        height: 250px;
    }
    
    .nominee-header h1 {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .nominee-profile-image {
        height: 300px;
    }
    
    .nominee-actions {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .share-buttons {
        margin-top: var(--spacing-sm);
    }
    
    .nominee-header h1 {
        font-size: 1.8rem;
    }
}
