<?php
/**
 * Header template for the Social Media Festival website
 */

// Get current page
$current_page = $_GET['page'] ?? 'home';

// Get language direction
$dir = get_language_direction();
$lang = get_current_language();
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $dir; ?>" class="<?php echo $dir === 'rtl' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo get_content('site', 'title', SITE_NAME); ?></title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="Social Media Festival - مهرجان السوشيال ميديا - احتفالية سنوية للإبداع الرقمي والتواصل الاجتماعي">
    <meta name="keywords" content="Social Media Festival, مهرجان السوشيال ميديا, social media, festival, awards, nominees, voting, digital content, influencers">
    <meta name="author" content="Social Media Festival">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.socialmediafestivals.com/">
    <meta property="og:title" content="<?php echo get_content('site', 'title', SITE_NAME); ?>">
    <meta property="og:description" content="Social Media Festival - مهرجان السوشيال ميديا - احتفالية سنوية للإبداع الرقمي والتواصل الاجتماعي">
    <meta property="og:image" content="<?php echo asset_url('img/og-image.jpg'); ?>">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://www.socialmediafestivals.com/">
    <meta property="twitter:title" content="<?php echo get_content('site', 'title', SITE_NAME); ?>">
    <meta property="twitter:description" content="Social Media Festival - مهرجان السوشيال ميديا - احتفالية سنوية للإبداع الرقمي والتواصل الاجتماعي">
    <meta property="twitter:image" content="<?php echo asset_url('img/og-image.jpg'); ?>">

    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo asset_url('img/favicon/apple-touch-icon.png'); ?>">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo asset_url('img/favicon/favicon-32x32.png'); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo asset_url('img/favicon/favicon-16x16.png'); ?>">
    <link rel="manifest" href="<?php echo asset_url('img/favicon/site.webmanifest'); ?>">
    <link rel="mask-icon" href="<?php echo asset_url('img/favicon/safari-pinned-tab.svg'); ?>" color="#d4af37">
    <link rel="shortcut icon" href="<?php echo asset_url('img/favicon/favicon.ico'); ?>">
    <meta name="msapplication-TileColor" content="#d4af37">
    <meta name="msapplication-config" content="<?php echo asset_url('img/favicon/browserconfig.xml'); ?>">
    <meta name="theme-color" content="#000000">

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo asset_url('css/normalize.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/main.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/header.css'); ?>">
    <?php if ($page === 'vote'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/vote-premium.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/vote-premium-2.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'nominee'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/nominee.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'login' || $page === 'register'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/auth.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'compare'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/compare.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'nominees'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/nominees.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'sponsors'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/sponsors.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'celebrities'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/celebrities.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'celebrity'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/celebrities.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/celebrity.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'stories'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/stories.css'); ?>">
    <?php endif; ?>
    <?php if ($page === 'profile'): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/profile.css'); ?>">
    <?php endif; ?>
    <?php if (is_rtl()): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/rtl.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/rtl-enhanced.css'); ?>">
    <?php endif; ?>
        <style>
           @media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 70px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 70px);
    background-color: rgba(0, 0, 0, 0.95);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: left var(--transition-normal), right var(--transition-normal);
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  }
}

        </style>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&family=Poppins:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- GSAP for animations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <!-- Three.js for 3D effects -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- Swiper.js for carousels -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>

    <!-- Performance Optimization Scripts -->
    <script src="<?php echo asset_url('js/simple-lazy.js'); ?>" defer></script>
    <script src="<?php echo asset_url('js/main.js'); ?>" defer></script>
</head>
<body class="page-<?php echo $current_page; ?>">
    <header class="site-header">
        <div class="container">
            <div class="header-inner">
                <div class="logo">
                    <a href="<?php echo base_url(); ?>">
                        <img src="<?php echo asset_url('img/logo.png'); ?>" alt="<?php echo SITE_NAME; ?>">
                    </a>
                </div>

                <nav class="main-nav">
                    <button class="menu-toggle" aria-label="Toggle Menu">
                        <span class="menu-toggle-bar"></span>
                        <span class="menu-toggle-bar"></span>
                        <span class="menu-toggle-bar"></span>
                    </button>

                    <ul class="nav-menu">
                        <li class="<?php echo $current_page === 'home' ? 'active' : ''; ?>">
                            <a href="<?php echo base_url(); ?>"><?php echo get_content('nav', 'home', 'Home'); ?></a>
                        </li>
                        <li class="<?php echo $current_page === 'nominees' ? 'active' : ''; ?>">
                            <a href="<?php echo base_url('?page=nominees'); ?>"><?php echo get_content('nav', 'nominees', 'Nominees'); ?></a>
                        </li>
                        <li class="<?php echo $current_page === 'sponsors' ? 'active' : ''; ?>">
                            <a href="<?php echo base_url('?page=sponsors'); ?>"><?php echo get_content('nav', 'sponsors', 'Sponsors'); ?></a>
                        </li>
                        <li class="<?php echo $current_page === 'celebrities' ? 'active' : ''; ?>">
                            <a href="<?php echo base_url('?page=celebrities'); ?>"><?php echo get_content('nav', 'celebrities', 'Celebrities'); ?></a>
                        </li>
                        <li class="<?php echo $current_page === 'stories' ? 'active' : ''; ?>">
                            <a href="<?php echo base_url('?page=stories'); ?>"><?php echo get_content('nav', 'stories', 'Festival Highlights'); ?></a>
                        </li>
                        <li class="<?php echo $current_page === 'vote' ? 'active' : ''; ?>">
                            <a href="<?php echo base_url('?page=vote'); ?>" class="btn-vote"><?php echo get_content('nav', 'vote', 'Vote Now'); ?></a>
                        </li>
                    </ul>
                </nav>

                <div class="header-actions">
                    <?php echo language_switcher(); ?>

                    <?php if (is_logged_in()): ?>
                        <div class="user-menu">
                            <a href="<?php echo base_url('?page=profile'); ?>" class="user-toggle">
                                <i class="fas fa-user-circle"></i>
                                <span><?php echo $_SESSION['user_name'] ?? $_SESSION['full_name']; ?></span>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a href="<?php echo base_url('?page=profile'); ?>"><?php echo get_content('nav', 'profile', 'Profile'); ?></a></li>
                                <?php if (is_admin()): ?>
                                <li><a href="<?php echo base_url('admin/'); ?>"><?php echo get_content('nav', 'admin', 'Admin Dashboard'); ?></a></li>
                                <?php endif; ?>
                                <li><a href="<?php echo base_url('includes/logout.php'); ?>"><?php echo get_content('nav', 'logout', 'Logout'); ?></a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <div class="auth-buttons">
                            <a href="<?php echo base_url('?page=login'); ?>" class="btn-login"><?php echo get_content('nav', 'login', 'Login'); ?></a>
                            <a href="<?php echo base_url('?page=register'); ?>" class="btn-register"><?php echo get_content('nav', 'register', 'Register'); ?></a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </header>

    <main class="site-main">
