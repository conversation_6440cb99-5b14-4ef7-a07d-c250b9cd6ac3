<?php
/**
 * Admin Reels Management Page
 */

// Make sure uploads directory exists
$uploads_dir = __DIR__ . '/../../assets/uploads/';
if (!file_exists($uploads_dir)) {
    mkdir($uploads_dir, 0777, true);
}

// Define web path for uploads
define('UPLOADS_WEB_PATH', 'uploads/');

// Process form submission
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $reel_id = isset($_POST['reel_id']) ? (int)$_POST['reel_id'] : 0;
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $title_ar = isset($_POST['title_ar']) ? trim($_POST['title_ar']) : '';
    $description = isset($_POST['description']) ? trim($_POST['description']) : '';
    $description_ar = isset($_POST['description_ar']) ? trim($_POST['description_ar']) : '';
    $action = isset($_POST['action']) ? $_POST['action'] : '';

    // Handle file uploads
    $video = '';
    $thumbnail = '';

    if (isset($_FILES['video']) && $_FILES['video']['error'] === UPLOAD_ERR_OK) {
        $video_tmp = $_FILES['video']['tmp_name'];
        $video_name = $_FILES['video']['name'];
        $video_ext = pathinfo($video_name, PATHINFO_EXTENSION);
        $video = 'reel_' . time() . '.' . $video_ext;

        if (!move_uploaded_file($video_tmp, $uploads_dir . $video)) {
            $message = 'حدث خطأ أثناء تحميل الفيديو.';
            $message_type = 'danger';
            $video = '';
        }
    }

    if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK) {
        $thumbnail_tmp = $_FILES['thumbnail']['tmp_name'];
        $thumbnail_name = $_FILES['thumbnail']['name'];
        $thumbnail_ext = pathinfo($thumbnail_name, PATHINFO_EXTENSION);
        $thumbnail = 'reel_thumb_' . time() . '.' . $thumbnail_ext;

        if (!move_uploaded_file($thumbnail_tmp, $uploads_dir . $thumbnail)) {
            $message = 'حدث خطأ أثناء تحميل الصورة المصغرة.';
            $message_type = 'danger';
            $thumbnail = '';
        }
    }

    // Process based on action
    if ($action === 'delete') {
        if ($reel_id > 0) {
            $get_files_sql = "SELECT video, thumbnail FROM reels WHERE id = ?";
            $get_files_stmt = $conn->prepare($get_files_sql);
            $get_files_stmt->bind_param('i', $reel_id);
            $get_files_stmt->execute();
            $get_files_result = $get_files_stmt->get_result();

            if ($get_files_result && $get_files_result->num_rows > 0) {
                $files = $get_files_result->fetch_assoc();
                if (!empty($files['video'])) {
                    @unlink($uploads_dir . $files['video']);
                }
                if (!empty($files['thumbnail'])) {
                    @unlink($uploads_dir . $files['thumbnail']);
                }
            }

            $sql = "DELETE FROM reels WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('i', $reel_id);

            if ($stmt->execute()) {
                $message = 'تم حذف الريل بنجاح.';
                $message_type = 'success';
            } else {
                $message = 'حدث خطأ أثناء حذف الريل: ' . $stmt->error;
                $message_type = 'danger';
            }
        }
    } elseif ($action === 'edit') {
        if ($reel_id > 0 && !empty($title) && !empty($title_ar)) {
            $get_reel_sql = "SELECT video, thumbnail FROM reels WHERE id = ?";
            $get_reel_stmt = $conn->prepare($get_reel_sql);
            $get_reel_stmt->bind_param('i', $reel_id);
            $get_reel_stmt->execute();
            $get_reel_result = $get_reel_stmt->get_result();
            $current_reel = $get_reel_result->fetch_assoc();

            if (empty($video)) {
                $video = $current_reel['video'];
            } else {
                if (!empty($current_reel['video'])) {
                    @unlink($uploads_dir . $current_reel['video']);
                }
            }

            if (empty($thumbnail)) {
                $thumbnail = $current_reel['thumbnail'];
            } else {
                if (!empty($current_reel['thumbnail'])) {
                    @unlink($uploads_dir . $current_reel['thumbnail']);
                }
            }

            $sql = "UPDATE reels SET title = ?, title_ar = ?, description = ?, description_ar = ?, video = ?, thumbnail = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ssssssi', $title, $title_ar, $description, $description_ar, $video, $thumbnail, $reel_id);

            if ($stmt->execute()) {
                $message = 'تم تحديث الريل بنجاح.';
                $message_type = 'success';
            } else {
                $message = 'حدث خطأ أثناء تحديث الريل: ' . $stmt->error;
                $message_type = 'danger';
            }
        } else {
            $message = 'يرجى ملء جميع الحقول المطلوبة.';
            $message_type = 'danger';
        }
    } elseif ($action === 'add') {
        if (!empty($title) && !empty($title_ar) && !empty($video) && !empty($thumbnail)) {
            $sql = "INSERT INTO reels (title, title_ar, description, description_ar, video, thumbnail) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ssssss', $title, $title_ar, $description, $description_ar, $video, $thumbnail);

            if ($stmt->execute()) {
                $message = 'تمت إضافة الريل بنجاح.';
                $message_type = 'success';
            } else {
                $message = 'حدث خطأ أثناء إضافة الريل: ' . $stmt->error;
                $message_type = 'danger';
            }
        } else {
            $message = 'يرجى ملء جميع الحقول المطلوبة وتحميل الفيديو والصورة المصغرة.';
            $message_type = 'danger';
        }
    }
}

// Get all reels
$reels_sql = "SELECT * FROM reels ORDER BY created_at DESC";
$reels_result = $conn->query($reels_sql);
$reels = [];

if ($reels_result && $reels_result->num_rows > 0) {
    while ($row = $reels_result->fetch_assoc()) {
        $reels[] = $row;
    }
}
?>


<div class="reels-management">
    <h1 class="page-title">إدارة الريلز</h1>

    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?>">
        <?php echo $message; ?>
    </div>
    <?php endif; ?>

    <!-- Add New Reel Button -->
    <div class="action-buttons">
        <button class="btn btn-primary" id="addReelBtn">
            <i class="fas fa-plus"></i> إضافة ريل جديد
        </button>
    </div>
<input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

    <!-- Reels List -->
    <div class="card">
        <div class="card-header">
            <h2>قائمة الريلز</h2>
        </div>
        <div class="card-body">
            <?php if (empty($reels)): ?>
            <p class="no-data">لا توجد ريلز حتى الآن.</p>
            <?php else: ?>
            <div class="reels-grid">
                <?php foreach ($reels as $reel): ?>
                <div class="reel-card">
                    <div class="reel-thumbnail">
                        <?php if (!empty($reel['thumbnail'])): ?>
                        <img src="<?php echo asset_url(UPLOADS_WEB_PATH . $reel['thumbnail']); ?>" alt="<?php echo htmlspecialchars($reel['title']); ?>">
                        <?php else: ?>
                        <div class="placeholder">
                            <i class="fas fa-film"></i>
                        </div>
                        <?php endif; ?>
                        <div class="play-button" data-video="<?php echo asset_url(UPLOADS_WEB_PATH . $reel['video']); ?>">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="reel-info">
                        <h3><?php echo htmlspecialchars($reel['title']); ?></h3>
                        <h4><?php echo htmlspecialchars($reel['title_ar']); ?></h4>
                        <p><?php echo htmlspecialchars(substr($reel['description'], 0, 100)) . (strlen($reel['description']) > 100 ? '...' : ''); ?></p>
                        <div class="reel-actions">
                            <button class="btn btn-sm btn-primary edit-reel"
                                data-id="<?php echo $reel['id']; ?>"
                                data-title="<?php echo htmlspecialchars($reel['title']); ?>"
                                data-title-ar="<?php echo htmlspecialchars($reel['title_ar']); ?>"
                                data-description="<?php echo htmlspecialchars($reel['description']); ?>"
                                data-description-ar="<?php echo htmlspecialchars($reel['description_ar']); ?>"
                                data-video="<?php echo htmlspecialchars($reel['video']); ?>"
                                data-thumbnail="<?php echo htmlspecialchars($reel['thumbnail']); ?>">
                                <i class="fas fa-edit"></i> تحرير
                            </button>
                            <button class="btn btn-sm btn-danger delete-reel"
                                data-id="<?php echo $reel['id']; ?>"
                                data-title="<?php echo htmlspecialchars($reel['title']); ?>">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Reel Modal -->
<div class="modal" id="addReelModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>إضافة ريل جديد</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=reels" method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="add">

                <div class="form-row">
                    <div class="form-group">
                        <label for="title">العنوان (الإنجليزية)</label>
                        <input type="text" id="title" name="title" required>
                    </div>

                    <div class="form-group">
                        <label for="title_ar">العنوان (العربية)</label>
                        <input type="text" id="title_ar" name="title_ar" required dir="rtl">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="description">الوصف (الإنجليزية)</label>
                        <textarea id="description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="description_ar">الوصف (العربية)</label>
                        <textarea id="description_ar" name="description_ar" rows="3" dir="rtl"></textarea>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="video">الفيديو</label>
                        <input type="file" id="video" name="video" accept="video/*" required>
                        <div class="file-preview"></div>
                    </div>

                    <div class="form-group">
                        <label for="thumbnail">الصورة المصغرة</label>
                        <input type="file" id="thumbnail" name="thumbnail" accept="image/*" required>
                        <div class="file-preview"></div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">إضافة ريل</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Reel Modal -->
<div class="modal" id="editReelModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>تحرير الريل</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=reels" method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="reel_id" id="edit_reel_id">

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_title">العنوان (الإنجليزية)</label>
                        <input type="text" id="edit_title" name="title" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_title_ar">العنوان (العربية)</label>
                        <input type="text" id="edit_title_ar" name="title_ar" required dir="rtl">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_description">الوصف (الإنجليزية)</label>
                        <textarea id="edit_description" name="description" rows="3"></textarea>
                    </div>

                    <div class="form-group">
                        <label for="edit_description_ar">الوصف (العربية)</label>
                        <textarea id="edit_description_ar" name="description_ar" rows="3" dir="rtl"></textarea>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_video">الفيديو</label>
                        <input type="file" id="edit_video" name="video" accept="video/*">
                        <div class="file-preview" id="edit_video_preview"></div>
                        <small class="form-text">اترك هذا الحقل فارغًا للاحتفاظ بالفيديو الحالي.</small>
                    </div>

                    <div class="form-group">
                        <label for="edit_thumbnail">الصورة المصغرة</label>
                        <input type="file" id="edit_thumbnail" name="thumbnail" accept="image/*">
                        <div class="file-preview" id="edit_thumbnail_preview"></div>
                        <small class="form-text">اترك هذا الحقل فارغًا للاحتفاظ بالصورة المصغرة الحالية.</small>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Reel Modal -->
<div class="modal" id="deleteReelModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>حذف الريل</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من أنك تريد حذف الريل "<span id="delete_reel_title"></span>"؟</p>
            <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>

            <form action="index.php?page=reels" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="reel_id" id="delete_reel_id">

                <div class="form-actions">
                    <button type="submit" class="btn btn-danger">حذف</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reel Video Modal -->
<div class="reel-video-modal">
    <div class="reel-video-container">
        <video controls></video>
        <div class="close-modal"><i class="fas fa-times"></i></div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add reel modal
    const addReelBtn = document.getElementById('addReelBtn');
    const addReelModal = document.getElementById('addReelModal');

    if (addReelBtn) {
        addReelBtn.addEventListener('click', function() {
            addReelModal.style.display = 'block';
        });
    }

    // Edit reel modal
    const editButtons = document.querySelectorAll('.edit-reel');
    const editReelModal = document.getElementById('editReelModal');
    const editReelId = document.getElementById('edit_reel_id');
    const editTitle = document.getElementById('edit_title');
    const editTitleAr = document.getElementById('edit_title_ar');
    const editDescription = document.getElementById('edit_description');
    const editDescriptionAr = document.getElementById('edit_description_ar');
    const editVideoPreview = document.getElementById('edit_video_preview');
    const editThumbnailPreview = document.getElementById('edit_thumbnail_preview');

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const title = this.getAttribute('data-title');
            const titleAr = this.getAttribute('data-title-ar');
            const description = this.getAttribute('data-description');
            const descriptionAr = this.getAttribute('data-description-ar');
            const video = this.getAttribute('data-video');
            const thumbnail = this.getAttribute('data-thumbnail');

            editReelId.value = id;
            editTitle.value = title;
            editTitleAr.value = titleAr;
            editDescription.value = description;
            editDescriptionAr.value = descriptionAr;

            // Show video preview
            editVideoPreview.innerHTML = '';
            if (video) {
                const videoElement = document.createElement('video');
                videoElement.src = '<?php echo asset_url(UPLOADS_WEB_PATH); ?>' + video;
                videoElement.controls = true;
                videoElement.muted = true;
                editVideoPreview.appendChild(videoElement);
            }

            // Show thumbnail preview
            editThumbnailPreview.innerHTML = '';
            if (thumbnail) {
                const img = document.createElement('img');
                img.src = '<?php echo asset_url(UPLOADS_WEB_PATH); ?>' + thumbnail;
                img.alt = title;
                editThumbnailPreview.appendChild(img);
            }

            editReelModal.style.display = 'block';
        });
    });

    // Delete reel modal
    const deleteButtons = document.querySelectorAll('.delete-reel');
    const deleteReelModal = document.getElementById('deleteReelModal');
    const deleteReelId = document.getElementById('delete_reel_id');
    const deleteReelTitle = document.getElementById('delete_reel_title');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const title = this.getAttribute('data-title');

            deleteReelId.value = id;
            deleteReelTitle.textContent = title;

            deleteReelModal.style.display = 'block';
        });
    });

    // Play reel video
    const playButtons = document.querySelectorAll('.reel-thumbnail .play-button');
    const reelVideoModal = document.querySelector('.reel-video-modal');
    const modalVideo = reelVideoModal.querySelector('video');
    const closeModal = reelVideoModal.querySelector('.close-modal');

    playButtons.forEach(button => {
        button.addEventListener('click', function() {
            const videoSrc = this.getAttribute('data-video');

            if (videoSrc) {
                modalVideo.src = videoSrc;
                reelVideoModal.classList.add('active');
                modalVideo.play();
            }
        });
    });

    // Close modal when clicking the close button
    closeModal.addEventListener('click', function() {
        modalVideo.pause();
        reelVideoModal.classList.remove('active');
    });

    // Close modal when clicking outside the video
    reelVideoModal.addEventListener('click', function(e) {
        if (e.target === reelVideoModal) {
            modalVideo.pause();
            reelVideoModal.classList.remove('active');
        }
    });

    // Close modals
    const modalCloseButtons = document.querySelectorAll('.modal-close');

    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });

    // File input preview
    const fileInputs = document.querySelectorAll('input[type="file"]');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const previewContainer = this.nextElementSibling;

            if (previewContainer && this.files && this.files[0]) {
                const file = this.files[0];
                previewContainer.innerHTML = '';

                if (file.type.startsWith('image/')) {
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.alt = file.name;
                    previewContainer.appendChild(img);
                } else if (file.type.startsWith('video/')) {
                    const video = document.createElement('video');
                    video.src = URL.createObjectURL(file);
                    video.controls = true;
                    video.muted = true;
                    previewContainer.appendChild(video);
                }
            }
        });
    });
});
</script>
