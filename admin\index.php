<?php
/**
 * Admin Dashboard for Social Media Festival
 */

// Start output buffering to prevent header issues
ob_start();

// Load session configuration
require_once '../config/session.php';

// Start session
session_start();

// Load configuration
$conn = require_once '../config/database.php';
require_once '../config/config.php';
require_once '../config/i18n.php';

// Load common functions
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Determine the page to load
$page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';

// Validate page name to prevent directory traversal
$page = preg_replace('/[^a-zA-Z0-9_-]/', '', $page);

// Define allowed pages
$allowed_pages = [
    'dashboard', 'content', 'header_content', 'categories', 'nominees', 'sponsors', 'celebrities',
    'stories', 'reels', 'users', 'votes', 'settings', 'ceo', 'team', 'invitations',
    'main_categories', 'subcategories', 'voting_categories', 'voting_subcategories', 'voting_nominees','visits', 'influencers', 'podcasts', 'media_coverage', 'gallery'
];

// Check if page exists and is allowed
if (!in_array($page, $allowed_pages) || !file_exists("pages/{$page}.php")) {
    $page = 'dashboard'; // Default to dashboard if page doesn't exist
}

// Get user data
$user_id = $_SESSION['user_id'];
$user = get_user($user_id);

// Get counts for dashboard
try {
    // Check if tables exist before querying
    $tables_result = $conn->query("SHOW TABLES");
    $tables = [];
    if ($tables_result) {
        while ($row = $tables_result->fetch_row()) {
            $tables[] = $row[0];
        }
    }

    // Get nominee count - use celebrities table as nominees
    $nominee_count = 0;
    if (in_array('celebrities', $tables)) {
        $nominee_query = $conn->query("SELECT COUNT(*) FROM celebrities");
        if ($nominee_query) {
            $nominee_count = $nominee_query->fetch_row()[0];
        }
    }

    // Get sponsor count
    $sponsor_count = 0;
    if (in_array('sponsors', $tables)) {
        $sponsor_query = $conn->query("SELECT COUNT(*) FROM sponsors");
        if ($sponsor_query) {
            $sponsor_count = $sponsor_query->fetch_row()[0];
        }
    }

    // Get celebrity count - same as nominee count in this case
    $celebrity_count = $nominee_count;

    // Get user count
    $user_count = 0;
    if (in_array('users', $tables)) {
        $user_query = $conn->query("SELECT COUNT(*) FROM users");
        if ($user_query) {
            $user_count = $user_query->fetch_row()[0];
        }
    }

    // Get vote count - use voting_votes table
    $vote_count = 0;
    if (in_array('voting_votes', $tables)) {
        $vote_query = $conn->query("SELECT COUNT(*) FROM voting_votes");
        if ($vote_query) {
            $vote_count = $vote_query->fetch_row()[0];
        }
    }
} catch (Exception $e) {
    // Log error
    error_log("Error getting dashboard counts: " . $e->getMessage());

    // Set default values
    $nominee_count = 0;
    $sponsor_count = 0;
    $celebrity_count = 0;
    $user_count = 0;
    $vote_count = 0;
}

// Get language direction and current language
$dir = get_language_direction();
$lang = get_current_language();
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $dir; ?>" class="<?php echo $dir === 'rtl' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo SITE_NAME; ?></title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo asset_url('img/favicon.ico'); ?>">

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo asset_url('css/normalize.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-pages.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-dashboard.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-content.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-voting-pages.css'); ?>">
    <?php if (is_rtl()): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-rtl.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-pages-rtl.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-rtl-enhanced.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-dashboard-rtl.css'); ?>">
    <?php endif; ?>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&family=Poppins:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="admin-page-<?php echo $page; ?>">
    <div class="admin-wrapper">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <img src="<?php echo asset_url('img/logo.png'); ?>" alt="<?php echo SITE_NAME; ?>">
                </a>
                <button class="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="<?php echo $page === 'dashboard' ? 'active' : ''; ?>">
                        <a href="index.php">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a>
                    </li>
                    <li class="<?php echo in_array($page, ['content', 'header_content']) ? 'active' : ''; ?>">
                        <a href="#" class="dropdown-toggle">
                            <i class="fas fa-edit"></i>
                            <span>إدارة المحتوى</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="<?php echo $page === 'content' ? 'active' : ''; ?>">
                                <a href="index.php?page=content">
                                    <i class="fas fa-file-alt"></i>
                                    <span>المحتوى العام</span>
                                </a>
                            </li>
                            <li class="<?php echo $page === 'header_content' ? 'active' : ''; ?>">
                                <a href="index.php?page=header_content">
                                    <i class="fas fa-heading"></i>
                                    <span>محتوى الهيدر</span>
                                </a>
                            </li>
                        </ul>
                    </li>


                    <li class="<?php echo $page === 'sponsors' ? 'active' : ''; ?>">
                        <a href="index.php?page=sponsors">
                            <i class="fas fa-handshake"></i>
                            <span>الرعاة</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'celebrities' ? 'active' : ''; ?>">
                        <a href="index.php?page=celebrities">
                            <i class="fas fa-star"></i>
                            <span>النجوم</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'influencers' ? 'active' : ''; ?>">
                        <a href="index.php?page=influencers">
                            <i class="fas fa-users"></i>
                            <span>البلوجرز المؤثرين</span>
                        </a>
                    </li>

                    <li class="<?php echo $page === 'reels' ? 'active' : ''; ?>">
                        <a href="index.php?page=reels">
                            <i class="fas fa-video"></i>
                            <span>الريلز</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'podcasts' ? 'active' : ''; ?>">
                        <a href="index.php?page=podcasts">
                            <i class="fas fa-microphone"></i>
                            <span>البودكاست</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'media_coverage' ? 'active' : ''; ?>">
                        <a href="index.php?page=media_coverage">
                            <i class="fas fa-tv"></i>
                            <span>التغطية الإعلامية</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'gallery' ? 'active' : ''; ?>">
                        <a href="index.php?page=gallery">
                            <i class="fas fa-images"></i>
                            <span>معرض الصور</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'users' ? 'active' : ''; ?>">
                        <a href="index.php?page=users">
                            <i class="fas fa-users"></i>
                            <span>المستخدمون</span>
                        </a>
                    </li>
                    <li class="<?php echo in_array($page, ['votes', 'voting_categories', 'voting_subcategories', 'voting_nominees']) ? 'active' : ''; ?>">
                        <a href="#" class="dropdown-toggle">
                            <i class="fas fa-vote-yea"></i>
                            <span>نظام التصويت</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="<?php echo $page === 'voting_categories' ? 'active' : ''; ?>">
                                <a href="index.php?page=voting_categories">
                                    <i class="fas fa-folder"></i>
                                    <span>الفئات الرئيسية</span>
                                </a>
                            </li>
                            <li class="<?php echo $page === 'voting_subcategories' ? 'active' : ''; ?>">
                                <a href="index.php?page=voting_subcategories">
                                    <i class="fas fa-folder-open"></i>
                                    <span>الفئات الفرعية</span>
                                </a>
                            </li>
                            <li class="<?php echo $page === 'voting_nominees' ? 'active' : ''; ?>">
                                <a href="index.php?page=voting_nominees">
                                    <i class="fas fa-user-friends"></i>
                                    <span>المرشحين</span>
                                </a>
                            </li>
                            <li class="<?php echo $page === 'votes' ? 'active' : ''; ?>">
                                <a href="index.php?page=votes">
                                    <i class="fas fa-chart-bar"></i>
                                    <span>إحصائيات التصويت</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="<?php echo $page === 'ceo' ? 'active' : ''; ?>">
                        <a href="index.php?page=ceo">
                            <i class="fas fa-user-tie"></i>
                            <span>الرئيس التنفيذي</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'team' ? 'active' : ''; ?>">
                        <a href="index.php?page=team">
                            <i class="fas fa-users-cog"></i>
                            <span>فريق العمل</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'invitations' ? 'active' : ''; ?>">
                        <a href="index.php?page=invitations">
                            <i class="fas fa-envelope-open-text"></i>
                            <span>الدعوات</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'visits' ? 'active' : ''; ?>">
                        <a href="index.php?page=visits">
                            <i class="fas fa-chart-line"></i>
                            <span>إحصائيات الزيارات</span>
                        </a>
                    </li>
                    <li class="<?php echo $page === 'settings' ? 'active' : ''; ?>">
                        <a href="index.php?page=settings">
                            <i class="fas fa-cog"></i>
                            <span>الإعدادات</span>
                        </a>
                    </li>
                    <li>
                        <a href="../clear-cache.php" target="_blank">
                            <i class="fas fa-broom"></i>
                            <span>مسح الكاش</span>
                        </a>
                    </li>
                    <li>
                        <a href="../index.php" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            <span>عرض الموقع</span>
                        </a>
                    </li>
                    <li>
                        <a href="../includes/logout.php">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>تسجيل الخروج</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <header class="admin-header">
                <div class="header-search">
                    <form action="index.php?page=search" method="get">
                        <input type="hidden" name="page" value="search">
                        <input type="text" name="q" placeholder="بحث...">
                        <button type="submit"><i class="fas fa-search"></i></button>
                    </form>
                </div>

                <div class="header-actions">
                    <?php echo language_switcher(); ?>

                    <div class="user-menu">
                        <a href="#" class="user-toggle">
                            <i class="fas fa-user-circle"></i>
                            <span><?php echo $user['full_name']; ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a href="../index.php?page=profile"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                            <li><a href="index.php?page=settings"><i class="fas fa-cog"></i> الإعدادات</a></li>
                            <li><a href="../includes/logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </header>

            <div class="admin-content">
                <?php include "pages/{$page}.php"; ?>
            </div>

            <footer class="admin-footer">
                <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.</p>
            </footer>
        </main>
    </div>

    <!-- Admin JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="<?php echo asset_url('js/admin.js'); ?>"></script>
    <script src="<?php echo asset_url('js/admin-pages.js'); ?>"></script>

    <!-- Dashboard JavaScript (only load on dashboard page) -->
    <?php if ($page === 'dashboard'): ?>
    <script src="<?php echo asset_url('js/admin-dashboard.js'); ?>"></script>
    <?php endif; ?>

    <!-- Page-specific JavaScript -->
    <?php
    // Ensure $page is a string
    $page_js = is_array($page) ? 'dashboard' : $page;
    if (file_exists(__DIR__ . '/../assets/js/admin/' . $page_js . '.js')):
    ?>
    <script src="<?php echo asset_url('js/admin/' . $page_js . '.js'); ?>"></script>
    <?php endif; ?>

    <!-- Special fix for dropdown menus -->
    <script src="<?php echo asset_url('js/admin-dropdown-fix.js'); ?>"></script>
</body>
</html>
<?php
// End output buffering
ob_end_flush();
?>
