/**
 * Enhanced JavaScript for Admin Pages
 * Includes animations and interactions for celebrities, stories, users, votes, and settings pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add page transition effect
    document.body.classList.add('page-loaded');

    // Enhanced card hover effects
    const cards = document.querySelectorAll('.celebrity-card, .story-card, .user-card');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px)';
            this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
            this.style.borderColor = 'var(--color-gold)';

            // Find image or video and add scale effect
            const media = this.querySelector('img, video');
            if (media) {
                media.style.transform = 'scale(1.05)';
            }
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
            this.style.borderColor = '';

            // Reset image or video scale
            const media = this.querySelector('img, video');
            if (media) {
                media.style.transform = '';
            }
        });
    });

    // Enhanced button hover effects
    const buttons = document.querySelectorAll('.btn');

    buttons.forEach(button => {
        if (!button.classList.contains('modal-close')) {
            button.addEventListener('mouseenter', function() {
                if (this.classList.contains('btn-primary')) {
                    this.style.backgroundColor = '#e5c14c';
                } else if (this.classList.contains('btn-danger')) {
                    this.style.backgroundColor = '#ff5252';
                }

                this.style.transform = 'translateY(-3px)';
                this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
                this.style.transform = '';
                this.style.boxShadow = '';
            });
        }
    });

    // Enhanced file input previews
    const fileInputs = document.querySelectorAll('input[type="file"]');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const previewContainer = this.nextElementSibling;

            if (previewContainer && this.files && this.files[0]) {
                const file = this.files[0];
                previewContainer.innerHTML = '';

                if (file.type.startsWith('image/')) {
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.alt = 'Preview';
                    img.style.opacity = '0';
                    previewContainer.appendChild(img);

                    // Fade in effect
                    setTimeout(() => {
                        img.style.transition = 'opacity 0.3s ease-in-out';
                        img.style.opacity = '1';
                    }, 50);
                } else if (file.type.startsWith('video/')) {
                    const video = document.createElement('video');
                    video.src = URL.createObjectURL(file);
                    video.controls = true;
                    video.muted = true;
                    video.style.opacity = '0';
                    previewContainer.appendChild(video);

                    // Fade in effect
                    setTimeout(() => {
                        video.style.transition = 'opacity 0.3s ease-in-out';
                        video.style.opacity = '1';
                    }, 50);
                }
            }
        });
    });

    // Enhanced modal animations
    const modalTriggers = document.querySelectorAll('[data-modal]');

    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);

            if (modal) {
                // Add animation classes
                modal.classList.add('modal-animated');
                const modalContent = modal.querySelector('.modal-content');

                if (modalContent) {
                    modalContent.classList.add('modal-content-animated');
                }

                modal.style.display = 'block';
            }
        });
    });

    // Settings tabs with smooth transitions - Skip if we're on the settings page
    // The settings page has its own tab handling code
    if (!document.querySelector('.settings-page')) {
        const otherTabs = document.querySelectorAll('.tab:not(.settings-tabs .tab)');
        const otherPanes = document.querySelectorAll('.tab-pane:not(.settings-page .tab-pane)');

        if (otherTabs.length > 0 && otherPanes.length > 0) {
            otherTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    if (this.classList.contains('active')) return;

                    // Remove active class from all tabs and panes
                    otherTabs.forEach(t => t.classList.remove('active'));

                    // First hide all panes with fade out
                    otherPanes.forEach(p => {
                        if (p.classList.contains('active')) {
                            p.style.opacity = '0';
                            setTimeout(() => {
                                p.classList.remove('active');
                            }, 200);
                        } else {
                            p.classList.remove('active');
                        }
                    });

                    // Add active class to clicked tab
                    const tabId = this.getAttribute('data-tab');
                    this.classList.add('active');

                    // Show the corresponding pane with a slight delay for animation
                    setTimeout(() => {
                        const activePane = document.getElementById(tabId);
                        activePane.classList.add('active');

                        // Trigger reflow
                        void activePane.offsetWidth;

                        // Fade in
                        activePane.style.opacity = '1';
                    }, 250);
                });
            });

            // Set initial opacity for active pane
            const activePane = document.querySelector('.tab-pane.active:not(.settings-page .tab-pane)');
            if (activePane) {
                activePane.style.opacity = '1';
            }

            // Set transition for all panes
            otherPanes.forEach(pane => {
                pane.style.transition = 'opacity 0.3s ease-in-out';
                pane.style.opacity = '0';
            });
        }
    }

    // Add ripple effect to buttons
    const rippleButtons = document.querySelectorAll('.btn:not(.modal-close)');

    rippleButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});
