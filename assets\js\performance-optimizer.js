/**
 * محسن الأداء العام - تحسين سرعة الموقع
 */

class PerformanceOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.optimizeCSS();
        this.optimizeJS();
        this.optimizeVideos();
        this.setupPreloading();
        this.optimizeScrolling();
        this.setupServiceWorker();
        this.monitorPerformance();
    }

    // تحسين CSS
    optimizeCSS() {
        // تحميل CSS بشكل غير متزامن
        const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
        cssLinks.forEach(link => {
            if (!link.hasAttribute('media')) {
                link.media = 'print';
                link.onload = function() {
                    this.media = 'all';
                };
            }
        });

        // إزالة CSS غير المستخدم (للصفحات المحددة)
        this.removeUnusedCSS();
    }

    // تحسين JavaScript
    optimizeJS() {
        // تأجيل تحميل JS غير الضروري
        const scripts = document.querySelectorAll('script[data-defer]');
        scripts.forEach(script => {
            script.defer = true;
        });

        // تحميل JS بشكل غير متزامن
        const asyncScripts = document.querySelectorAll('script[data-async]');
        asyncScripts.forEach(script => {
            script.async = true;
        });
    }

    // تحسين الفيديوهات
    optimizeVideos() {
        const videos = document.querySelectorAll('video');
        videos.forEach(video => {
            // تعطيل التحميل التلقائي
            if (!video.hasAttribute('autoplay')) {
                video.preload = 'metadata';
            }

            // ضغط الفيديو للشاشات الصغيرة
            if (window.innerWidth <= 768) {
                video.style.maxWidth = '100%';
                video.style.height = 'auto';
            }

            // إيقاف الفيديوهات خارج الشاشة
            this.setupVideoIntersection(video);
        });
    }

    // مراقبة الفيديوهات
    setupVideoIntersection(video) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (!entry.isIntersecting && !video.paused) {
                    video.pause();
                }
            });
        }, { threshold: 0.1 });

        observer.observe(video);
    }

    // إعداد التحميل المسبق
    setupPreloading() {
        // تحميل مسبق للصفحات المهمة
        const importantLinks = document.querySelectorAll('a[data-preload]');
        importantLinks.forEach(link => {
            link.addEventListener('mouseenter', () => {
                this.preloadPage(link.href);
            }, { once: true });
        });

        // تحميل مسبق للخطوط
        this.preloadFonts();

        // تحميل مسبق للصور المهمة
        this.preloadCriticalAssets();
    }

    // تحميل مسبق للصفحة
    preloadPage(url) {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = url;
        document.head.appendChild(link);
        console.log('🚀 Prefetching page:', url);
    }

    // تحميل مسبق للخطوط
    preloadFonts() {
        const fonts = [
            'assets/fonts/Cairo-Regular.woff2',
            'assets/fonts/Poppins-Regular.woff2'
        ];

        fonts.forEach(font => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'font';
            link.type = 'font/woff2';
            link.crossOrigin = 'anonymous';
            link.href = font;
            document.head.appendChild(link);
        });
    }

    // تحميل مسبق للأصول المهمة
    preloadCriticalAssets() {
        const criticalAssets = [
            'assets/img/logo.png',
            'assets/css/main.css',
            'assets/js/main.js'
        ];

        criticalAssets.forEach(asset => {
            const link = document.createElement('link');
            link.rel = 'preload';
            
            if (asset.endsWith('.css')) {
                link.as = 'style';
            } else if (asset.endsWith('.js')) {
                link.as = 'script';
            } else if (asset.match(/\.(jpg|jpeg|png|webp)$/)) {
                link.as = 'image';
            }
            
            link.href = asset;
            document.head.appendChild(link);
        });
    }

    // تحسين التمرير
    optimizeScrolling() {
        // Passive event listeners للتمرير
        let ticking = false;

        const optimizedScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    // معالجة أحداث التمرير هنا
                    this.handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', optimizedScroll, { passive: true });
    }

    // معالجة التمرير
    handleScroll() {
        // إخفاء/إظهار عناصر حسب التمرير
        const scrollY = window.scrollY;
        
        // تحسين الأداء بإخفاء العناصر البعيدة
        const sections = document.querySelectorAll('section');
        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            const isVisible = rect.top < window.innerHeight && rect.bottom > 0;
            
            if (!isVisible) {
                // تعطيل الرسوم المتحركة للعناصر غير المرئية
                section.style.willChange = 'auto';
            } else {
                section.style.willChange = 'transform';
            }
        });
    }

    // إزالة CSS غير المستخدم
    removeUnusedCSS() {
        // قائمة بالـ CSS المستخدم في الصفحة الحالية
        const currentPage = window.location.pathname;
        const unusedSelectors = [];

        // تحديد CSS غير المستخدم حسب الصفحة
        if (currentPage === '/' || currentPage === '/index.php') {
            // الصفحة الرئيسية - استخدام كل CSS
            return;
        } else if (currentPage.includes('vote')) {
            // صفحة التصويت - إزالة CSS الخاص بالأدمن
            unusedSelectors.push('.admin-', '.dashboard-');
        } else if (currentPage.includes('admin')) {
            // صفحة الأدمن - إزالة CSS الخاص بالواجهة الأمامية
            unusedSelectors.push('.hero-', '.swiper-');
        }

        // إزالة القواعد غير المستخدمة
        this.removeCSSRules(unusedSelectors);
    }

    // إزالة قواعد CSS
    removeCSSRules(selectors) {
        const styleSheets = document.styleSheets;
        
        for (let sheet of styleSheets) {
            try {
                const rules = sheet.cssRules || sheet.rules;
                for (let i = rules.length - 1; i >= 0; i--) {
                    const rule = rules[i];
                    if (rule.selectorText) {
                        const shouldRemove = selectors.some(selector => 
                            rule.selectorText.includes(selector)
                        );
                        if (shouldRemove) {
                            sheet.deleteRule(i);
                        }
                    }
                }
            } catch (e) {
                // تجاهل الأخطاء للـ stylesheets الخارجية
            }
        }
    }

    // إعداد Service Worker
    setupServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('✅ Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('❌ Service Worker registration failed:', error);
                });
        }
    }

    // مراقبة الأداء
    monitorPerformance() {
        // قياس أداء التحميل
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart;
                
                console.log('📊 Performance Metrics:');
                console.log(`Load Time: ${loadTime}ms`);
                console.log(`DOM Content Loaded: ${domContentLoaded}ms`);
                console.log(`Total Page Load: ${perfData.loadEventEnd - perfData.fetchStart}ms`);
                
                // إرسال البيانات للتحليل (اختياري)
                this.sendPerformanceData({
                    loadTime,
                    domContentLoaded,
                    totalTime: perfData.loadEventEnd - perfData.fetchStart,
                    page: window.location.pathname
                });
            }, 0);
        });
    }

    // إرسال بيانات الأداء
    sendPerformanceData(data) {
        // يمكن إرسال البيانات لخدمة تحليل الأداء
        console.log('Performance data:', data);
    }
}

// تشغيل محسن الأداء
document.addEventListener('DOMContentLoaded', function() {
    console.log('⚡ Initializing Performance Optimizer...');
    window.performanceOptimizer = new PerformanceOptimizer();
});

// تحسين الذاكرة
window.addEventListener('beforeunload', function() {
    // تنظيف المراقبين والمؤقتات
    if (window.performanceOptimizer) {
        console.log('🧹 Cleaning up performance optimizer...');
    }
});
