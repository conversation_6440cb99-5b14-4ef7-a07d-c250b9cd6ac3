/**
 * Enhanced CSS for Voting, CEO, and Team Pages
 */

/* ===== Common Styles ===== */
.page-header {
    margin-bottom: var(--spacing-lg);
    position: relative;
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.page-header h1 {
    color: var(--color-gold);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
}

.page-header h1 i {
    margin-right: var(--spacing-sm);
    background-color: rgba(212, 175, 55, 0.1);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.page-header p {
    color: var(--color-light);
    margin-bottom: 0;
}

/* Card Styles */
.card {
    background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-gray);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    position: relative;
    transition: all var(--transition-normal);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%);
    z-index: 0;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid var(--color-gray);
    padding: var(--spacing-md) var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.card-header h2 {
    color: var(--color-gold);
    font-size: 1.4rem;
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.card-header h2 i {
    margin-right: var(--spacing-sm);
    font-size: 1.2rem;
}

.card-body {
    padding: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    color: var(--color-gold);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
}

.form-group label i {
    margin-right: var(--spacing-xs);
    width: 20px;
    text-align: center;
}

.form-control {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--color-gray);
    border-radius: var(--border-radius-sm);
    color: var(--color-white);
    padding: var(--spacing-sm) var(--spacing-md);
    transition: all var(--transition-normal);
}

.form-control:focus {
    background-color: rgba(0, 0, 0, 0.3);
    border-color: var(--color-gold);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
    outline: none;
}

.form-control-file {
    background-color: transparent;
    border: none;
    padding: var(--spacing-sm) 0;
    color: var(--color-white);
}

.form-text {
    color: var(--color-light-gray);
    font-size: 0.85rem;
    margin-top: var(--spacing-xs);
}

.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -10px;
    margin-left: -10px;
}

.form-row > .form-group {
    padding-right: 10px;
    padding-left: 10px;
    flex: 1;
}

.form-actions {
    margin-top: var(--spacing-lg);
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

/* Current Image Display */
.current-image {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    text-align: center;
    border: 1px dashed var(--color-gray);
}

.current-image img {
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
    max-width: 100%;
    height: auto;
}

.current-image p {
    margin-top: var(--spacing-xs);
    margin-bottom: 0;
    color: var(--color-light-gray);
    font-size: 0.9rem;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--color-gray);
}

.table {
    width: 100%;
    margin-bottom: 0;
    color: var(--color-white);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md);
    vertical-align: middle;
    border-top: 1px solid var(--color-gray);
}

.table thead th {
    background-color: rgba(0, 0, 0, 0.3);
    border-bottom: 2px solid var(--color-gray);
    color: var(--color-gold);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color var(--transition-normal);
}

.table tbody tr:hover {
    background-color: rgba(212, 175, 55, 0.05);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.2);
}

.table-striped tbody tr:nth-of-type(odd):hover {
    background-color: rgba(212, 175, 55, 0.05);
}

/* Button Styles */
.btn {
    position: relative;
    overflow: hidden;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    text-decoration: none;
}

.btn i {
    margin-right: var(--spacing-xs);
}

.btn-primary {
    background: linear-gradient(135deg, var(--color-gold) 0%, #aa8c2c 100%);
    color: var(--color-black);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #f1e5ac 0%, var(--color-gold) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.btn-secondary {
    background-color: var(--color-dark-gray);
    color: var(--color-white);
}

.btn-secondary:hover {
    background-color: var(--color-gray);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.btn-danger {
    background-color: #dc3545;
    color: var(--color-white);
}

.btn-danger:hover {
    background-color: #bd2130;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.btn-info {
    background-color: #17a2b8;
    color: var(--color-white);
}

.btn-info:hover {
    background-color: #138496;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* Alert Styles */
.alert {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    animation: fadeIn 0.5s ease-out;
}

.alert i {
    margin-right: var(--spacing-md);
    font-size: 1.5rem;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #dc3545;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #ffc107;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border: 1px solid rgba(23, 162, 184, 0.3);
    color: #17a2b8;
}

/* No Data Message */
.no-data {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--color-light-gray);
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: ripple 0.6s linear;
    transform: scale(0);
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* RTL Support */
html[dir="rtl"] .page-header h1 i,
html[dir="rtl"] .card-header h2 i,
html[dir="rtl"] .form-group label i,
html[dir="rtl"] .btn i,
html[dir="rtl"] .alert i {
    margin-right: 0;
    margin-left: var(--spacing-sm);
}

html[dir="rtl"] .form-group label i {
    margin-left: var(--spacing-xs);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .row {
        flex-direction: column;
    }
    
    .col-md-4,
    .col-md-6,
    .col-md-8 {
        width: 100%;
    }
    
    .form-row {
        flex-direction: column;
    }
    
    .form-row > .form-group {
        width: 100%;
    }
}
