<?php
/**
 * Main entry point for the Social Media Festival website
 */

// Load session configuration
require_once 'config/session.php';

// Start session
session_start();

// Load configuration
$conn = require_once 'config/database.php';
require_once 'config/config.php';
require_once 'config/i18n.php';

// Load common functions
require_once 'includes/functions.php';

// Load visits tracking system
require_once 'includes/visits.php';

// Determine the page to load
$page = isset($_GET['page']) ? $_GET['page'] : 'home';

// Validate page name to prevent directory traversal
$page = preg_replace('/[^a-zA-Z0-9_-]/', '', $page);

// Define allowed pages
$allowed_pages = [
    'home', 'nominees', 'nominee-profile', 'compare', 'sponsors',
    'celebrities', 'celebrity', 'stories', 'login', 'register', 'profile',
    'reset-password', 'vote', 'about', 'contact', 'nominee', 'privacy', 'terms' , 'influencers'
];

// Check if page exists and is allowed
if (!in_array($page, $allowed_pages) || !file_exists("includes/pages/{$page}.php")) {
    $page = 'home'; // Default to home if page doesn't exist
}

// Include header
include 'includes/header.php';

// Record the visit
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
record_visit($page, $user_id);

// Include the requested page
include "includes/pages/{$page}.php";

// Include footer
include 'includes/footer.php';

// Close database connection
$conn->close();
