<?php
/**
 * Invitations Management Page
 */

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Process form submission for adding/editing invitation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save') {
    $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    $title = $_POST['title'] ?? '';
    $title_ar = $_POST['title_ar'] ?? '';
    $display_order = (int)($_POST['display_order'] ?? 0);
    
    // Handle image upload
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/uploads/';
        $temp_name = $_FILES['image']['tmp_name'];
        $original_name = $_FILES['image']['name'];
        $extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $new_name = 'invitation_' . time() . '.' . $extension;
        
        if (move_uploaded_file($temp_name, $upload_dir . $new_name)) {
            $image = $new_name;
        }
    }
    
    if ($id > 0) {
        // Update existing invitation
        $update_sql = "UPDATE invitations SET 
                      title = ?, 
                      title_ar = ?, 
                      display_order = ?";
        
        // Only update image if a new one was uploaded
        if (!empty($image)) {
            $update_sql .= ", image = ?";
        }
        
        $update_sql .= " WHERE id = ?";
        
        $stmt = $conn->prepare($update_sql);
        
        if (!empty($image)) {
            $stmt->bind_param("ssisi", $title, $title_ar, $display_order, $image, $id);
        } else {
            $stmt->bind_param("ssii", $title, $title_ar, $display_order, $id);
        }
        
        if ($stmt->execute()) {
            $success_message = "تم تحديث الدعوة بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء تحديث البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    } else {
        // Insert new invitation
        $insert_sql = "INSERT INTO invitations (title, title_ar, display_order, image) 
                      VALUES (?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("ssis", $title, $title_ar, $display_order, $image);
        
        if ($stmt->execute()) {
            $success_message = "تم إضافة الدعوة بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء إضافة البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    }
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $id = (int)$_POST['id'];
    
    // Get the image filename before deleting
    $image_sql = "SELECT image FROM invitations WHERE id = ?";
    $stmt = $conn->prepare($image_sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($image_filename);
    $stmt->fetch();
    $stmt->close();
    
    // Delete the invitation
    $delete_sql = "DELETE FROM invitations WHERE id = ?";
    $stmt = $conn->prepare($delete_sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        // Delete the image file if it exists
        if (!empty($image_filename)) {
            $image_path = '../assets/uploads/' . $image_filename;
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }
        
        $success_message = "تم حذف الدعوة بنجاح";
    } else {
        $error_message = "حدث خطأ أثناء حذف البيانات: " . $stmt->error;
    }
    
    $stmt->close();
}

// Get invitations
$invitations = get_invitations();

// Get invitation for editing
$edit_invitation = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    
    foreach ($invitations as $invitation) {
        if ($invitation['id'] == $edit_id) {
            $edit_invitation = $invitation;
            break;
        }
    }
}
?>

<div class="page-header">
    <h1><i class="fas fa-envelope-open-text"></i> إدارة الدعوات</h1>
    <p>إضافة وتعديل وحذف صور الدعوات</p>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success">
    <?php echo $success_message; ?>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger">
    <?php echo $error_message; ?>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h2><?php echo $edit_invitation ? 'تعديل الدعوة' : 'إضافة دعوة جديدة'; ?></h2>
            </div>
            <div class="card-body">
                <form action="" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="save">
                    <?php if ($edit_invitation): ?>
                    <input type="hidden" name="id" value="<?php echo $edit_invitation['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="title">العنوان (الإنجليزية)</label>
                        <input type="text" id="title" name="title" class="form-control" value="<?php echo $edit_invitation ? htmlspecialchars($edit_invitation['title']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="title_ar">العنوان (العربية)</label>
                        <input type="text" id="title_ar" name="title_ar" class="form-control" value="<?php echo $edit_invitation ? htmlspecialchars($edit_invitation['title_ar']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="display_order">ترتيب العرض</label>
                        <input type="number" id="display_order" name="display_order" class="form-control" value="<?php echo $edit_invitation ? (int)$edit_invitation['display_order'] : count($invitations) + 1; ?>" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="image">صورة الدعوة</label>
                        <?php if ($edit_invitation && !empty($edit_invitation['image'])): ?>
                        <div class="current-image">
                            <img src="<?php echo asset_url('uploads/' . $edit_invitation['image']); ?>" alt="<?php echo htmlspecialchars($edit_invitation['title']); ?>" style="max-width: 100%; margin-bottom: 10px;">
                            <p>الصورة الحالية</p>
                        </div>
                        <?php endif; ?>
                        <input type="file" id="image" name="image" class="form-control-file" <?php echo !$edit_invitation ? 'required' : ''; ?>>
                        <small class="form-text text-muted">يفضل صورة بجودة عالية</small>
                    </div>
                    
                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                        <?php if ($edit_invitation): ?>
                        <a href="index.php?page=invitations" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h2>الدعوات</h2>
            </div>
            <div class="card-body">
                <?php if (empty($invitations)): ?>
                <div class="alert alert-info">
                    لا توجد دعوات حالياً. قم بإضافة دعوات جديدة.
                </div>
                <?php else: ?>
                <div class="invitations-grid">
                    <?php foreach ($invitations as $invitation): ?>
                    <div class="invitation-card">
                        <div class="invitation-card-image">
                            <img src="<?php echo asset_url('uploads/' . $invitation['image']); ?>" alt="<?php echo htmlspecialchars($invitation['title']); ?>">
                        </div>
                        <div class="invitation-card-content">
                            <h3><?php echo htmlspecialchars($invitation['title']); ?></h3>
                            <p>الترتيب: <?php echo (int)$invitation['display_order']; ?></p>
                            <div class="invitation-card-actions">
                                <a href="index.php?page=invitations&edit=<?php echo $invitation['id']; ?>" class="btn btn-sm btn-primary"><i class="fas fa-edit"></i> تعديل</a>
                                <form action="" method="post" class="d-inline delete-form" onsubmit="return confirm('هل أنت متأكد من حذف هذه الدعوة؟');">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="id" value="<?php echo $invitation['id']; ?>">
                                    <button type="submit" class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> حذف</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.invitations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.invitation-card {
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.invitation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.invitation-card-image {
    height: 200px;
    overflow: hidden;
}

.invitation-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.invitation-card-content {
    padding: 15px;
}

.invitation-card-content h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.invitation-card-actions {
    margin-top: 15px;
    display: flex;
    justify-content: space-between;
}
</style>
