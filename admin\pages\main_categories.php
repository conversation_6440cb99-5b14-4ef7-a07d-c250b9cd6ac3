<?php
/**
 * Main Categories Management Page
 */

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Process form submission for adding/editing main category
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save') {
    $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    $name = $_POST['name'] ?? '';
    $name_ar = $_POST['name_ar'] ?? '';
    $description = $_POST['description'] ?? '';
    $description_ar = $_POST['description_ar'] ?? '';
    $display_order = (int)($_POST['display_order'] ?? 0);
    
    // Handle image upload
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/uploads/';
        $temp_name = $_FILES['image']['tmp_name'];
        $original_name = $_FILES['image']['name'];
        $extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $new_name = 'category_' . time() . '.' . $extension;
        
        if (move_uploaded_file($temp_name, $upload_dir . $new_name)) {
            $image = $new_name;
        }
    }
    
    if ($id > 0) {
        // Update existing main category
        $update_sql = "UPDATE main_categories SET 
                      name = ?, 
                      name_ar = ?, 
                      description = ?, 
                      description_ar = ?, 
                      display_order = ?";
        
        // Only update image if a new one was uploaded
        if (!empty($image)) {
            $update_sql .= ", image = ?";
        }
        
        $update_sql .= " WHERE id = ?";
        
        $stmt = $conn->prepare($update_sql);
        
        if (!empty($image)) {
            $stmt->bind_param("ssssis", $name, $name_ar, $description, $description_ar, $display_order, $image, $id);
        } else {
            $stmt->bind_param("ssssi", $name, $name_ar, $description, $description_ar, $display_order, $id);
        }
        
        if ($stmt->execute()) {
            $success_message = "تم تحديث الفئة الرئيسية بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء تحديث البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    } else {
        // Insert new main category
        $insert_sql = "INSERT INTO main_categories (name, name_ar, description, description_ar, display_order, image) 
                      VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("ssssis", $name, $name_ar, $description, $description_ar, $display_order, $image);
        
        if ($stmt->execute()) {
            $success_message = "تم إضافة الفئة الرئيسية بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء إضافة البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    }
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $id = (int)$_POST['id'];
    
    // Get the image filename before deleting
    $image_sql = "SELECT image FROM main_categories WHERE id = ?";
    $stmt = $conn->prepare($image_sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($image_filename);
    $stmt->fetch();
    $stmt->close();
    
    // Delete the main category
    $delete_sql = "DELETE FROM main_categories WHERE id = ?";
    $stmt = $conn->prepare($delete_sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        // Delete the image file if it exists
        if (!empty($image_filename)) {
            $image_path = '../assets/uploads/' . $image_filename;
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }
        
        $success_message = "تم حذف الفئة الرئيسية بنجاح";
    } else {
        $error_message = "حدث خطأ أثناء حذف البيانات: " . $stmt->error;
    }
    
    $stmt->close();
}

// Get main categories
$main_categories = get_main_categories();

// Get main category for editing
$edit_category = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    
    foreach ($main_categories as $category) {
        if ($category['id'] == $edit_id) {
            $edit_category = $category;
            break;
        }
    }
}
?>

<div class="page-header">
    <h1><i class="fas fa-folder"></i> إدارة الفئات الرئيسية</h1>
    <p>إضافة وتعديل وحذف الفئات الرئيسية للتصويت</p>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success">
    <?php echo $success_message; ?>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger">
    <?php echo $error_message; ?>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h2><?php echo $edit_category ? 'تعديل الفئة الرئيسية' : 'إضافة فئة رئيسية جديدة'; ?></h2>
            </div>
            <div class="card-body">
                <form action="" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="save">
                    <?php if ($edit_category): ?>
                    <input type="hidden" name="id" value="<?php echo $edit_category['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="name">الاسم (الإنجليزية)</label>
                        <input type="text" id="name" name="name" class="form-control" value="<?php echo $edit_category ? htmlspecialchars($edit_category['name']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="name_ar">الاسم (العربية)</label>
                        <input type="text" id="name_ar" name="name_ar" class="form-control" value="<?php echo $edit_category ? htmlspecialchars($edit_category['name_ar']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">الوصف (الإنجليزية)</label>
                        <textarea id="description" name="description" class="form-control" rows="3"><?php echo $edit_category ? htmlspecialchars($edit_category['description']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="description_ar">الوصف (العربية)</label>
                        <textarea id="description_ar" name="description_ar" class="form-control" rows="3"><?php echo $edit_category ? htmlspecialchars($edit_category['description_ar']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="display_order">ترتيب العرض</label>
                        <input type="number" id="display_order" name="display_order" class="form-control" value="<?php echo $edit_category ? (int)$edit_category['display_order'] : count($main_categories) + 1; ?>" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="image">الصورة</label>
                        <?php if ($edit_category && !empty($edit_category['image'])): ?>
                        <div class="current-image">
                            <img src="<?php echo asset_url('uploads/' . $edit_category['image']); ?>" alt="<?php echo htmlspecialchars($edit_category['name']); ?>" style="max-width: 100px; margin-bottom: 10px;">
                            <p>الصورة الحالية</p>
                        </div>
                        <?php endif; ?>
                        <input type="file" id="image" name="image" class="form-control-file" <?php echo !$edit_category ? 'required' : ''; ?>>
                        <small class="form-text text-muted">يفضل صورة دائرية بأبعاد 200×200 بكسل</small>
                    </div>
                    
                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                        <?php if ($edit_category): ?>
                        <a href="index.php?page=main_categories" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h2>الفئات الرئيسية</h2>
            </div>
            <div class="card-body">
                <?php if (empty($main_categories)): ?>
                <div class="alert alert-info">
                    لا توجد فئات رئيسية حالياً. قم بإضافة فئات جديدة.
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>الترتيب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($main_categories as $category): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($category['image'])): ?>
                                    <img src="<?php echo asset_url('uploads/' . $category['image']); ?>" alt="<?php echo htmlspecialchars($category['name']); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%;">
                                    <?php else: ?>
                                    <div class="no-image">لا توجد صورة</div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($current_lang === 'ar' ? $category['name_ar'] : $category['name']); ?></td>
                                <td><?php echo (int)$category['display_order']; ?></td>
                                <td>
                                    <a href="index.php?page=main_categories&edit=<?php echo $category['id']; ?>" class="btn btn-sm btn-primary"><i class="fas fa-edit"></i> تعديل</a>
                                    <a href="index.php?page=subcategories&main_category=<?php echo $category['id']; ?>" class="btn btn-sm btn-info"><i class="fas fa-list"></i> الفئات الفرعية</a>
                                    <form action="" method="post" class="d-inline delete-form" onsubmit="return confirm('هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع الفئات الفرعية والمرشحين المرتبطين بها.');">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="<?php echo $category['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> حذف</button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
