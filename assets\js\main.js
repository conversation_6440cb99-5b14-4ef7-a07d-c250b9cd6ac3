/**
 * Main JavaScript for Social Media Festival
 */

document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const menuToggle = document.querySelector('.menu-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (menuToggle && navMenu) {
        menuToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            document.body.classList.toggle('menu-open');

            // Add animation to menu items
            const menuItems = navMenu.querySelectorAll('li');
            if (navMenu.classList.contains('active')) {
                menuItems.forEach((item, index) => {
                    gsap.from(item, {
                        opacity: 0,
                        y: 20,
                        delay: 0.1 + (index * 0.1),
                        duration: 0.4,
                        ease: 'power2.out'
                    });
                });
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (navMenu.classList.contains('active') &&
                !navMenu.contains(event.target) &&
                !menuToggle.contains(event.target)) {
                navMenu.classList.remove('active');
                document.body.classList.remove('menu-open');
            }
        });
    }

    // Story video player
    const storyVideos = document.querySelectorAll('.story-video');

    storyVideos.forEach(storyVideo => {
        const video = storyVideo.querySelector('video');
        const playButton = storyVideo.querySelector('.play-button');

        if (video && playButton) {
            playButton.addEventListener('click', function() {
                if (video.paused) {
                    // Pause all other videos first
                    document.querySelectorAll('.story-video video').forEach(v => {
                        if (v !== video) {
                            v.pause();
                            v.closest('.story-video').classList.remove('playing');
                        }
                    });

                    // Play this video
                    video.play();
                    storyVideo.classList.add('playing');
                    playButton.innerHTML = '<i class="fas fa-pause"></i>';
                } else {
                    video.pause();
                    storyVideo.classList.remove('playing');
                    playButton.innerHTML = '<i class="fas fa-play"></i>';
                }
            });

            video.addEventListener('ended', function() {
                storyVideo.classList.remove('playing');
                playButton.innerHTML = '<i class="fas fa-play"></i>';
            });
        }
    });

    // Sticky header
    const header = document.querySelector('.site-header');
    let lastScrollTop = 0;

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (scrollTop > 100) {
            header.classList.add('sticky');

            if (scrollTop > lastScrollTop) {
                // Scrolling down
                header.classList.add('hide');
            } else {
                // Scrolling up
                header.classList.remove('hide');
            }
        } else {
            header.classList.remove('sticky');
            header.classList.remove('hide');
        }

        lastScrollTop = scrollTop;
    });

    // Initialize animations
    initAnimations();
});

/**
 * Initialize animations using GSAP
 */
function initAnimations() {
    // Check if GSAP is loaded
    if (typeof gsap === 'undefined') {
        console.warn('GSAP is not loaded');
        return;
    }

    // Register ScrollTrigger plugin
    if (typeof ScrollTrigger !== 'undefined') {
        gsap.registerPlugin(ScrollTrigger);
    }

    // Animate section headers
    gsap.utils.toArray('.section-header').forEach(header => {
        gsap.from(header, {
            y: 50,
            opacity: 0,
            duration: 1,
            scrollTrigger: {
                trigger: header,
                start: 'top 80%',
                toggleActions: 'play none none none'
            }
        });
    });

    // Animate category items
    gsap.utils.toArray('.category-item').forEach((item, i) => {
        gsap.from(item, {
            y: 100,
            opacity: 0,
            duration: 0.8,
            delay: i * 0.1,
            scrollTrigger: {
                trigger: '.categories-carousel',
                start: 'top 85%',
                toggleActions: 'play none none none'
            }
        });
    });

    // Animate story items
    gsap.utils.toArray('.story-item').forEach((item, i) => {
        gsap.from(item, {
            x: 100,
            opacity: 0,
            duration: 0.8,
            delay: i * 0.1,
            scrollTrigger: {
                trigger: item,
                start: 'top 85%',
                toggleActions: 'play none none none'
            }
        });
    });

    // Celebrities section animations
    const celebritiesSection = document.querySelector('.celebrities-section');
    if (celebritiesSection) {
        // Create a timeline for the celebrities section
        const celebritiesTl = gsap.timeline({
            scrollTrigger: {
                trigger: '.celebrities-section',
                start: 'top 80%',
                end: 'bottom 30%',
                toggleActions: 'play none none reverse'
            }
        });

        // Animate section header
        celebritiesTl
            .from('.celebrities-section .section-header', {
                y: 30,
                opacity: 0,
                duration: 0.8,
                ease: 'power3.out'
            });

        // Swiper for podcasts (مثل الريلز)
        new Swiper('.podcasts-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 20,
            loop: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
            },
            speed: 3000,
            freeMode: true,
            grabCursor: true,
            breakpoints: {
                320: {
                    slidesPerView: 1.2,
                    spaceBetween: 15,
                },
                576: {
                    slidesPerView: 1.8,
                    spaceBetween: 15,
                },
                768: {
                    slidesPerView: 2.5,
                    spaceBetween: 20,
                },
                992: {
                    slidesPerView: 3.5,
                    spaceBetween: 20,
                },
                1200: {
                    slidesPerView: 4.2,
                    spaceBetween: 25,
                }
            }
        });

        // Swiper for Advertising Coverage
        new Swiper('.advertising-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 25,
            loop: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
            },
            speed: 4000,
            freeMode: true,
            grabCursor: true,
            breakpoints: {
                320: {
                    slidesPerView: 1.1,
                    spaceBetween: 15,
                },
                576: {
                    slidesPerView: 1.5,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 2.2,
                    spaceBetween: 25,
                },
                992: {
                    slidesPerView: 3.2,
                    spaceBetween: 25,
                },
                1200: {
                    slidesPerView: 4,
                    spaceBetween: 30,
                }
            }
        });

        // Swiper for TV Coverage (reverse direction)
        new Swiper('.tv-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 25,
            loop: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
                reverseDirection: true,
            },
            speed: 3500,
            freeMode: true,
            grabCursor: true,
            breakpoints: {
                320: {
                    slidesPerView: 1.1,
                    spaceBetween: 15,
                },
                576: {
                    slidesPerView: 1.5,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 2.2,
                    spaceBetween: 25,
                },
                992: {
                    slidesPerView: 3.2,
                    spaceBetween: 25,
                },
                1200: {
                    slidesPerView: 4,
                    spaceBetween: 30,
                }
            }
        });

        // Swiper for Platinum Sponsors
        new Swiper('.platinum-sponsors-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
            },
            speed: 5000,
            freeMode: true,
            grabCursor: true,
            breakpoints: {
                320: {
                    slidesPerView: 1.2,
                    spaceBetween: 20,
                },
                576: {
                    slidesPerView: 1.8,
                    spaceBetween: 25,
                },
                768: {
                    slidesPerView: 2.5,
                    spaceBetween: 30,
                },
                992: {
                    slidesPerView: 3.5,
                    spaceBetween: 30,
                },
                1200: {
                    slidesPerView: 4.5,
                    spaceBetween: 35,
                }
            }
        });

        // Swiper for Gold Sponsors (reverse direction)
        new Swiper('.gold-sponsors-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 25,
            loop: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
                reverseDirection: true,
            },
            speed: 4500,
            freeMode: true,
            grabCursor: true,
            breakpoints: {
                320: {
                    slidesPerView: 1.3,
                    spaceBetween: 15,
                },
                576: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 3,
                    spaceBetween: 25,
                },
                992: {
                    slidesPerView: 4,
                    spaceBetween: 25,
                },
                1200: {
                    slidesPerView: 5,
                    spaceBetween: 30,
                }
            }
        });

        // Swiper for Silver Sponsors
        new Swiper('.silver-sponsors-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 20,
            loop: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
            },
            speed: 4000,
            freeMode: true,
            grabCursor: true,
            breakpoints: {
                320: {
                    slidesPerView: 1.5,
                    spaceBetween: 15,
                },
                576: {
                    slidesPerView: 2.2,
                    spaceBetween: 18,
                },
                768: {
                    slidesPerView: 3.5,
                    spaceBetween: 20,
                },
                992: {
                    slidesPerView: 4.5,
                    spaceBetween: 20,
                },
                1200: {
                    slidesPerView: 6,
                    spaceBetween: 25,
                }
            }
        });

        // Swiper for Photo Gallery
        new Swiper('.gallery-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
            },
            speed: 6000,
            freeMode: true,
            grabCursor: true,
            breakpoints: {
                320: {
                    slidesPerView: 1.1,
                    spaceBetween: 20,
                },
                576: {
                    slidesPerView: 1.5,
                    spaceBetween: 25,
                },
                768: {
                    slidesPerView: 2.2,
                    spaceBetween: 30,
                },
                992: {
                    slidesPerView: 2.8,
                    spaceBetween: 30,
                },
                1200: {
                    slidesPerView: 3.5,
                    spaceBetween: 35,
                }
            }
        });

        // Simple Swiper for celebrities
        new Swiper('.celebrities-swiper', {
            slidesPerView: 'auto',
            spaceBetween: 20,
            loop: true,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
            },
            speed: 3000,
            freeMode: true,
            grabCursor: true,
            breakpoints: {
                320: {
                    slidesPerView: 1.2,
                },
                576: {
                    slidesPerView: 2.2,
                },
                768: {
                    slidesPerView: 3.2,
                },
                992: {
                    slidesPerView: 4.2,
                },
                1200: {
                    slidesPerView: 5.2,
                }
            }
        });

        // Add hover effects with GSAP
        gsap.utils.toArray('.celebrity-item').forEach(item => {
            item.addEventListener('mouseenter', () => {
                gsap.to(item.querySelector('.celebrity-image img'), {
                    scale: 1.15,
                    duration: 0.4,
                    ease: 'power2.out'
                });
                gsap.to(item.querySelector('.celebrity-info h3'), {
                    y: -3,
                    color: '#ffd700',
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });

            item.addEventListener('mouseleave', () => {
                gsap.to(item.querySelector('.celebrity-image img'), {
                    scale: 1,
                    duration: 0.4,
                    ease: 'power2.out'
                });
                gsap.to(item.querySelector('.celebrity-info h3'), {
                    y: 0,
                    color: 'var(--color-gold)',
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });
        });
    }

    // Initialize Swiper for stories
    const storiesSwiper = new Swiper('.stories-swiper', {
        slidesPerView: 'auto',
        spaceBetween: 0,
        centeredSlides: false,
        loop: true,
        autoplay: {
            delay: 0,
            disableOnInteraction: false,
        },
        speed: 5000,
        freeMode: true,
        freeModeMomentum: false,
        grabCursor: true,
        breakpoints: {
            320: {
                slidesPerView: 1.2,
            },
            576: {
                slidesPerView: 2.2,
            },
            768: {
                slidesPerView: 3.2,
            },
            992: {
                slidesPerView: 3.5,
            }
        }
    });

    // Fixed video play functionality
    console.log('Initializing video functionality...');

    // Wait for page to fully load
    window.addEventListener('load', function() {
        console.log('Page loaded, setting up video controls...');

        // Function to pause all videos
        function pauseAllVideos() {
            document.querySelectorAll('video').forEach(video => {
                if (!video.paused) {
                    video.pause();
                }
            });

            // Show all play buttons
            document.querySelectorAll('.play-button').forEach(btn => {
                btn.style.display = 'flex';
            });
        }

        // Set up click handlers for play buttons
        function setupPlayButtons() {
            const playButtons = document.querySelectorAll('.play-button');
            console.log('Found play buttons:', playButtons.length);

            playButtons.forEach(function(button, index) {
                console.log('Setting up button', index, button);

                // Remove any existing listeners
                button.removeEventListener('click', handlePlayClick);

                // Add new listener
                button.addEventListener('click', handlePlayClick);
            });
        }

        function handlePlayClick(e) {
            e.preventDefault();
            e.stopPropagation();

            const button = e.currentTarget;
            const reelId = button.getAttribute('data-reel-id');
            const podcastId = button.getAttribute('data-podcast-id');

            console.log('Play button clicked!');
            console.log('Reel ID:', reelId);
            console.log('Podcast ID:', podcastId);

            let video = null;

            // Find the corresponding video
            if (reelId) {
                video = document.querySelector(`video[data-reel-id="${reelId}"]`);
                console.log('Looking for reel video:', `video[data-reel-id="${reelId}"]`);
            } else if (podcastId) {
                video = document.querySelector(`video[data-podcast-id="${podcastId}"]`);
                console.log('Looking for podcast video:', `video[data-podcast-id="${podcastId}"]`);
            }

            console.log('Found video element:', video);
            console.log('Video src:', video ? video.src : 'No video found');

            if (video) {
                if (video.paused) {
                    console.log('Video is paused, attempting to play...');

                    // Pause all other videos first
                    pauseAllVideos();

                    // Try to play this video
                    const playPromise = video.play();

                    if (playPromise !== undefined) {
                        playPromise.then(function() {
                            console.log('✅ Video playing successfully!');
                            button.style.display = 'none';
                        }).catch(function(error) {
                            console.error('❌ Video play failed:', error);
                            console.error('Error details:', error.message);
                            alert('فشل في تشغيل الفيديو: ' + error.message);
                        });
                    }
                } else {
                    console.log('Video is playing, pausing...');
                    video.pause();
                    button.style.display = 'flex';
                }
            } else {
                console.error('❌ Video element not found!');
                console.log('Available videos:');
                document.querySelectorAll('video').forEach(function(v, i) {
                    console.log(`Video ${i}:`, v.getAttribute('data-reel-id'), v.getAttribute('data-podcast-id'), v.src);
                });
                alert('لم يتم العثور على عنصر الفيديو');
            }
        }

        // Initial setup
        setupPlayButtons();

        // Re-setup when new content is loaded (for dynamic content)
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length > 0) {
                    setupPlayButtons();
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Handle video ended - show play button again
        document.addEventListener('ended', function(e) {
            if (e.target.tagName === 'VIDEO') {
                const video = e.target;
                const reelId = video.getAttribute('data-reel-id');
                const podcastId = video.getAttribute('data-podcast-id');

                let button = null;
                if (reelId) {
                    button = document.querySelector(`.play-button[data-reel-id="${reelId}"]`);
                } else if (podcastId) {
                    button = document.querySelector(`.play-button[data-podcast-id="${podcastId}"]`);
                }

                if (button) {
                    button.style.display = 'flex';
                }

                video.currentTime = 0;
                console.log('Video ended, reset to beginning');
            }
        }, true);

        // Handle direct video clicks
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'VIDEO') {
                const video = e.target;
                const reelId = video.getAttribute('data-reel-id');
                const podcastId = video.getAttribute('data-podcast-id');

                let button = null;
                if (reelId) {
                    button = document.querySelector(`.play-button[data-reel-id="${reelId}"]`);
                } else if (podcastId) {
                    button = document.querySelector(`.play-button[data-podcast-id="${podcastId}"]`);
                }

                if (button) {
                    handlePlayClick({ currentTarget: button, preventDefault: function(){}, stopPropagation: function(){} });
                }
            }
        });

        // Initialize all videos after a short delay
        setTimeout(function() {
            console.log('Initializing videos...');
            document.querySelectorAll('video').forEach(function(video, index) {
                console.log(`Initializing video ${index}:`, video.src);

                video.load();
                video.pause();
                video.currentTime = 0;
                video.muted = false; // Ensure not muted
                video.volume = 1.0;  // Full volume

                video.addEventListener('loadeddata', function() {
                    console.log('✅ Video loaded successfully:', this.src);
                });

                video.addEventListener('error', function() {
                    console.error('❌ Video loading error:', this.src, this.error);
                });

                video.addEventListener('canplay', function() {
                    console.log('✅ Video can play:', this.src);
                });
            });
        }, 2000);
    });

    // Initialize Swiper for categories
    const categoriesSwiper = new Swiper('.categories-swiper', {
        slidesPerView: 'auto',
        spaceBetween: 0,
        centeredSlides: false,
        loop: true,
        autoplay: {
            delay: 0,
            disableOnInteraction: false,
        },
        speed: 5000,
        freeMode: true,
        freeModeMomentum: false,
        grabCursor: true,
        breakpoints: {
            320: {
                slidesPerView: 1.2,
            },
            576: {
                slidesPerView: 2.2,
            },
            768: {
                slidesPerView: 3.2,
            },
            992: {
                slidesPerView: 4.2,
            },
            1200: {
                slidesPerView: 5.2,
            }
        }
    });

    // Initialize Swiper for team members
    const teamSwiper = new Swiper('.team-swiper', {
        slidesPerView: 'auto',
        spaceBetween: 0,
        centeredSlides: false,
        loop: true,
        autoplay: {
            delay: 0,
            disableOnInteraction: false,
        },
        speed: 6000,
        freeMode: true,
        freeModeMomentum: false,
        grabCursor: true,
        breakpoints: {
            320: {
                slidesPerView: 1.2,
            },
            576: {
                slidesPerView: 2.2,
            },
            768: {
                slidesPerView: 3.2,
            },
            992: {
                slidesPerView: 4.2,
            },
            1200: {
                slidesPerView: 5.2,
            }
        }
    });

    // Initialize Sponsors Swipers (Original)

    // Animate sponsor items
    gsap.utils.toArray('.sponsor-item').forEach((item, i) => {
        gsap.from(item, {
            scale: 0.8,
            opacity: 0,
            duration: 0.6,
            delay: i * 0.05,
            scrollTrigger: {
                trigger: item,
                start: 'top 85%',
                toggleActions: 'play none none none'
            }
        });
    });

    // Initialize Swiper for invitations
    const invitationsSwiper = new Swiper('.invitations-swiper', {
        slidesPerView: 'auto',
        spaceBetween: 0,
        centeredSlides: false,
        loop: true,
        autoplay: {
            delay: 0,
            disableOnInteraction: false,
        },
        speed: 7000,
        freeMode: true,
        freeModeMomentum: false,
        grabCursor: true,
        breakpoints: {
            320: {
                slidesPerView: 1.2,
            },
            576: {
                slidesPerView: 2.2,
            },
            768: {
                slidesPerView: 3.2,
            },
            992: {
                slidesPerView: 3.5,
            }
        }
    });

    // Invitations Lightbox
    const invitationItems = document.querySelectorAll('.invitation-item');
    const lightbox = document.querySelector('.invitation-lightbox');

    // Check if lightbox exists before accessing its properties
    if (lightbox) {
        const lightboxImg = lightbox.querySelector('img');
        const closeLightbox = document.querySelector('.close-lightbox');

        invitationItems.forEach(item => {
            item.addEventListener('click', () => {
                // Pause the Swiper autoplay
                if (invitationsSwiper && invitationsSwiper.autoplay) {
                    invitationsSwiper.autoplay.stop();
                }

                const imgSrc = item.getAttribute('data-image');
                lightboxImg.src = imgSrc;
                lightbox.style.display = 'flex';
                document.body.style.overflow = 'hidden'; // Prevent scrolling
            });
        });

        closeLightbox.addEventListener('click', () => {
            lightbox.style.display = 'none';
            document.body.style.overflow = ''; // Re-enable scrolling

            // Restart the Swiper autoplay
            if (invitationsSwiper && invitationsSwiper.autoplay) {
                invitationsSwiper.autoplay.start();
            }
        });

        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                lightbox.style.display = 'none';
                document.body.style.overflow = ''; // Re-enable scrolling

                // Restart the Swiper autoplay
                if (invitationsSwiper && invitationsSwiper.autoplay) {
                    invitationsSwiper.autoplay.start();
                }
            }
        });
    }

    // Enhanced Swiper for influencers
    const influencersSwiper = new Swiper('.influencers-swiper', {
        slidesPerView: 1,
        centeredSlides: false,
        loop: true,
        autoplay: {
            delay: 4000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
            reverseDirection: false, // اتجاه السكرول العادي
        },
        speed: 800,
        grabCursor: true,
        effect: 'slide',
        freeMode: false, // تعطيل الوضع الحر لضمان السكرول المنتظم
        watchSlidesProgress: true, // مراقبة تقدم الشرائح
        watchSlidesVisibility: true, // مراقبة رؤية الشرائح
        pagination: {
            el: '.influencers-swiper .swiper-pagination',
            clickable: true,
            dynamicBullets: true,
        },
        navigation: {
            nextEl: '.influencers-swiper .swiper-button-next',
            prevEl: '.influencers-swiper .swiper-button-prev',
        },
        breakpoints: {
            320: {
                slidesPerView: 1,
            },
            576: {
                slidesPerView: 2,

            },
            768: {
                slidesPerView: 3,

            },
            992: {
                slidesPerView: 4,

            },
            1200: {
                slidesPerView: 5,

            }
        },
        on: {
            init: function() {
                // Add entrance animations
                gsap.from('.influencer-item', {
                    duration: 0.8,
                    y: 50,
                    opacity: 0,
                    stagger: 0.1,
                    ease: 'power2.out'
                });
            },
            slideChange: function() {
                // Add slide change animations
                gsap.from('.swiper-slide-active .influencer-item', {
                    duration: 0.6,
                    scale: 0.9,
                    opacity: 0.8,
                    ease: 'power2.out'
                });
            }
        }
    });

    // Add hover effects for influencers with GSAP
    gsap.utils.toArray('.influencer-item').forEach(item => {
        item.addEventListener('mouseenter', () => {
            gsap.to(item.querySelector('.influencer-image img'), {
                scale: 1.1,
                duration: 0.4,
                ease: 'power2.out'
            });
            gsap.to(item.querySelector('.influence-badge'), {
                scale: 1.1,
                duration: 0.3,
                ease: 'power2.out'
            });
            gsap.to(item.querySelector('.influencer-info h3'), {
                y: -3,
                color: '#ffd700',
                duration: 0.3,
                ease: 'power2.out'
            });
        });

        item.addEventListener('mouseleave', () => {
            gsap.to(item.querySelector('.influencer-image img'), {
                scale: 1,
                duration: 0.4,
                ease: 'power2.out'
            });
            gsap.to(item.querySelector('.influence-badge'), {
                scale: 1,
                duration: 0.3,
                ease: 'power2.out'
            });
            gsap.to(item.querySelector('.influencer-info h3'), {
                y: 0,
                color: 'var(--color-white)',
                duration: 0.3,
                ease: 'power2.out'
            });
        });
    });

    // Enhanced hover effects for influencers
    gsap.utils.toArray('.influencer-item').forEach(item => {
        const tl = gsap.timeline({ paused: true });

        tl.to(item, {
            duration: 0.3,
            y: -10,
            scale: 1.02,
            boxShadow: '0 25px 50px rgba(0, 0, 0, 0.4)',
            ease: 'power2.out'
        })
        .to(item.querySelector('.influence-badge'), {
            duration: 0.3,
            scale: 1.1,
            rotation: 5,
            ease: 'power2.out'
        }, 0)
        .to(item.querySelector('.influencer-image img'), {
            duration: 0.3,
            scale: 1.1,
            ease: 'power2.out'
        }, 0);

        item.addEventListener('mouseenter', () => tl.play());
        item.addEventListener('mouseleave', () => tl.reverse());
    });

    // Animate stats on scroll
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const statNumbers = entry.target.querySelectorAll('.stat-number');
                statNumbers.forEach(stat => {
                    const finalValue = stat.textContent.trim();

                    // Skip animation if the value is already correct or if it's 0
                    if (!finalValue || finalValue === '0' || finalValue === '0.0M') {
                        return;
                    }

                    // Parse the numeric value correctly
                    let numericValue = 0;
                    let suffix = '';

                    if (finalValue.includes('M')) {
                        numericValue = parseFloat(finalValue.replace('M', '')) * 1000000;
                        suffix = 'M';
                    } else if (finalValue.includes('K')) {
                        numericValue = parseFloat(finalValue.replace('K', '')) * 1000;
                        suffix = 'K';
                    } else {
                        numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
                    }

                    // Only animate if we have a valid number
                    if (numericValue > 0) {
                        gsap.fromTo(stat, {
                            textContent: 0
                        }, {
                            textContent: numericValue,
                            duration: 2,
                            ease: 'power2.out',
                            snap: { textContent: 1 },
                            onUpdate: function() {
                                const currentValue = Math.round(this.targets()[0].textContent);
                                if (suffix === 'M') {
                                    stat.textContent = (currentValue / 1000000).toFixed(1) + 'M';
                                } else if (suffix === 'K') {
                                    stat.textContent = (currentValue / 1000).toFixed(1) + 'K';
                                } else {
                                    stat.textContent = currentValue.toLocaleString();
                                }
                            }
                        });
                    }
                });
                statsObserver.unobserve(entry.target);
            }
        });
    });

    const statsBar = document.querySelector('.stats-bar');
    if (statsBar) {
        statsObserver.observe(statsBar);
    }

    // Animate floating icons
    gsap.utils.toArray('.floating-icon').forEach((icon, index) => {
        gsap.to(icon, {
            duration: 6 + index,
            y: -30,
            rotation: 360,
            repeat: -1,
            yoyo: true,
            ease: 'power1.inOut',
            delay: index * 0.5
        });
    });
}

// Follow influencer function
function followInfluencer(influencerId) {
    const button = event.target.closest('.follow-btn');
    const originalText = button.innerHTML;

    // Animate button
    gsap.to(button, {
        duration: 0.2,
        scale: 0.95,
        ease: 'power2.out',
        onComplete: () => {
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Following...</span>';
            button.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';

            // Simulate API call
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-check"></i><span>Following</span>';
                button.disabled = true;
                button.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';

                gsap.to(button, {
                    duration: 0.3,
                    scale: 1,
                    ease: 'power2.out'
                });

                // Show success message
                showNotification('Successfully followed influencer!', 'success');
            }, 1500);
        }
    });
}

// Show notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    // Add notification styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: 600;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Influencers page functionality
function initInfluencersPage() {
    // View toggle functionality
    const viewBtns = document.querySelectorAll('.view-btn');
    const influencersGrid = document.getElementById('influencersGrid');

    viewBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons
            viewBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            btn.classList.add('active');

            // Toggle grid view
            const view = btn.dataset.view;
            if (view === 'list') {
                influencersGrid.classList.add('list-view');
            } else {
                influencersGrid.classList.remove('list-view');
            }
        });
    });

    // Back to top button
    const backToTopBtn = document.querySelector('.back-to-top');

    if (backToTopBtn) {
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });
    }

    // Animate cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all influencer cards
    document.querySelectorAll('.influencer-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        cardObserver.observe(card);
    });
}

// Scroll to top function
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Initialize influencers page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the influencers page
    if (window.location.search.includes('page=influencers')) {
        initInfluencersPage();
    }
});

/**
 * Vote for a nominee
 *
 * @param {number} nomineeId Nominee ID
 */
function voteForNominee(nomineeId) {
    // Check if user is logged in
    const isLoggedIn = document.body.classList.contains('logged-in');

    if (!isLoggedIn) {
        // Redirect to login page
        window.location.href = '?page=login&redirect=vote';
        return;
    }

    // Send AJAX request to vote
    const xhr = new XMLHttpRequest();
    xhr.open('POST', 'includes/vote.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);

                    if (response.success) {
                        // Update vote count
                        const voteCountElement = document.querySelector(`.nominee-card[data-nominee-id="${nomineeId}"] .votes-count`);
                        if (voteCountElement) {
                            voteCountElement.textContent = response.votes;
                        }

                        // Show success message
                        showNotification(response.message, 'success');
                    } else {
                        // Show error message
                        showNotification(response.message, 'error');
                    }
                } catch (e) {
                    showNotification('An error occurred while processing your vote', 'error');
                }
            } else {
                showNotification('An error occurred while processing your vote', 'error');
            }
        }
    };
    xhr.send(`nominee_id=${nomineeId}&csrf_token=${getCsrfToken()}`);
}

/**
 * Get CSRF token from the page
 *
 * @return {string} CSRF token
 */
function getCsrfToken() {
    const tokenInput = document.querySelector('input[name="csrf_token"]');
    return tokenInput ? tokenInput.value : '';
}

/**
 * Show notification
 *
 * @param {string} message Notification message
 * @param {string} type Notification type (success, error, info)
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = message;

    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Hide and remove notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');

        notification.addEventListener('transitionend', function() {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }, 3000);
}

// Gallery Lightbox Functionality
let currentImageIndex = 0;
let galleryImages = [];

// Initialize gallery images array
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('.gallery-image img');
    galleryImages = Array.from(images).map(img => ({
        src: img.src,
        title: img.getAttribute('data-title') || '',
        description: img.getAttribute('data-description') || ''
    }));
});

function openLightbox(imageSrc, title, description) {
    const lightbox = document.getElementById('galleryLightbox');
    const lightboxImage = document.getElementById('lightboxImage');
    const lightboxTitle = document.getElementById('lightboxTitle');
    const lightboxDescription = document.getElementById('lightboxDescription');

    // Find current image index
    currentImageIndex = galleryImages.findIndex(img => img.src === imageSrc);
    if (currentImageIndex === -1) currentImageIndex = 0;

    // Set image and info
    lightboxImage.src = imageSrc;
    lightboxTitle.textContent = title;
    lightboxDescription.textContent = description;

    // Show lightbox
    lightbox.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Add keyboard event listener
    document.addEventListener('keydown', handleKeydown);
}

function closeLightbox() {
    const lightbox = document.getElementById('galleryLightbox');
    lightbox.style.display = 'none';
    document.body.style.overflow = 'auto';

    // Remove keyboard event listener
    document.removeEventListener('keydown', handleKeydown);
}

function previousImage() {
    if (galleryImages.length === 0) return;

    currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
    updateLightboxImage();
}

function nextImage() {
    if (galleryImages.length === 0) return;

    currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
    updateLightboxImage();
}

function updateLightboxImage() {
    const lightboxImage = document.getElementById('lightboxImage');
    const lightboxTitle = document.getElementById('lightboxTitle');
    const lightboxDescription = document.getElementById('lightboxDescription');

    const currentImage = galleryImages[currentImageIndex];

    // Add fade effect
    lightboxImage.style.opacity = '0';

    setTimeout(() => {
        lightboxImage.src = currentImage.src;
        lightboxTitle.textContent = currentImage.title;
        lightboxDescription.textContent = currentImage.description;
        lightboxImage.style.opacity = '1';
    }, 150);
}

function handleKeydown(event) {
    switch(event.key) {
        case 'Escape':
            closeLightbox();
            break;
        case 'ArrowLeft':
            previousImage();
            break;
        case 'ArrowRight':
            nextImage();
            break;
    }
}

// Close lightbox when clicking outside the image
document.addEventListener('click', function(event) {
    const lightbox = document.getElementById('galleryLightbox');
    if (event.target === lightbox) {
        closeLightbox();
    }
});

// Performance optimizations
document.addEventListener('DOMContentLoaded', function() {
    // Optimize video loading
    optimizeVideoLoading();

    // Add cache headers
    addCacheHeaders();

    // Preload critical resources
    preloadCriticalResources();
});

function optimizeVideoLoading() {
    const videos = document.querySelectorAll('video');

    videos.forEach(video => {
        // Only load video when user interacts with it
        video.addEventListener('play', function() {
            if (this.dataset.src && !this.src) {
                this.src = this.dataset.src;
                this.load();
            }
        }, { once: true });

        // Pause videos when not in viewport
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (!entry.isIntersecting && !entry.target.paused) {
                    entry.target.pause();
                }
            });
        }, { threshold: 0.5 });

        observer.observe(video);
    });
}

function addCacheHeaders() {
    // Add cache control for static assets
    const staticAssets = document.querySelectorAll('img, video, audio');
    staticAssets.forEach(asset => {
        if (asset.src && !asset.src.includes('data:')) {
            asset.crossOrigin = 'anonymous';
        }
    });
}

function preloadCriticalResources() {
    // Preload hero video
    const heroVideo = document.getElementById('hero-video');
    if (heroVideo && heroVideo.dataset.src) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'video';
        link.href = heroVideo.dataset.src;
        document.head.appendChild(link);
    }

    // Preload critical images
    const criticalImages = document.querySelectorAll('.hero img, .logo img');
    criticalImages.forEach(img => {
        if (img.dataset.src) {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = img.dataset.src;
            document.head.appendChild(link);
        }
    });
}
