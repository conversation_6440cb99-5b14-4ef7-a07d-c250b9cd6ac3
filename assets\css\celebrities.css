/* Celebrities Page Styles */
:root {
    --celebrities-bg-color: #000000;
    --celebrities-text-color: #ffffff;
    --celebrities-gold: #d4af37;
    --celebrities-gold-light: #e5c158;
    --celebrities-gold-dark: #b38728;
    --celebrities-card-bg: #111111;
    --celebrities-card-hover-bg: #1a1a1a;
    --celebrities-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    --celebrities-border-radius: 8px;
    --celebrities-transition: all 0.3s ease;
}

.celebrities-section {
    background: linear-gradient(to bottom, var(--celebrities-bg-color), #111111);
    padding: 40px 0 60px; /* Increased bottom padding */
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.celebrities-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(212, 175, 55, 0.05), transparent 70%);
    pointer-events: none;
}

.celebrities-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.celebrities-title {
    color: var(--celebrities-gold);
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.celebrities-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, transparent, var(--celebrities-gold), transparent);
}

.celebrities-description {
    color: var(--celebrities-text-color);
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

/* Categories Filter */
.celebrities-filter {
    margin-bottom: 30px;
}

.filter-tabs {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
}

.filter-tab {
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.5);
    color: var(--celebrities-text-color);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 30px;
    cursor: pointer;
    transition: var(--celebrities-transition);
    font-size: 0.9rem;
    text-decoration: none;
}

.filter-tab:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-3px);
}

.filter-tab.active {
    background: linear-gradient(145deg, var(--celebrities-gold), var(--celebrities-gold-dark));
    color: #000;
    border-color: var(--celebrities-gold);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Celebrities Grid */
.celebrities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 20px;
    min-height: 400px; /* Add minimum height to ensure content fills the page */
}

.celebrity-card {
    background: linear-gradient(145deg, var(--celebrities-card-bg), #0a0a0a);
    border-radius: var(--celebrities-border-radius);
    overflow: hidden;
    box-shadow: var(--celebrities-shadow);
    transition: var(--celebrities-transition);
    border: 1px solid rgba(212, 175, 55, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.celebrity-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(212, 175, 55, 0.1), transparent);
    opacity: 0;
    transition: var(--celebrities-transition);
    pointer-events: none;
}

.celebrity-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
    border-color: rgba(212, 175, 55, 0.3);
}

.celebrity-card:hover::before {
    opacity: 1;
}

.celebrity-image {
    height: 350px;
    overflow: hidden;
    position: relative;
}

.celebrity-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--celebrities-transition);
}

.celebrity-card:hover .celebrity-image img {
    transform: scale(1.05);
}

.celebrity-category {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: var(--celebrities-gold);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 1;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.celebrity-info {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.celebrity-name {
    color: var(--celebrities-gold);
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.celebrity-description {
    color: var(--celebrities-text-color);
    opacity: 0.8;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 15px;
    flex: 1;
}

.celebrity-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.celebrity-social {
    display: flex;
    gap: 10px;
}

.social-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--celebrities-text-color);
    transition: var(--celebrities-transition);
    text-decoration: none;
}

.social-icon:hover {
    background: var(--celebrities-gold);
    color: #000;
    transform: translateY(-3px);
}

.celebrity-actions {
    display: flex;
    gap: 10px;
}

.btn-view, .btn-vote {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--celebrities-transition);
    cursor: pointer;
    text-decoration: none;
}

.btn-view {
    background: rgba(255, 255, 255, 0.1);
    color: var(--celebrities-text-color);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-vote {
    background: linear-gradient(145deg, var(--celebrities-gold), var(--celebrities-gold-dark));
    color: #000;
    border: none;
}

.btn-view:hover, .btn-vote:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-view i, .btn-vote i {
    margin-right: 5px;
}

/* Featured Celebrities */
.featured-celebrities {
    margin-bottom: 40px;
}

.featured-title {
    text-align: center;
    margin-bottom: 20px;
}

.featured-slider {
    position: relative;
    overflow: hidden;
    padding: 10px 0;
}

.featured-slide {
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    border-radius: var(--celebrities-border-radius);
    overflow: hidden;
    box-shadow: var(--celebrities-shadow);
    transition: var(--celebrities-transition);
    border: 1px solid rgba(212, 175, 55, 0.2);
    display: flex;
    flex-direction: column;
    height: 450px;
    margin: 0 15px;
}

.featured-slide:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
}

.featured-image {
    height: 300px;
    position: relative;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--celebrities-transition);
}

.featured-slide:hover .featured-image img {
    transform: scale(1.05);
}

.featured-info {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.featured-name {
    color: var(--celebrities-gold);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.featured-role {
    color: var(--celebrities-text-color);
    opacity: 0.7;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.featured-actions {
    margin-top: auto;
}

/* Pagination */
.celebrities-pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 10%;
    margin-bottom: 30px;
}

.page-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    color: var(--celebrities-text-color);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--celebrities-transition);
    border: 1px solid rgba(212, 175, 55, 0.2);
    text-decoration: none;
    font-weight: bold;
    margin: 0 2px;
}

.page-item.active {
    background: linear-gradient(145deg, var(--celebrities-gold), var(--celebrities-gold-dark));
    color: #000;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    transform: scale(1.1);
}

.page-item:hover:not(.active) {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .celebrities-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .celebrity-image {
        height: 300px;
    }

    .featured-slide {
        height: 400px;
    }

    .featured-image {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .celebrities-section {
        padding: 40px 0;
    }

    .celebrities-title {
        font-size: 2rem;
    }

    .filter-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 10px;
        justify-content: flex-start;
    }

    .filter-tab {
        white-space: nowrap;
        flex: 0 0 auto;
    }

    .celebrities-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 15px;
    }

    .celebrity-image {
        height: 250px;
    }

    .featured-slide {
        height: 380px;
    }

    .featured-image {
        height: 220px;
    }
}

@media (max-width: 576px) {
    .celebrities-grid {
        grid-template-columns: 1fr;
        max-width: 300px;
        margin: 0 auto 40px;
    }

    .celebrity-image {
        height: 300px;
    }
}
