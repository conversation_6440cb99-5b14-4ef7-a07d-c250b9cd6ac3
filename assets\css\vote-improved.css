/* Improved Vote Page Styles */
.vote-section {
    padding: 0;
    background: #111111;
    min-height: 100vh;
}

.container {
    max-width: 100%;
    padding: 0;
}

.alert {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: auto;
    max-width: 90%;
    text-align: center;
    animation: fadeOut 0.5s ease 3s forwards;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; visibility: hidden; }
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.9);
    border: none;
    color: white;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.9);
    border: none;
    color: white;
}

/* Main Categories Section */
.main-categories-section {
    position: sticky;
    top: 60px;
    z-index: 100;
    background: #000000;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.main-categories-section h2 {
    display: none;
}

.main-categories-slider {
    overflow: hidden;
    position: relative;
}

.categories-scroll {
    display: flex;
    overflow-x: auto;
    padding: 5px 10px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}

.categories-scroll::-webkit-scrollbar {
    display: none;
}

.main-category-item {
    flex: 0 0 auto;
    width: 80px;
    margin-right: 10px;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 8px;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    border: 1px solid #333;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.main-category-item:hover {
    transform: translateY(-3px);
    border-color: #ffd700;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.main-category-item.active {
    background: rgba(255, 215, 0, 0.1);
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.category-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-icon i {
    font-size: 1.5rem;
    color: #ffd700;
}

.main-category-item h3 {
    font-size: 0.7rem;
    margin: 0;
    color: white;
    transition: color 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.main-category-item:hover h3 {
    color: #ffd700;
}

/* Voting Content */
.voting-content {
    padding: 10px;
}

/* Subcategories Section */
.subcategories-section {
    margin-bottom: 15px;
    background: #1a1a1a;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #333;
}

.subcategories-section h2 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 10px;
    text-align: center;
}

.subcategories-slider {
    overflow: hidden;
    position: relative;
}

.subcategories-scroll {
    display: flex;
    overflow-x: auto;
    padding: 5px 0;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}

.subcategories-scroll::-webkit-scrollbar {
    display: none;
}

.subcategory-item {
    flex: 0 0 auto;
    width: 80px;
    margin-right: 10px;
    background: #222;
    border-radius: 8px;
    padding: 8px;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    border: 1px solid #333;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.subcategory-item:hover {
    transform: translateY(-3px);
    border-color: #ffd700;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.subcategory-item.active {
    background: rgba(255, 215, 0, 0.1);
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.subcategory-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.subcategory-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.subcategory-icon i {
    font-size: 1.2rem;
    color: #ffd700;
}

.subcategory-item h3 {
    font-size: 0.7rem;
    margin: 0;
    color: white;
    transition: color 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.subcategory-item:hover h3 {
    color: #ffd700;
}
