<?php
/**
 * Admin Content Management Page
 */

// Process form submission
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $message = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
        $message_type = 'danger';
    } else {
        // Get form data
        $section = isset($_POST['section']) ? trim($_POST['section']) : '';
        $key_name = isset($_POST['key_name']) ? trim($_POST['key_name']) : '';
        $value_en = isset($_POST['value_en']) ? trim($_POST['value_en']) : '';
        $value_ar = isset($_POST['value_ar']) ? trim($_POST['value_ar']) : '';
        $content_id = isset($_POST['content_id']) ? (int)$_POST['content_id'] : 0;
        $action = isset($_POST['action']) ? $_POST['action'] : '';

        // Validate form data
        if ($action === 'delete') {
            // Delete content
            if ($content_id > 0) {
                $sql = "DELETE FROM content WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('i', $content_id);

                if ($stmt->execute()) {
                    $message = 'تم حذف المحتوى بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء حذف المحتوى: ' . $stmt->error;
                    $message_type = 'danger';
                }
            }
        } elseif ($action === 'edit') {
            // Update content
            if ($content_id > 0 && !empty($value_en) && !empty($value_ar)) {
                $sql = "UPDATE content SET value_en = ?, value_ar = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('ssi', $value_en, $value_ar, $content_id);

                if ($stmt->execute()) {
                    $message = 'تم تحديث المحتوى بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تحديث المحتوى: ' . $stmt->error;
                    $message_type = 'danger';
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        } elseif ($action === 'add') {
            // Add new content
            if (!empty($section) && !empty($key_name) && !empty($value_en) && !empty($value_ar)) {
                // Check if content already exists
                $check_sql = "SELECT id FROM content WHERE section = ? AND key_name = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param('ss', $section, $key_name);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();

                if ($check_result->num_rows > 0) {
                    $message = 'المحتوى موجود بالفعل. يرجى استخدام زر التحرير بدلاً من ذلك.';
                    $message_type = 'danger';
                } else {
                    // Insert new content
                    $sql = "INSERT INTO content (section, key_name, value_en, value_ar) VALUES (?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('ssss', $section, $key_name, $value_en, $value_ar);

                    if ($stmt->execute()) {
                        $message = 'تمت إضافة المحتوى بنجاح.';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء إضافة المحتوى: ' . $stmt->error;
                        $message_type = 'danger';
                    }
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        }
    }
}

// Get content sections
$sections_sql = "SELECT DISTINCT section FROM content ORDER BY section";
$sections_result = $conn->query($sections_sql);
$sections = [];

if ($sections_result && $sections_result->num_rows > 0) {
    while ($row = $sections_result->fetch_assoc()) {
        $sections[] = $row['section'];
    }
}

// Get content for each section
$content_by_section = [];

foreach ($sections as $section) {
    $content_sql = "SELECT id, section, key_name, value_en, value_ar FROM content WHERE section = ? ORDER BY key_name";
    $content_stmt = $conn->prepare($content_sql);
    $content_stmt->bind_param('s', $section);
    $content_stmt->execute();
    $content_result = $content_stmt->get_result();

    $content_by_section[$section] = [];

    if ($content_result && $content_result->num_rows > 0) {
        while ($row = $content_result->fetch_assoc()) {
            $content_by_section[$section][] = $row;
        }
    }
}

// Debug information
echo "<!-- Debug: Found " . count($sections) . " sections -->";
foreach ($sections as $section) {
    echo "<!-- Debug: Section '$section' has " . count($content_by_section[$section]) . " items -->";
}
?>

<div class="content-management">
    <h1 class="page-title">إدارة المحتوى</h1>

    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?>">
        <?php echo $message; ?>
    </div>
    <?php endif; ?>

    <!-- Add New Content Form -->
    <div class="card">
        <div class="card-header">
            <h2>إضافة محتوى جديد</h2>
        </div>
        <div class="card-body">
            <form action="index.php?page=content" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="add">

                <div class="form-row">
                    <div class="form-group">
                        <label for="section">القسم</label>
                        <input type="text" id="section" name="section" required list="section-list">
                        <datalist id="section-list">
                            <?php foreach ($sections as $section): ?>
                            <option value="<?php echo htmlspecialchars($section); ?>">
                            <?php endforeach; ?>
                        </datalist>
                    </div>

                    <div class="form-group">
                        <label for="key_name">اسم المفتاح</label>
                        <input type="text" id="key_name" name="key_name" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="value_en">القيمة (الإنجليزية)</label>
                        <textarea id="value_en" name="value_en" rows="3" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="value_ar">القيمة (العربية)</label>
                        <textarea id="value_ar" name="value_ar" rows="3" required dir="rtl"></textarea>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">إضافة محتوى</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Content Tabs -->
    <div class="content-tabs-container">
        <div class="content-tabs">
            <?php if (empty($sections)): ?>
            <div class="content-tab active">No Sections</div>
            <?php else: ?>
                <?php foreach ($sections as $index => $section): ?>
                <div class="content-tab <?php echo $index === 0 ? 'active' : ''; ?>" data-tab="tab-<?php echo htmlspecialchars($section); ?>">
                    <?php echo htmlspecialchars($section); ?>
                </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <?php if (empty($sections)): ?>
        <div class="content-panel active">
            <p class="no-data">لا توجد أقسام محتوى. أضف محتوى جديد أولاً.</p>
        </div>
        <?php else: ?>
            <?php foreach ($sections as $index => $section): ?>
            <div id="tab-<?php echo htmlspecialchars($section); ?>" class="content-panel <?php echo $index === 0 ? 'active' : ''; ?>">
                <h2><?php echo htmlspecialchars($section); ?></h2>

                <?php if (empty($content_by_section[$section])): ?>
                <p class="no-data">لا يوجد محتوى في هذا القسم.</p>
                <?php else: ?>
                <div class="content-items">
                    <?php foreach ($content_by_section[$section] as $content): ?>
                    <div class="content-item">
                        <div class="content-item-header">
                            <h3 class="content-item-title"><?php echo htmlspecialchars($content['key_name']); ?></h3>
                            <div class="content-item-actions">
                                <button class="btn btn-sm btn-primary edit-content" data-id="<?php echo $content['id']; ?>" data-key="<?php echo htmlspecialchars($content['key_name']); ?>" data-en="<?php echo htmlspecialchars($content['value_en']); ?>" data-ar="<?php echo htmlspecialchars($content['value_ar']); ?>">
                                    <i class="fas fa-edit"></i> تحرير
                                </button>
                                <button class="btn btn-sm btn-danger delete-content" data-id="<?php echo $content['id']; ?>" data-key="<?php echo htmlspecialchars($content['key_name']); ?>">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </div>
                        </div>

                        <div class="content-item-body">
                            <div class="content-value">
                                <div class="content-label">الإنجليزية:</div>
                                <div class="content-text"><?php echo nl2br(htmlspecialchars($content['value_en'])); ?></div>
                            </div>

                            <div class="content-value">
                                <div class="content-label">العربية:</div>
                                <div class="content-text" dir="rtl"><?php echo nl2br(htmlspecialchars($content['value_ar'])); ?></div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- Edit Content Modal -->
<div class="modal" id="editContentModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>تحرير المحتوى</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=content" method="post" id="editContentForm">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="content_id" id="edit_content_id">

                <div class="form-group">
                    <label for="edit_key_name">اسم المفتاح</label>
                    <input type="text" id="edit_key_name" readonly>
                </div>

                <div class="form-group">
                    <label for="edit_value_en">القيمة (الإنجليزية)</label>
                    <textarea id="edit_value_en" name="value_en" rows="3" required></textarea>
                </div>

                <div class="form-group">
                    <label for="edit_value_ar">القيمة (العربية)</label>
                    <textarea id="edit_value_ar" name="value_ar" rows="3" required dir="rtl"></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Content Modal -->
<div class="modal" id="deleteContentModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>حذف المحتوى</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من أنك تريد حذف المحتوى "<span id="delete_content_key"></span>"؟</p>
            <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>

            <form action="index.php?page=content" method="post" id="deleteContentForm">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="content_id" id="delete_content_id">

                <div class="form-actions">
                    <button type="submit" class="btn btn-danger">حذف</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Content tabs
    const contentTabs = document.querySelectorAll('.content-tab');
    const contentPanels = document.querySelectorAll('.content-panel');

    // Store the active tab in session storage
    const storedActiveTab = sessionStorage.getItem('activeContentTab');

    // If there's a stored active tab, activate it
    if (storedActiveTab) {
        const tabToActivate = document.querySelector(`[data-tab="${storedActiveTab}"]`);
        const panelToActivate = document.getElementById(storedActiveTab);

        if (tabToActivate && panelToActivate) {
            // Deactivate all tabs and panels
            contentTabs.forEach(tab => tab.classList.remove('active'));
            contentPanels.forEach(panel => panel.classList.remove('active'));

            // Activate the stored tab and panel
            tabToActivate.classList.add('active');
            panelToActivate.classList.add('active');
        }
    }

    contentTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            if (!tabId) return; // Skip if no tab ID (for "No Sections" tab)

            // Store the active tab in session storage
            sessionStorage.setItem('activeContentTab', tabId);

            // Remove active class from all tabs and panels
            contentTabs.forEach(t => t.classList.remove('active'));
            contentPanels.forEach(p => {
                p.style.opacity = '0';
                setTimeout(() => {
                    p.classList.remove('active');
                }, 300);
            });

            // Add active class to clicked tab and corresponding panel
            this.classList.add('active');

            const panel = document.getElementById(tabId);
            if (panel) {
                setTimeout(() => {
                    panel.classList.add('active');
                    setTimeout(() => {
                        panel.style.opacity = '1';
                    }, 10);
                }, 300);
            } else {
                console.error('Panel not found for tab ID:', tabId);
            }
        });
    });

    // Edit content modal
    const editButtons = document.querySelectorAll('.edit-content');
    const editModal = document.getElementById('editContentModal');
    const editContentId = document.getElementById('edit_content_id');
    const editKeyName = document.getElementById('edit_key_name');
    const editValueEn = document.getElementById('edit_value_en');
    const editValueAr = document.getElementById('edit_value_ar');

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const key = this.getAttribute('data-key');
            const valueEn = this.getAttribute('data-en');
            const valueAr = this.getAttribute('data-ar');

            editContentId.value = id;
            editKeyName.value = key;
            editValueEn.value = valueEn;
            editValueAr.value = valueAr;

            // Show modal with animation
            editModal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Auto-resize textareas
            setTimeout(() => {
                editValueEn.style.height = 'auto';
                editValueEn.style.height = (editValueEn.scrollHeight) + 'px';
                editValueAr.style.height = 'auto';
                editValueAr.style.height = (editValueAr.scrollHeight) + 'px';

                // Focus on the first input
                editValueEn.focus();
            }, 300);
        });
    });

    // Delete content modal
    const deleteButtons = document.querySelectorAll('.delete-content');
    const deleteModal = document.getElementById('deleteContentModal');
    const deleteContentId = document.getElementById('delete_content_id');
    const deleteContentKey = document.getElementById('delete_content_key');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const key = this.getAttribute('data-key');

            deleteContentId.value = id;
            deleteContentKey.textContent = key;

            // Show modal with animation
            deleteModal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        });
    });

    // Close modals
    const modalCloseButtons = document.querySelectorAll('.modal-close');

    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.opacity = '0';
            setTimeout(() => {
                event.target.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        }
    });

    // Add animation to content items
    const contentItems = document.querySelectorAll('.content-item');
    contentItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';

        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 100 + (index * 50));
    });

    // Add search functionality
    const searchContainer = document.createElement('div');
    searchContainer.className = 'content-search';
    searchContainer.innerHTML = `
        <input type="text" id="contentSearch" placeholder="بحث في المحتوى...">
        <i class="fas fa-search"></i>
    `;

    const contentTabsContainer = document.querySelector('.content-tabs-container');
    if (contentTabsContainer) {
        contentTabsContainer.insertBefore(searchContainer, contentTabsContainer.firstChild);

        const searchInput = document.getElementById('contentSearch');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const contentItems = document.querySelectorAll('.content-item');

                contentItems.forEach(item => {
                    const title = item.querySelector('.content-item-title').textContent.toLowerCase();
                    const contentEn = item.querySelector('.content-text').textContent.toLowerCase();
                    const contentAr = item.querySelectorAll('.content-text')[1]?.textContent.toLowerCase();

                    if (title.includes(searchTerm) || contentEn.includes(searchTerm) || (contentAr && contentAr.includes(searchTerm))) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }
    }
});
</script>
