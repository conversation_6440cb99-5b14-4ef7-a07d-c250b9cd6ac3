/**
 * Content Management Page JavaScript
 * Handles tab switching, modals, and other interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Content tabs functionality
    initContentTabs();
    
    // Edit modal functionality
    initEditModal();
    
    // Delete modal functionality
    initDeleteModal();
    
    // Add animation to content items
    animateContentItems();
    
    // Add ripple effect to buttons
    initRippleEffect();
    
    // Add textarea auto-resize
    initTextareaAutoResize();
    
    // Add form validation
    initFormValidation();
    
    // Add search functionality
    initSearchFunctionality();
});

/**
 * Initialize content tabs functionality
 */
function initContentTabs() {
    const contentTabs = document.querySelectorAll('.content-tab');
    const contentPanels = document.querySelectorAll('.content-panel');
    
    if (contentTabs.length === 0 || contentPanels.length === 0) return;
    
    // Store the active tab in session storage
    const storedActiveTab = sessionStorage.getItem('activeContentTab');
    
    // If there's a stored active tab, activate it
    if (storedActiveTab) {
        const tabToActivate = document.querySelector(`[data-tab="${storedActiveTab}"]`);
        const panelToActivate = document.getElementById(storedActiveTab);
        
        if (tabToActivate && panelToActivate) {
            // Deactivate all tabs and panels
            contentTabs.forEach(tab => tab.classList.remove('active'));
            contentPanels.forEach(panel => panel.classList.remove('active'));
            
            // Activate the stored tab and panel
            tabToActivate.classList.add('active');
            panelToActivate.classList.add('active');
        }
    }
    
    // Add click event listeners to tabs
    contentTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            if (!tabId) return;
            
            // Store the active tab in session storage
            sessionStorage.setItem('activeContentTab', tabId);
            
            // Deactivate all tabs and panels
            contentTabs.forEach(t => t.classList.remove('active'));
            contentPanels.forEach(p => {
                p.style.opacity = '0';
                setTimeout(() => {
                    p.classList.remove('active');
                }, 300);
            });
            
            // Activate the clicked tab
            this.classList.add('active');
            
            // Activate the corresponding panel with animation
            const panel = document.getElementById(tabId);
            if (panel) {
                setTimeout(() => {
                    panel.classList.add('active');
                    setTimeout(() => {
                        panel.style.opacity = '1';
                    }, 10);
                }, 300);
            }
        });
    });
}

/**
 * Initialize edit modal functionality
 */
function initEditModal() {
    const editButtons = document.querySelectorAll('.edit-content');
    const editModal = document.getElementById('editContentModal');
    
    if (!editModal || editButtons.length === 0) return;
    
    const editContentId = document.getElementById('edit_content_id');
    const editKeyName = document.getElementById('edit_key_name');
    const editValueEn = document.getElementById('edit_value_en');
    const editValueAr = document.getElementById('edit_value_ar');
    
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const key = this.getAttribute('data-key');
            const valueEn = this.getAttribute('data-en');
            const valueAr = this.getAttribute('data-ar');
            
            editContentId.value = id;
            editKeyName.value = key;
            editValueEn.value = valueEn;
            editValueAr.value = valueAr;
            
            editModal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            // Focus on the first input
            setTimeout(() => {
                editValueEn.focus();
            }, 300);
        });
    });
}

/**
 * Initialize delete modal functionality
 */
function initDeleteModal() {
    const deleteButtons = document.querySelectorAll('.delete-content');
    const deleteModal = document.getElementById('deleteContentModal');
    
    if (!deleteModal || deleteButtons.length === 0) return;
    
    const deleteContentId = document.getElementById('delete_content_id');
    const deleteContentKey = document.getElementById('delete_content_key');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const key = this.getAttribute('data-key');
            
            deleteContentId.value = id;
            deleteContentKey.textContent = key;
            
            deleteModal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        });
    });
}

/**
 * Close modals when clicking close button or outside
 */
function initModalClose() {
    const modals = document.querySelectorAll('.modal');
    const closeButtons = document.querySelectorAll('.modal-close');
    
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
            document.body.style.overflow = '';
        });
    });
    
    modals.forEach(modal => {
        modal.addEventListener('click', function(event) {
            if (event.target === this) {
                this.style.display = 'none';
                document.body.style.overflow = '';
            }
        });
    });
}

/**
 * Add animation to content items
 */
function animateContentItems() {
    const contentItems = document.querySelectorAll('.content-item');
    
    contentItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
        
        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 100 + (index * 50));
    });
}

/**
 * Add ripple effect to buttons
 */
function initRippleEffect() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * Add textarea auto-resize
 */
function initTextareaAutoResize() {
    const textareas = document.querySelectorAll('textarea');
    
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
        
        // Trigger the event on load
        const event = new Event('input');
        textarea.dispatchEvent(event);
    });
}

/**
 * Add form validation
 */
function initFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredInputs = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredInputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('is-invalid');
                    
                    // Add error message if it doesn't exist
                    const errorMessage = input.parentElement.querySelector('.error-message');
                    if (!errorMessage) {
                        const error = document.createElement('div');
                        error.className = 'error-message';
                        error.textContent = 'هذا الحقل مطلوب';
                        input.parentElement.appendChild(error);
                    }
                } else {
                    input.classList.remove('is-invalid');
                    
                    // Remove error message if it exists
                    const errorMessage = input.parentElement.querySelector('.error-message');
                    if (errorMessage) {
                        errorMessage.remove();
                    }
                }
            });
            
            if (!isValid) {
                e.preventDefault();
            }
        });
    });
}

/**
 * Add search functionality
 */
function initSearchFunctionality() {
    // Add search input
    const contentTabsContainer = document.querySelector('.content-tabs-container');
    if (!contentTabsContainer) return;
    
    const searchContainer = document.createElement('div');
    searchContainer.className = 'content-search';
    searchContainer.innerHTML = `
        <input type="text" id="contentSearch" placeholder="بحث في المحتوى...">
        <i class="fas fa-search"></i>
    `;
    
    contentTabsContainer.insertBefore(searchContainer, contentTabsContainer.firstChild);
    
    // Add search functionality
    const searchInput = document.getElementById('contentSearch');
    if (!searchInput) return;
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const contentItems = document.querySelectorAll('.content-item');
        
        contentItems.forEach(item => {
            const title = item.querySelector('.content-item-title').textContent.toLowerCase();
            const contentEn = item.querySelector('.content-text').textContent.toLowerCase();
            const contentAr = item.querySelectorAll('.content-text')[1]?.textContent.toLowerCase();
            
            if (title.includes(searchTerm) || contentEn.includes(searchTerm) || (contentAr && contentAr.includes(searchTerm))) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    });
}

// Initialize modal close functionality
initModalClose();
