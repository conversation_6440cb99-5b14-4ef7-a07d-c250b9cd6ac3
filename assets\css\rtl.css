/**
 * RTL CSS for Arabic language support
 */

/* ===== Base Styles ===== */
body {
    text-align: right;
}

/* ===== Header ===== */
.header-inner {
    flex-direction: row-reverse;
}

.nav-menu {
    flex-direction: row-reverse;
}

.nav-menu a::after {
    right: 0;
    left: auto;
}

.language-switcher {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

.lang-item {
    margin-left: 0;
    margin-right: var(--spacing-sm);
}

.lang-item img {
    margin-right: 0;
    margin-left: var(--spacing-xs);
}

.auth-buttons {
    flex-direction: row-reverse;
}

.btn-login,
.btn-register {
    margin-left: 0;
    margin-right: var(--spacing-sm);
}

.user-toggle i {
    margin-right: 0;
    margin-left: var(--spacing-xs);
}

.dropdown-menu {
    right: auto;
    left: 0;
}

/* ===== Buttons ===== */
.btn-vote-hero {
    direction: rtl;
}

/* ===== Nominees Section ===== */
.nominee-info {
    direction: rtl;
}

.votes-label {
    margin-left: 0;
    margin-right: var(--spacing-xs);
}

/* ===== Stories Section ===== */
.stories-carousel {
    direction: rtl;
}

.story-info {
    direction: rtl;
}

/* ===== Sponsors Section ===== */
.sponsors-grid {
    direction: rtl;
}

/* ===== Footer ===== */
.footer-nav ul,
.footer-contact ul {
    padding-right: 0;
}

.footer-contact i {
    margin-right: 0;
    margin-left: var(--spacing-xs);
}

.social-links {
    flex-direction: row-reverse;
}

.social-links a {
    margin-right: 0;
    margin-left: var(--spacing-sm);
}

.newsletter-form input {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.newsletter-form button {
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.footer-links a {
    margin-left: 0;
    margin-right: var(--spacing-md);
}

/* ===== Media Queries ===== */
@media (max-width: 768px) {
    .nav-menu {
        right: -100%;
        left: auto;
    }
    
    .nav-menu.active {
        right: 0;
        left: auto;
    }
    
    .footer-links a {
        margin: 0 var(--spacing-xs);
    }
}
