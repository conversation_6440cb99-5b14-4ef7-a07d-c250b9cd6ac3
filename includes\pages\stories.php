<?php
/**
 * Stories Page - Festival Highlights
 */

// Check if reels table exists
$check_table = $conn->query("SHOW TABLES LIKE 'reels'");
$table_exists = $check_table->num_rows > 0;

// Create reels table if it doesn't exist
if (!$table_exists) {
    $sql = "CREATE TABLE reels (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        title_ar VARCHAR(255) NOT NULL,
        description TEXT,
        description_ar TEXT,
        video VARCHAR(255) NOT NULL,
        thumbnail VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        // Insert sample reels
        $sample_reels = [
            ['Opening Ceremony', 'حفل الافتتاح', 'Highlights from the grand opening ceremony of the Social Media Festival.', 'لقطات من حفل الافتتاح الكبير لمهرجان وسائل التواصل الاجتماعي.', 'desktop.mp4', 'thumbnail1.jpg'],
            ['Awards Ceremony', 'حفل توزيع الجوائز', 'The exciting moments from our awards ceremony honoring digital excellence.', 'اللحظات المثيرة من حفل توزيع الجوائز الذي يكرم التميز الرقمي.', 'mobile.mp4', 'thumbnail2.jpg'],
            ['Celebrity Interview', 'مقابلة مع المشاهير', 'Exclusive interview with the festival\'s special guests and celebrities.', 'مقابلة حصرية مع ضيوف المهرجان الخاصين والمشاهير.', 'desktop.mp4', 'thumbnail3.jpg'],
            ['Behind The Scenes', 'خلف الكواليس', 'Take a look behind the scenes of the Social Media Festival.', 'إلقاء نظرة خلف كواليس مهرجان وسائل التواصل الاجتماعي.', 'mobile.mp4', 'thumbnail4.jpg'],
            ['Festival Moments', 'لحظات المهرجان', 'Memorable moments from throughout the festival.', 'لحظات لا تنسى من جميع أنحاء المهرجان.', 'desktop.mp4', 'thumbnail5.jpg'],
            ['Special Performance', 'عرض خاص', 'Special performance by our guest artists at the festival.', 'عرض خاص من فنانينا الضيوف في المهرجان.', 'mobile.mp4', 'thumbnail6.jpg']
        ];

        $insert_sql = "INSERT INTO reels (title, title_ar, description, description_ar, video, thumbnail) VALUES (?, ?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);

        foreach ($sample_reels as $reel) {
            $insert_stmt->bind_param("ssssss", $reel[0], $reel[1], $reel[2], $reel[3], $reel[4], $reel[5]);
            $insert_stmt->execute();
        }
    }
}

// Get reels from database - since there's no category field, we'll filter by title for demo purposes
function get_stories_by_category($category = '', $page = 1, $per_page = 6) {
    global $conn;

    // Calculate offset
    $offset = ($page - 1) * $per_page;

    // Base query
    $sql_count = "SELECT COUNT(*) as total FROM reels";
    $sql = "SELECT * FROM reels";

    // Add title filter if category is specified (for demo purposes)
    if (!empty($category)) {
        // Map category to title keywords for filtering
        $title_keywords = [
            'opening_ceremony' => 'Opening Ceremony',
            'awards_ceremony' => 'Awards Ceremony',
            'interview' => 'Interview',
            'behind_scenes' => 'Behind The Scenes',
            'moments' => 'Moments',
            'performance' => 'Performance'
        ];

        if (isset($title_keywords[$category])) {
            $keyword = $title_keywords[$category];
            $where_clause = " WHERE title LIKE ?";
            $sql_count .= $where_clause;
            $sql .= $where_clause;
            $keyword = "%$keyword%";
        }
    }

    // Add order and limit
    $sql .= " ORDER BY created_at DESC LIMIT ?, ?";

    // Get total count
    if (!empty($category) && isset($keyword)) {
        $count_stmt = $conn->prepare($sql_count);
        $count_stmt->bind_param("s", $keyword);
    } else {
        $count_stmt = $conn->prepare($sql_count);
    }

    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total = $count_result->fetch_assoc()['total'];

    // Get stories
    if (!empty($category) && isset($keyword)) {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sii", $keyword, $offset, $per_page);
    } else {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $offset, $per_page);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $stories = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $stories[] = $row;
        }
    }

    return [
        'stories' => $stories,
        'total' => $total
    ];
}

// Since there's no category field in the reels table, we'll use a fixed set of categories
function get_story_categories() {
    // Define fixed categories based on the videos we have
    return [
        'opening_ceremony',
        'awards_ceremony',
        'interview',
        'behind_scenes',
        'moments',
        'performance'
    ];
}

// Get selected category from URL
$selected_category = $_GET['category'] ?? '';

// Get current page from URL
$current_page = (int)($_GET['p'] ?? 1);
$per_page = 9; // Number of stories per page

// Get stories with pagination
$result = get_stories_by_category($selected_category, $current_page, $per_page);
$stories = $result['stories'];
$total_stories = $result['total'];

// Calculate total pages
$total_pages = ceil($total_stories / $per_page);

// Ensure current page is valid
if ($current_page < 1) {
    $current_page = 1;
} elseif ($current_page > $total_pages && $total_pages > 0) {
    $current_page = $total_pages;
}

// Get all categories
$categories = get_story_categories();

// Page title
$page_title = get_content('stories', 'title', 'Festival Highlights');
?>

<style>
    .stories-title{    margin-top: 5%;}
    @media (max-width: 768px) {
    .stories-title {
       margin-top: 18%;
    }
}
</style>
<!-- Stories Page -->
<section class="stories-section">
    <div class="container">
        <div class="stories-header">
            <h1 class="stories-title"><?php echo $page_title; ?></h1>
            <p class="stories-description"><?php echo get_content('stories', 'description', 'Watch the most exciting moments and highlights from the Social Media Festival.'); ?></p>
        </div>


        <!-- Stories Grid -->
        <?php if (!empty($stories)): ?>
        <div class="stories-grid">
            <?php foreach ($stories as $story): ?>
            <div class="story-card">
                <div class="story-thumbnail" data-video="<?php echo asset_url('uploads/' . $story['video']); ?>">
                    <img src="<?php echo asset_url('uploads/' . $story['thumbnail']); ?>" alt="<?php echo htmlspecialchars($current_lang === 'ar' ? $story['title_ar'] : $story['title']); ?>">
                    <div class="story-play-button">
                        <i class="fas fa-play"></i>
                    </div>
                </div>
                <div class="story-info">
                    <h3 class="story-title"><?php echo $current_lang === 'ar' ? $story['title_ar'] : $story['title']; ?></h3>
                    <div class="story-description">
                        <?php
                        $description = $current_lang === 'ar' ?
                            ($story['description_ar'] ?? '') :
                            ($story['description'] ?? '');
                        echo mb_substr($description, 0, 100) . (mb_strlen($description) > 100 ? '...' : '');
                        ?>
                    </div>
                    <div class="story-meta">
                        <div class="story-date">
                            <?php echo format_date($story['created_at']); ?>
                        </div>
                        <div class="story-actions">
                            <a href="#" class="btn-share" data-title="<?php echo htmlspecialchars($current_lang === 'ar' ? $story['title_ar'] : $story['title']); ?>">
                                <i class="fas fa-share-alt"></i> <?php echo get_content('stories', 'share', 'Share'); ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="stories-pagination">
            <?php if ($current_page > 1): ?>
            <a href="<?php echo base_url('?page=stories' . ($selected_category ? '&category=' . urlencode($selected_category) : '') . '&p=' . ($current_page - 1)); ?>" class="page-item">
                <i class="fas fa-chevron-left"></i>
            </a>
            <?php endif; ?>

            <?php
            // Simplified pagination - show fewer numbers
            $start_page = max(1, $current_page - 1);
            $end_page = min($total_pages, $current_page + 1);

            if ($start_page > 1) {
                echo '<a href="' . base_url('?page=stories' . ($selected_category ? '&category=' . urlencode($selected_category) : '') . '&p=1') . '" class="page-item">1</a>';
                if ($start_page > 2) {
                    echo '<span class="page-item">...</span>';
                }
            }

            for ($i = $start_page; $i <= $end_page; $i++) {
                echo '<a href="' . base_url('?page=stories' . ($selected_category ? '&category=' . urlencode($selected_category) : '') . '&p=' . $i) . '" class="page-item ' . ($i == $current_page ? 'active' : '') . '">' . $i . '</a>';
            }

            if ($end_page < $total_pages) {
                if ($end_page < $total_pages - 1) {
                    echo '<span class="page-item">...</span>';
                }
                echo '<a href="' . base_url('?page=stories' . ($selected_category ? '&category=' . urlencode($selected_category) : '') . '&p=' . $total_pages) . '" class="page-item">' . $total_pages . '</a>';
            }
            ?>

            <?php if ($current_page < $total_pages): ?>
            <a href="<?php echo base_url('?page=stories' . ($selected_category ? '&category=' . urlencode($selected_category) : '') . '&p=' . ($current_page + 1)); ?>" class="page-item">
                <i class="fas fa-chevron-right"></i>
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="no-stories">
            <i class="fas fa-video-slash"></i>
            <p><?php echo get_content('stories', 'no_stories', 'No stories found in this category'); ?></p>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Video Modal -->
<div class="video-modal">
    <div class="video-modal-content">
        <div class="video-modal-close">
            <i class="fas fa-times"></i>
        </div>
        <div class="video-wrapper">
            <!-- Video will be inserted here -->
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Video modal functionality
    const storyThumbnails = document.querySelectorAll('.story-thumbnail');
    const videoModal = document.querySelector('.video-modal');
    const videoWrapper = document.querySelector('.video-wrapper');
    const videoModalClose = document.querySelector('.video-modal-close');

    storyThumbnails.forEach(thumbnail => {
        thumbnail.addEventListener('click', function() {
            const videoSrc = this.getAttribute('data-video');
            videoWrapper.innerHTML = `<video controls autoplay>
                <source src="${videoSrc}" type="video/mp4">
                Your browser does not support the video tag.
            </video>`;
            videoModal.classList.add('active');
            document.body.style.overflow = 'hidden';
        });
    });

    videoModalClose.addEventListener('click', function() {
        videoModal.classList.remove('active');
        videoWrapper.innerHTML = '';
        document.body.style.overflow = '';
    });

    videoModal.addEventListener('click', function(e) {
        if (e.target === videoModal) {
            videoModal.classList.remove('active');
            videoWrapper.innerHTML = '';
            document.body.style.overflow = '';
        }
    });

    // Share functionality
    const shareButtons = document.querySelectorAll('.btn-share');

    shareButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const title = this.getAttribute('data-title');
            const url = window.location.href;

            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                }).catch(console.error);
            } else {
                // Fallback for browsers that don't support Web Share API
                prompt('Copy this link to share:', url);
            }
        });
    });

    // Add animations to story cards
    const storyCards = document.querySelectorAll('.story-card');

    storyCards.forEach((card, index) => {
        // Stagger animation delay
        const delay = index * 0.1;

        // Apply animation with GSAP if available
        if (typeof gsap !== 'undefined') {
            gsap.from(card, {
                y: 50,
                duration: 0.8,
                delay: delay,
                ease: 'power2.out'
            });
        }
    });
});
</script>
