/**
 * Enhanced CSS for Content Management Page
 */

/* ===== Content Management ===== */
.content-management {
    animation: fadeIn 0.5s ease-out;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Ripple Effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    animation: ripple 0.6s linear;
    transform: scale(0);
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.content-management .page-title {
    margin-bottom: var(--spacing-lg);
}

/* Content Tabs Container */
.content-tabs-container {
    margin-top: var(--spacing-xl);
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--color-gray);
    overflow: hidden;
}

/* Content Search */
.content-search {
    padding: var(--spacing-md);
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid var(--color-gray);
    position: relative;
}

.content-search input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-left: 40px;
    background-color: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--color-gray);
    border-radius: var(--border-radius-sm);
    color: var(--color-white);
    font-size: 1rem;
    transition: all var(--transition-normal);
}

.content-search input:focus {
    border-color: var(--color-gold);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
    outline: none;
}

.content-search i {
    position: absolute;
    left: 25px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-gold);
    font-size: 1.2rem;
}

html[dir="rtl"] .content-search input {
    padding-left: var(--spacing-md);
    padding-right: 40px;
}

html[dir="rtl"] .content-search i {
    left: auto;
    right: 25px;
}

/* Content Tabs */
.content-tabs {
    display: flex;
    overflow-x: auto;
    background-color: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid var(--color-gray);
    padding: 0 var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 10;
}

.content-tab {
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    color: var(--color-white);
    white-space: nowrap;
    position: relative;
    transition: all var(--transition-normal);
    font-weight: 500;
}

.content-tab:hover {
    color: var(--color-gold);
}

.content-tab.active {
    color: var(--color-gold);
}

.content-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--color-gold);
    border-radius: 3px 3px 0 0;
}

/* Content Panels */
.content-panel {
    display: none;
    padding: var(--spacing-lg);
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

.content-panel.active {
    display: block;
    opacity: 1;
    animation: fadeIn 0.5s ease-out;
}

.content-panel h2 {
    color: var(--color-gold);
    margin-bottom: var(--spacing-lg);
    font-size: 1.5rem;
    position: relative;
    display: inline-block;
    padding-bottom: var(--spacing-xs);
}

.content-panel h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--color-gold);
    border-radius: 1px;
}

/* Content Items */
.content-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.content-item {
    background-color: var(--color-dark-gray);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    border: 1px solid var(--color-gray);
    animation: fadeIn 0.5s ease-out;
}

.content-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
    border-color: var(--color-gold);
}

.content-item-header {
    background-color: rgba(0, 0, 0, 0.2);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-item-title {
    margin-bottom: 0;
    color: var(--color-gold);
    font-size: 1.2rem;
    font-weight: 600;
}

.content-item-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.content-item-body {
    padding: var(--spacing-md);
}

.content-value {
    margin-bottom: var(--spacing-md);
}

.content-value:last-child {
    margin-bottom: 0;
}

.content-label {
    font-weight: 500;
    color: var(--color-gold);
    margin-bottom: var(--spacing-xs);
    font-size: 0.9rem;
}

.content-text {
    background-color: rgba(0, 0, 0, 0.2);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--color-gray);
    color: var(--color-white);
    max-height: 150px;
    overflow-y: auto;
    white-space: pre-wrap;
}

/* Add Content Form */
.content-management .card {
    background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
    border: 1px solid var(--color-gray);
    position: relative;
    overflow: hidden;
}

.content-management .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%);
    z-index: 0;
}

.content-management .card-header,
.content-management .card-body {
    position: relative;
    z-index: 1;
}

.content-management .card-header {
    background-color: rgba(0, 0, 0, 0.3);
}

.content-management .form-group label {
    font-weight: 600;
}

.content-management .form-group input,
.content-management .form-group textarea {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid var(--color-gray);
}

.content-management .form-group input:focus,
.content-management .form-group textarea:focus {
    border-color: var(--color-gold);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--color-dark);
    border-radius: var(--border-radius-md);
    width: 90%;
    max-width: 600px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    position: relative;
    border: 1px solid var(--color-gray);
    animation: slideIn 0.3s ease-out;
    overflow: hidden;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-gray);
    background-color: rgba(0, 0, 0, 0.3);
}

.modal-header h2 {
    margin-bottom: 0;
    font-size: 1.5rem;
    color: var(--color-gold);
}

.modal-close {
    background: none;
    border: none;
    color: var(--color-white);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--color-gold);
    transform: rotate(90deg);
}

.modal-body {
    padding: var(--spacing-md);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .content-items {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .content-tabs {
        flex-wrap: wrap;
    }

    .content-tab {
        flex: 1 0 auto;
        text-align: center;
    }
}

@media (max-width: 576px) {
    .content-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .content-item-actions {
        margin-top: var(--spacing-sm);
    }
}
