/* Celebrity Profile Page Styles */
:root {
    --celebrity-bg-color: #000000;
    --celebrity-text-color: #ffffff;
    --celebrity-gold: #d4af37;
    --celebrity-gold-light: #e5c158;
    --celebrity-gold-dark: #b38728;
    --celebrity-card-bg: rgba(17, 17, 17, 0.7);
    --celebrity-card-hover-bg: rgba(26, 26, 26, 0.7);
    --celebrity-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    --celebrity-border-radius: 8px;
    --celebrity-transition: all 0.3s ease;
}

.celebrity-profile-section {
    background: linear-gradient(to bottom, var(--celebrity-bg-color), #111111);
    padding: 40px 0 60px;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.celebrity-profile-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(212, 175, 55, 0.05), transparent 70%);
    pointer-events: none;
}

.celebrity-profile-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.back-to-celebrities {
    position: absolute;
    left: 0;
    top: 10px;
    display: flex;
    align-items: center;
    color: var(--celebrity-text-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--celebrity-transition);
}

.back-to-celebrities i {
    margin-right: 5px;
}

.back-to-celebrities:hover {
    color: var(--celebrity-gold);
    transform: translateX(-5px);
}

.celebrity-profile-container {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-bottom: 40px;
}

.celebrity-profile-image {
    flex: 0 0 350px;
    position: relative;
    border-radius: var(--celebrity-border-radius);
    overflow: hidden;
    box-shadow: var(--celebrity-shadow);
    height: 500px;
}

.celebrity-profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.celebrity-profile-role {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: var(--celebrity-gold);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    z-index: 1;
    border: 1px solid rgba(212, 175, 55, 0.3);
    backdrop-filter: blur(5px);
}

.celebrity-profile-info {
    flex: 1;
    min-width: 300px;
    background: var(--celebrity-card-bg);
    border-radius: var(--celebrity-border-radius);
    padding: 30px;
    box-shadow: var(--celebrity-shadow);
    border: 1px solid rgba(212, 175, 55, 0.1);
    backdrop-filter: blur(5px);
}

.celebrity-profile-name {
    color: var(--celebrity-gold);
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.celebrity-profile-name::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--celebrity-gold), transparent);
}

.celebrity-profile-bio {
    color: var(--celebrity-text-color);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 30px;
    opacity: 0.9;
}

.celebrity-profile-social {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.celebrity-profile-social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--celebrity-text-color);
    transition: var(--celebrity-transition);
    text-decoration: none;
    font-size: 1.2rem;
}

.celebrity-profile-social-icon:hover {
    background: var(--celebrity-gold);
    color: #000;
    transform: translateY(-5px);
}

.celebrity-profile-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.celebrity-profile-btn {
    padding: 12px 25px;
    border-radius: 30px;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--celebrity-transition);
    cursor: pointer;
    text-decoration: none;
}

.celebrity-profile-btn i {
    margin-right: 8px;
}

.btn-vote {
    background: linear-gradient(145deg, var(--celebrity-gold), var(--celebrity-gold-dark));
    color: #000;
    border: none;
}

.btn-share {
    background: rgba(255, 255, 255, 0.1);
    color: var(--celebrity-text-color);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-watch {
    background: rgba(212, 175, 55, 0.2);
    color: var(--celebrity-gold);
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.btn-vote:hover, .btn-share:hover, .btn-watch:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.btn-watch:hover {
    background: rgba(212, 175, 55, 0.3);
}

.celebrity-profile-video {
    width: 100%;
    margin-top: 40px;
    border-radius: var(--celebrity-border-radius);
    overflow: hidden;
    box-shadow: var(--celebrity-shadow);
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border: 1px solid rgba(212, 175, 55, 0.1);
    backdrop-filter: blur(5px);
}

.video-caption {
    text-align: center;
    margin-top: 15px;
    color: var(--celebrity-text-color);
    opacity: 0.8;
    font-style: italic;
}

.celebrity-profile-video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
}

.celebrity-profile-video-container iframe,
.celebrity-profile-video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

.celebrity-profile-section h2 {
    color: var(--celebrity-gold);
    font-size: 1.8rem;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.celebrity-profile-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 2px;
    background: linear-gradient(to right, var(--celebrity-gold), transparent);
}

/* Related celebrities section */
.related-celebrities {
    margin-top: 60px;
}

.related-celebrities-title {
    text-align: left;
    margin-bottom: 30px;
}

.related-celebrities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

/* Responsive styles */
@media (max-width: 992px) {
    .celebrity-profile-image {
        flex: 0 0 300px;
        height: 450px;
    }

    .celebrity-profile-name {
        font-size: 2.2rem;
    }
}

@media (max-width: 768px) {
    .celebrity-profile-container {
        flex-direction: column;
    }

    .celebrity-profile-image {
        flex: 0 0 auto;
        height: 400px;
        width: 100%;
        max-width: 350px;
        margin: 0 auto;
    }

    .celebrity-profile-info {
        width: 100%;
    }

    .celebrity-profile-name {
        font-size: 2rem;
    }

    .related-celebrities-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 576px) {
    .celebrity-profile-section {
        padding: 30px 0 40px;
    }

    .celebrity-profile-image {
        height: 350px;
    }

    .celebrity-profile-name {
        font-size: 1.8rem;
    }

    .celebrity-profile-bio {
        font-size: 1rem;
    }

    .celebrity-profile-actions {
        flex-direction: column;
    }

    .related-celebrities-grid {
        grid-template-columns: 1fr;
        max-width: 280px;
        margin: 0 auto;
    }
}
