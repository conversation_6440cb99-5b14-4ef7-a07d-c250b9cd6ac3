<?php
/**
 * Admin Settings Page
 */

// Process form submission
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $message = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
        $message_type = 'danger';
    } else {
        $action = isset($_POST['action']) ? $_POST['action'] : '';

        if ($action === 'change_password') {
            // Change password
            $current_password = isset($_POST['current_password']) ? $_POST['current_password'] : '';
            $new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';
            $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';

            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                $message = 'يرجى ملء جميع الحقول.';
                $message_type = 'danger';
            } elseif ($new_password !== $confirm_password) {
                $message = 'كلمة المرور الجديدة وتأكيدها غير متطابقين.';
                $message_type = 'danger';
            } elseif (strlen($new_password) < 6) {
                $message = 'يجب أن تكون كلمة المرور الجديدة 6 أحرف على الأقل.';
                $message_type = 'danger';
            } else {
                // Get current user's password
                $user_id = $_SESSION['user_id'];
                $get_user_sql = "SELECT password FROM users WHERE id = ?";
                $get_user_stmt = $conn->prepare($get_user_sql);
                $get_user_stmt->bind_param('i', $user_id);
                $get_user_stmt->execute();
                $get_user_result = $get_user_stmt->get_result();

                if ($get_user_result && $get_user_result->num_rows > 0) {
                    $current_hash = $get_user_result->fetch_assoc()['password'];

                    // Verify current password
                    if (password_verify($current_password, $current_hash)) {
                        // Hash new password
                        $new_hash = password_hash($new_password, PASSWORD_DEFAULT);

                        // Update password
                        $update_sql = "UPDATE users SET password = ? WHERE id = ?";
                        $update_stmt = $conn->prepare($update_sql);
                        $update_stmt->bind_param('si', $new_hash, $user_id);

                        if ($update_stmt->execute()) {
                            $message = 'تم تغيير كلمة المرور بنجاح.';
                            $message_type = 'success';
                        } else {
                            $message = 'حدث خطأ أثناء تغيير كلمة المرور: ' . $update_stmt->error;
                            $message_type = 'danger';
                        }
                    } else {
                        $message = 'كلمة المرور الحالية غير صحيحة.';
                        $message_type = 'danger';
                    }
                } else {
                    $message = 'حدث خطأ أثناء التحقق من المستخدم.';
                    $message_type = 'danger';
                }
            }
        } elseif ($action === 'update_profile') {
            // Update profile
            $full_name = isset($_POST['full_name']) ? trim($_POST['full_name']) : '';
            $email = isset($_POST['email']) ? trim($_POST['email']) : '';
            $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';

            if (empty($full_name) || empty($email)) {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            } else {
                $user_id = $_SESSION['user_id'];

                // Check if email already exists
                $check_sql = "SELECT id FROM users WHERE email = ? AND id != ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param('si', $email, $user_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();

                if ($check_result->num_rows > 0) {
                    $message = 'البريد الإلكتروني مستخدم بالفعل.';
                    $message_type = 'danger';
                } else {
                    // Update profile
                    $update_sql = "UPDATE users SET full_name = ?, email = ?, phone = ? WHERE id = ?";
                    $update_stmt = $conn->prepare($update_sql);
                    $update_stmt->bind_param('sssi', $full_name, $email, $phone, $user_id);

                    if ($update_stmt->execute()) {
                        // Update session
                        $_SESSION['full_name'] = $full_name;

                        $message = 'تم تحديث الملف الشخصي بنجاح.';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء تحديث الملف الشخصي: ' . $update_stmt->error;
                        $message_type = 'danger';
                    }
                }
            }
        } elseif ($action === 'clear_cache') {
            // Clear cache
            $cache_dirs = [
                UPLOAD_DIR . 'cache/',
                __DIR__ . '/../../assets/cache/'
            ];

            $cleared = false;

            foreach ($cache_dirs as $dir) {
                if (is_dir($dir)) {
                    $files = glob($dir . '*');

                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                            $cleared = true;
                        }
                    }
                }
            }

            if ($cleared) {
                $message = 'تم مسح ذاكرة التخزين المؤقت بنجاح.';
                $message_type = 'success';
            } else {
                $message = 'لا توجد ملفات مؤقتة للمسح.';
                $message_type = 'info';
            }
        }
    }
}

// Get current user data
$user_id = $_SESSION['user_id'];
$user_sql = "SELECT username, email, full_name, phone FROM users WHERE id = ?";
$user_stmt = $conn->prepare($user_sql);
$user_stmt->bind_param('i', $user_id);
$user_stmt->execute();
$user_result = $user_stmt->get_result();
$user = $user_result->fetch_assoc();

// Get system information
$php_version = phpversion();
$mysql_version = $conn->server_info;
$server_software = $_SERVER['SERVER_SOFTWARE'];
$upload_max_filesize = ini_get('upload_max_filesize');
$post_max_size = ini_get('post_max_size');
$memory_limit = ini_get('memory_limit');
$max_execution_time = ini_get('max_execution_time');
?>

<div class="settings-page">
    <h1 class="page-title">الإعدادات</h1>

    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?>">
        <?php echo $message; ?>
    </div>
    <?php endif; ?>

    <div class="settings-tabs">
        <div class="tab active" data-tab="profile">الملف الشخصي</div>
        <div class="tab" data-tab="password">تغيير كلمة المرور</div>
        <div class="tab" data-tab="system">معلومات النظام</div>
    </div>

    <div class="tab-content">
        <!-- Profile Tab -->
        <div class="tab-pane active" id="profile">
            <div class="card">
                <div class="card-header">
                    <h2>تحديث الملف الشخصي</h2>
                </div>
                <div class="card-body">
                    <form action="index.php?page=settings" method="post">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="update_profile">

                        <div class="form-group">
                            <label for="username">اسم المستخدم</label>
                            <input type="text" id="username" value="<?php echo htmlspecialchars($user['username']); ?>" readonly disabled>
                            <small class="form-text">لا يمكن تغيير اسم المستخدم.</small>
                        </div>

                        <div class="form-group">
                            <label for="full_name">الاسم الكامل</label>
                            <input type="text" id="full_name" name="full_name" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="phone">الهاتف</label>
                            <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($user['phone']); ?>">
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Password Tab -->
        <div class="tab-pane" id="password">
            <div class="card">
                <div class="card-header">
                    <h2>تغيير كلمة المرور</h2>
                </div>
                <div class="card-body">
                    <form action="index.php?page=settings" method="post">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="change_password">

                        <div class="form-group">
                            <label for="current_password">كلمة المرور الحالية</label>
                            <input type="password" id="current_password" name="current_password" required>
                        </div>

                        <div class="form-group">
                            <label for="new_password">كلمة المرور الجديدة</label>
                            <input type="password" id="new_password" name="new_password" required>
                            <small class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل.</small>
                        </div>

                        <div class="form-group">
                            <label for="confirm_password">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" id="confirm_password" name="confirm_password" required>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">تغيير كلمة المرور</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- System Tab -->
        <div class="tab-pane" id="system">
            <div class="card">
                <div class="card-header">
                    <h2>معلومات النظام</h2>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-item">
                            <div class="info-label">إصدار PHP</div>
                            <div class="info-value"><?php echo htmlspecialchars($php_version); ?></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">إصدار MySQL</div>
                            <div class="info-value"><?php echo htmlspecialchars($mysql_version); ?></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">برنامج الخادم</div>
                            <div class="info-value"><?php echo htmlspecialchars($server_software); ?></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">الحد الأقصى لحجم الملف</div>
                            <div class="info-value"><?php echo htmlspecialchars($upload_max_filesize); ?></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">الحد الأقصى لحجم POST</div>
                            <div class="info-value"><?php echo htmlspecialchars($post_max_size); ?></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">حد الذاكرة</div>
                            <div class="info-value"><?php echo htmlspecialchars($memory_limit); ?></div>
                        </div>

                        <div class="info-item">
                            <div class="info-label">الحد الأقصى لوقت التنفيذ</div>
                            <div class="info-value"><?php echo htmlspecialchars($max_execution_time); ?> ثانية</div>
                        </div>
                    </div>

                    <div class="system-actions">
                        <form action="index.php?page=settings" method="post">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="clear_cache">

                            <button type="submit" class="btn btn-secondary">
                                <i class="fas fa-trash"></i> مسح ذاكرة التخزين المؤقت
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching - Override the default behavior from admin-pages.js
    const settingsTabs = document.querySelectorAll('.settings-tabs .tab');
    const settingsPanes = document.querySelectorAll('.tab-pane');

    // Remove any existing event listeners by cloning and replacing elements
    settingsTabs.forEach(tab => {
        const newTab = tab.cloneNode(true);
        tab.parentNode.replaceChild(newTab, tab);
    });

    // Add our own event listeners
    document.querySelectorAll('.settings-tabs .tab').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent event bubbling

            const tabId = this.getAttribute('data-tab');

            // Remove active class from all tabs and panes
            document.querySelectorAll('.settings-tabs .tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(p => {
                p.classList.remove('active');
                p.style.display = 'none'; // Explicitly hide
            });

            // Add active class to clicked tab and corresponding pane
            this.classList.add('active');
            const activePane = document.getElementById(tabId);
            activePane.classList.add('active');
            activePane.style.display = 'block'; // Explicitly show
        });
    });

    // Password confirmation validation
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');

    if (newPasswordInput && confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            if (this.value !== newPasswordInput.value) {
                this.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        newPasswordInput.addEventListener('input', function() {
            if (confirmPasswordInput.value !== '' && confirmPasswordInput.value !== this.value) {
                confirmPasswordInput.setCustomValidity('كلمات المرور غير متطابقة');
            } else {
                confirmPasswordInput.setCustomValidity('');
            }
        });
    }
});
</script>
