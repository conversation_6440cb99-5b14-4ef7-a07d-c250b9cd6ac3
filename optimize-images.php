<?php
/**
 * أداة تحسين الصور مع تحديث قواعد البيانات وحذف الصور الغير مستخدمة
 */

require_once 'config/config.php';
require_once 'config/database.php';

$JPEG_QUALITY = 85;
$PNG_COMPRESSION = 6;
$WEBP_QUALITY = 80;
$MAX_WIDTH = 1920;
$MAX_HEIGHT = 1080;

echo "<h2>🖼️ أداة تحسين الصور</h2>";

$image_directories = [
    'assets/uploads/',
    'assets/img/',
    'img/',
    'assets/img/influencers/'
];

$total_original_size = 0;
$total_optimized_size = 0;
$optimized_count = 0;

foreach ($image_directories as $dir) {
    if (!is_dir($dir)) continue;

    echo "<h3>📁 معالجة مجلد: $dir</h3>";
    $files = glob($dir . '*.{jpg,jpeg,png,gif,JPG,PNG,GIF,JPEG}', GLOB_BRACE);

    foreach ($files as $file) {
        $original_size = filesize($file);
        $total_original_size += $original_size;

        echo "<div style='margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;'>";
        echo "<strong>📷 " . basename($file) . "</strong><br>";
        echo "الحجم الأصلي: " . formatBytes($original_size) . "<br>";

        $optimized = optimizeImage($file, $JPEG_QUALITY, $PNG_COMPRESSION, $WEBP_QUALITY, $MAX_WIDTH, $MAX_HEIGHT);

        if ($optimized) {
            $new_size = filesize($file);
            $total_optimized_size += $new_size;
            $saved = $original_size - $new_size;
            $percentage = round(($saved / $original_size) * 100, 2);

            echo "✅ الحجم الجديد: " . formatBytes($new_size) . "<br>";
            echo "🎯 توفير: " . formatBytes($saved) . " ($percentage%)<br>";

            createWebPVersion($file, $WEBP_QUALITY);
            $optimized_count++;
        } else {
            echo "❌ فشل في التحسين<br>";
            $total_optimized_size += $original_size;
        }

        echo "</div>";
    }
}

$total_saved = $total_original_size - $total_optimized_size;
$total_percentage = $total_original_size > 0 ? round(($total_saved / $total_original_size) * 100, 2) : 0;

echo "<div style='background: #e8f5e8; padding: 20px; margin: 20px 0; border-radius: 10px;'>";
echo "<h3>📊 إحصائيات التحسين</h3>";
echo "<p><strong>عدد الصور المحسنة:</strong> $optimized_count</p>";
echo "<p><strong>الحجم الأصلي:</strong> " . formatBytes($total_original_size) . "</p>";
echo "<p><strong>الحجم بعد التحسين:</strong> " . formatBytes($total_optimized_size) . "</p>";
echo "<p><strong>إجمالي التوفير:</strong> " . formatBytes($total_saved) . " ($total_percentage%)</p>";
echo "</div>";

echo "<br><a href='index.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة</a>";

function optimizeImage($file, $jpeg_quality, $png_compression, $webp_quality, $max_width, $max_height) {
    $info = getimagesize($file);
    if (!$info) return false;

    $mime = $info['mime'];
    $width = $info[0];
    $height = $info[1];

    switch ($mime) {
        case 'image/jpeg': $image = imagecreatefromjpeg($file); break;
        case 'image/png':  $image = imagecreatefrompng($file); break;
        case 'image/gif':  $image = imagecreatefromgif($file); break;
        default: return false;
    }

    if (!$image) return false;

    if ($width > $max_width || $height > $max_height) {
        $ratio = min($max_width / $width, $max_height / $height);
        $new_width = round($width * $ratio);
        $new_height = round($height * $ratio);
        $resized = imagecreatetruecolor($new_width, $new_height);

        if ($mime == 'image/png') {
            imagealphablending($resized, false);
            imagesavealpha($resized, true);
            $transparent = imagecolorallocatealpha($resized, 255, 255, 255, 127);
            imagefill($resized, 0, 0, $transparent);
        }

        imagecopyresampled($resized, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
        imagedestroy($image);
        $image = $resized;
    }

    $result = false;
    switch ($mime) {
        case 'image/jpeg': $result = imagejpeg($image, $file, $jpeg_quality); break;
        case 'image/png':  $result = imagepng($image, $file, $png_compression); break;
        case 'image/gif':  $result = imagegif($image, $file); break;
    }

    imagedestroy($image);
    return $result;
}

function createWebPVersion($file, $quality) {
    global $conn;

    if (!function_exists('imagewebp')) return false;
    $info = getimagesize($file);
    if (!$info) return false;

    $mime = $info['mime'];
    switch ($mime) {
        case 'image/jpeg': $image = imagecreatefromjpeg($file); break;
        case 'image/png':  $image = imagecreatefrompng($file); break;
        default: return false;
    }

    if (!$image) return false;

    $webp_file = preg_replace('/\.(jpg|jpeg|png)$/i', '.webp', $file);
    $result = imagewebp($image, $webp_file, $quality);
    imagedestroy($image);

    if ($result) {
        echo "✅ تم إنشاء WebP: " . basename($webp_file) . "<br>";
        updateImagePathInDatabase(basename($file), basename($webp_file));
        deleteUnusedOriginalImage($file);
    }

    return $result;
}

function updateImagePathInDatabase($old_filename, $new_filename) {
    global $conn;

    $tables = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
    }

    foreach ($tables as $table) {
        $columns_result = $conn->query("SHOW COLUMNS FROM `$table`");
        while ($col = $columns_result->fetch_assoc()) {
            $field = $col['Field'];
            $type = $col['Type'];

            if (preg_match('/text|varchar/i', $type)) {
                $sql = "UPDATE `$table` SET `$field` = REPLACE(`$field`, ?, ?) WHERE `$field` LIKE ?";
                $stmt = $conn->prepare($sql);
                $like = "%$old_filename";
                $stmt->bind_param("sss", $old_filename, $new_filename, $like);
                $stmt->execute();
                if ($stmt->affected_rows > 0) {
                    echo "🔁 تم تحديث $table.$field: $old_filename ➔ $new_filename<br>";
                }
            }
        }
    }
}

function deleteUnusedOriginalImage($original_filename) {
    global $conn;
    $still_used = false;

    $tables = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
    }

    foreach ($tables as $table) {
        $columns_result = $conn->query("SHOW COLUMNS FROM `$table`");
        while ($col = $columns_result->fetch_assoc()) {
            $field = $col['Field'];
            $type = $col['Type'];

            if (preg_match('/text|varchar/i', $type)) {
                $sql = "SELECT COUNT(*) AS count FROM `$table` WHERE `$field` LIKE ?";
                $stmt = $conn->prepare($sql);
                $like = "%$original_filename";
                $stmt->bind_param("s", $like);
                $stmt->execute();
                $res = $stmt->get_result();
                $row = $res->fetch_assoc();
                if ($row['count'] > 0) {
                    $still_used = true;
                    break 2;
                }
            }
        }
    }

    if (!$still_used && file_exists($original_filename)) {
        unlink($original_filename);
        echo "🗑️ تم حذف الأصل: " . basename($original_filename) . "<br>";
    } else {
        echo "ℹ️ الأصل ما زال مستخدم: " . basename($original_filename) . "<br>";
    }
}

if (!function_exists('formatBytes')) {
    function formatBytes($size, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, $precision) . ' ' . $units[$i];
    }
}
?>
