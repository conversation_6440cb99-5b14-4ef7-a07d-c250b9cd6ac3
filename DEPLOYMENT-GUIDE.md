# 🚀 دليل النشر للاستضافة - مهرجان وسائل التواصل الاجتماعي

## 📋 قائمة المراجعة قبل النشر

### ✅ الملفات المطلوبة:
- [ ] جميع ملفات PHP
- [ ] مجلد assets/ كامل
- [ ] ملف .htaccess
- [ ] ملف sw.js (Service Worker)
- [ ] ملفات التحسين (optimize-*.php)

### ✅ قاعدة البيانات:
- [ ] تصدير قاعدة البيانات
- [ ] تحديث إعدادات الاتصال في config.php
- [ ] اختبار الاتصال

### ✅ الإعدادات:
- [ ] تحديث URLs في الملفات
- [ ] تحديث مسارات الملفات
- [ ] فحص صلاحيات المجلدات

## 🛠️ خطوات النشر

### 1. تحضير الملفات:
```bash
# ضغط الملفات
zip -r website.zip . -x "*.git*" "*.DS_Store*" "node_modules/*"

# أو استخدام FTP/SFTP
# رفع جميع الملفات للخادم
```

### 2. إعداد قاعدة البيانات:
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE social_media_festival;

-- استيراد البيانات
mysql -u username -p social_media_festival < database.sql
```

### 3. تحديث الإعدادات:
```php
// في includes/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'your_database_name');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

// تحديث URL الأساسي
define('BASE_URL', 'https://yourdomain.com/');
```

### 4. ضبط صلاحيات المجلدات:
```bash
chmod 755 assets/uploads/
chmod 644 .htaccess
chmod 644 sw.js
```

## ⚡ تشغيل أدوات التحسين

### بعد النشر مباشرة:
1. **زيارة** `yourdomain.com/optimize-website.php`
2. **تشغيل تحسين الصور** لضغط جميع الصور
3. **تشغيل تحسين قاعدة البيانات** لإضافة الفهارس
4. **اختبار الأداء** للتأكد من السرعة

### الصيانة الدورية:
- **أسبوعياً**: فحص الأداء
- **شهرياً**: تحسين قاعدة البيانات
- **عند إضافة صور جديدة**: تشغيل تحسين الصور

## 🔧 متطلبات الخادم

### الحد الأدنى:
- **PHP**: 7.4+
- **MySQL**: 5.7+
- **Apache**: 2.4+ مع mod_rewrite
- **مساحة القرص**: 1GB+
- **الذاكرة**: 512MB+

### الموصى به:
- **PHP**: 8.0+
- **MySQL**: 8.0+
- **SSD Storage**: لسرعة أفضل
- **CDN**: لتوزيع المحتوى
- **SSL Certificate**: للأمان

### الإضافات المطلوبة:
```php
// فحص الإضافات
extension_loaded('gd');        // معالجة الصور
extension_loaded('mysqli');    // قاعدة البيانات
extension_loaded('curl');      // طلبات HTTP
extension_loaded('json');      // معالجة JSON
extension_loaded('mbstring');  // النصوص متعددة البايت
```

## 🔒 الأمان

### إعدادات مهمة:
```apache
# في .htaccess
# حماية الملفات الحساسة
<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# منع الوصول للمجلدات الحساسة
Options -Indexes
```

### كلمات المرور:
- [ ] تغيير كلمة مرور قاعدة البيانات
- [ ] تغيير كلمات مرور الأدمن
- [ ] استخدام كلمات مرور قوية

## 📊 مراقبة الأداء

### أدوات مفيدة:
- **Google PageSpeed Insights**: قياس السرعة
- **GTmetrix**: تحليل شامل للأداء
- **Pingdom**: مراقبة وقت التشغيل
- **Google Analytics**: تحليل الزوار

### مؤشرات مهمة:
- **وقت التحميل**: أقل من 3 ثوان
- **First Contentful Paint**: أقل من 1.8 ثانية
- **Largest Contentful Paint**: أقل من 2.5 ثانية
- **Cumulative Layout Shift**: أقل من 0.1

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

#### الصور لا تظهر:
```bash
# فحص الصلاحيات
ls -la assets/uploads/
chmod 755 assets/uploads/
```

#### خطأ 500:
```bash
# فحص ملف الأخطاء
tail -f /var/log/apache2/error.log
# أو
tail -f error_log
```

#### بطء في التحميل:
1. تشغيل أدوات التحسين
2. فحص حجم الصور
3. تفعيل الكاش
4. استخدام CDN

#### مشاكل قاعدة البيانات:
```sql
-- فحص الاتصال
SHOW PROCESSLIST;

-- تحسين الجداول
OPTIMIZE TABLE users, votes, nominees;

-- فحص الفهارس
SHOW INDEX FROM users;
```

## 📈 تحسينات إضافية

### للمواقع عالية الزيارات:
1. **استخدام CDN** (Cloudflare, AWS CloudFront)
2. **تفعيل Redis** للكاش
3. **Load Balancer** لتوزيع الأحمال
4. **Database Clustering** لقاعدة البيانات

### تحسينات SEO:
1. **Sitemap XML** لمحركات البحث
2. **Meta Tags** محسنة
3. **Schema Markup** للبيانات المنظمة
4. **Open Graph** لوسائل التواصل

## 📞 الدعم

### في حالة المشاكل:
1. **فحص ملفات السجلات** أولاً
2. **تشغيل أدوات التشخيص** المدمجة
3. **مراجعة الوثائق** في README-OPTIMIZATION.md
4. **التواصل مع فريق التطوير** عند الحاجة

### معلومات مفيدة للدعم:
- إصدار PHP
- إصدار MySQL
- نوع الخادم (Apache/Nginx)
- رسائل الأخطاء الكاملة
- خطوات إعادة إنتاج المشكلة

---

## 🎉 تهانينا!

موقع مهرجان وسائل التواصل الاجتماعي جاهز للنشر مع:
- ⚡ **أداء ممتاز**
- 🔒 **أمان عالي**
- 📱 **تجربة مستخدم رائعة**
- 🚀 **سرعة فائقة**

**نصيحة أخيرة**: احتفظ بنسخ احتياطية دورية واستخدم أدوات التحسين بانتظام للحفاظ على الأداء الممتاز! 🌟
