/* ===== Enhanced Dashboard Styles ===== */

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.dashboard-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.date-display {
    display: flex;
    align-items: center;
    background-color: var(--color-black);
    padding: 8px 15px;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-gray);
    color: var(--color-gold);
}

.date-display i {
    margin-right: 8px;
}

/* Welcome Banner */
.welcome-banner {
    background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--color-gray);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.welcome-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%);
    z-index: 0;
}

.welcome-content {
    position: relative;
    z-index: 1;
    flex: 1;
}

.welcome-content h2 {
    color: var(--color-gold);
    margin-bottom: var(--spacing-sm);
    font-size: 1.8rem;
}

.welcome-content p {
    color: var(--color-light);
    max-width: 600px;
    line-height: 1.6;
}

.welcome-image {
    position: relative;
    z-index: 1;
    width: 100px;
    height: 100px;
    background-color: rgba(212, 175, 55, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: var(--spacing-lg);
    border: 2px solid rgba(212, 175, 55, 0.3);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.2);
}

.welcome-image i {
    font-size: 3rem;
    color: var(--color-gold);
}

/* Section Titles */
.section-title {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    color: var(--color-gold);
    font-size: 1.4rem;
    position: relative;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.section-title i {
    margin-right: 10px;
    background-color: rgba(212, 175, 55, 0.1);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Stats Overview */
.stats-overview {
    margin-bottom: var(--spacing-lg);
}

.stat-progress {
    height: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin-top: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--color-gold) 0%, #f1e5ac 100%);
    border-radius: 3px;
    transition: width 0.5s ease;
}

.stat-card.highlight {
    border-color: var(--color-gold);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(20, 20, 20, 0.9) 100%);
}

.stat-card.highlight .stat-icon {
    background: linear-gradient(135deg, var(--color-gold) 0%, #f1e5ac 100%);
}

/* Quick Actions */
.quick-actions {
    margin-bottom: var(--spacing-lg);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.action-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-gray);
    color: var(--color-white);
    text-decoration: none;
    transition: all var(--transition-normal);
    text-align: center;
    height: 120px;
}

.action-button i {
    font-size: 2rem;
    color: var(--color-gold);
    margin-bottom: var(--spacing-sm);
    transition: all var(--transition-normal);
}

.action-button span {
    font-weight: 600;
}

.action-button:hover {
    transform: translateY(-5px);
    border-color: var(--color-gold);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.action-button:hover i {
    transform: scale(1.2);
}

/* Charts Section */
.dashboard-charts-section {
    margin-bottom: var(--spacing-lg);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.chart-actions {
    display: flex;
    gap: 5px;
}

.chart-body {
    height: 250px;
}

/* Tables Section */
.dashboard-tables-section {
    margin-bottom: var(--spacing-lg);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.table-responsive {
    overflow-x: auto;
    max-height: 400px;
    overflow-y: auto;
}

.user-info, .nominee-info {
    display: flex;
    align-items: center;
}

.user-avatar, .nominee-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: var(--color-gold);
    color: var(--color-black);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
}

.user-username {
    font-size: 0.8rem;
    color: var(--color-light-gray);
}

.category-badge {
    background-color: rgba(212, 175, 55, 0.1);
    color: var(--color-gold);
    padding: 3px 8px;
    border-radius: 20px;
    font-size: 0.8rem;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.date-info {
    display: flex;
    flex-direction: column;
}

.date {
    font-weight: 600;
}

.time {
    font-size: 0.8rem;
    color: var(--color-light-gray);
}

.progress-container {
    display: flex;
    align-items: center;
    width: 100%;
}

.progress-container .progress-bar {
    flex: 1;
    margin-right: 10px;
}

.progress-text {
    font-weight: 600;
    color: var(--color-gold);
    width: 40px;
    text-align: right;
}

.no-data-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg) 0;
}

.no-data-icon {
    font-size: 3rem;
    color: rgba(212, 175, 55, 0.2);
    margin-bottom: var(--spacing-md);
}

/* System Status */
.system-status {
    margin-bottom: var(--spacing-lg);
}

.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.status-card {
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    border: 1px solid var(--color-gray);
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    color: var(--color-gold);
    font-size: 1.2rem;
}

.status-content {
    flex: 1;
}

.status-content h3 {
    margin-bottom: 5px;
    font-size: 1rem;
}

.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.status-badge.warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-badge.danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.status-progress {
    height: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    margin-top: 5px;
    position: relative;
}

.status-progress .progress-bar {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.status-progress .progress-text {
    position: absolute;
    right: 0;
    top: -20px;
    font-size: 0.8rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .welcome-banner {
        flex-direction: column;
        text-align: center;
    }
    
    .welcome-content {
        margin-bottom: var(--spacing-md);
    }
    
    .welcome-image {
        margin-left: 0;
    }
    
    .action-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .dashboard-actions {
        margin-top: var(--spacing-sm);
        width: 100%;
        justify-content: space-between;
    }
    
    .action-buttons {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .status-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .status-cards {
        grid-template-columns: 1fr;
    }
}
