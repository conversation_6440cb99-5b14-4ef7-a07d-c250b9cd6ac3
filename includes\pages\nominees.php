<?php
/**
 * Nominees Page
 */

// Get all categories for filter
$categories = get_main_categories();

// Get selected category from URL
$selected_category = isset($_GET['category']) ? (int)$_GET['category'] : 0;

// Get current page from URL
$current_page = isset($_GET['p']) ? (int)$_GET['p'] : 1;
$per_page = 12; // Number of nominees per page

// Get nominees based on selected category
$nominees = [];
$total_nominees = 0;

// Function to get nominees with pagination
function get_nominees_paginated($category_id = 0, $page = 1, $per_page = 12) {
    global $conn;

    // Calculate offset
    $offset = ($page - 1) * $per_page;

    // Base query
    $sql_count = "SELECT COUNT(*) as total FROM voting_nominees";
    $sql = "SELECT n.*, s.name as subcategory_name, s.name_ar as subcategory_name_ar,
            c.name as category_name, c.name_ar as category_name_ar, c.id as category_id
            FROM voting_nominees n
            LEFT JOIN voting_subcategories s ON n.subcategory_id = s.id
            LEFT JOIN voting_categories c ON s.category_id = c.id";

    // Add category filter if specified
    if ($category_id > 0) {
        // First, we need to modify the count query to join with subcategories table
        $sql_count = "SELECT COUNT(*) as total FROM voting_nominees n
                      LEFT JOIN voting_subcategories s ON n.subcategory_id = s.id
                      WHERE s.category_id = ?";

        // The main query already has the joins, so just add the WHERE clause
        $sql .= " WHERE s.category_id = ?";
    }

    // Add order and limit
    $sql .= " ORDER BY n.votes_count DESC, n.name ASC LIMIT ?, ?";

    // Get total count
    if ($category_id > 0) {
        $count_stmt = $conn->prepare($sql_count);
        $count_stmt->bind_param("i", $category_id);
    } else {
        $count_stmt = $conn->prepare($sql_count);
    }

    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $total = $count_result->fetch_assoc()['total'];

    // Get nominees
    if ($category_id > 0) {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("iii", $category_id, $offset, $per_page);
    } else {
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ii", $offset, $per_page);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $nominees = [];

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $nominees[] = $row;
        }
    }

    return [
        'nominees' => $nominees,
        'total' => $total
    ];
}

// Get nominees with pagination
$result = get_nominees_paginated($selected_category, $current_page, $per_page);
$nominees = $result['nominees'];
$total_nominees = $result['total'];

// Calculate total pages
$total_pages = ceil($total_nominees / $per_page);

// Ensure current page is valid
if ($current_page < 1) {
    $current_page = 1;
} elseif ($current_page > $total_pages && $total_pages > 0) {
    $current_page = $total_pages;
}

// Get user votes if logged in
$user_votes = [];
if (is_logged_in()) {
    $user_id = $_SESSION['user_id'];

    $sql = "SELECT nominee_id FROM voting_votes WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $user_votes[] = $row['nominee_id'];
        }
    }
}

// Page title
$page_title = get_content('nominees', 'title', 'Our Nominees');
?>

<!-- Nominees Page -->
<section class="nominees-section">
    <div class="container">
        <div class="nominees-header">
            <h1 class="nominees-title"><?php echo $page_title; ?></h1>
            <p class="nominees-description"><?php echo get_content('nominees', 'description', 'Discover all the nominees for the Social Media Festival awards. Browse by category and vote for your favorites.'); ?></p>
        </div>

        <!-- Categories Filter -->
        <div class="nominees-filter">
            <div class="filter-tabs">
                <a href="<?php echo base_url('?page=nominees'); ?>" class="filter-tab <?php echo $selected_category === 0 ? 'active' : ''; ?>">
                    <?php echo get_content('nominees', 'all_categories', 'All Categories'); ?>
                </a>

                <?php foreach ($categories as $category): ?>
                <a href="<?php echo base_url('?page=nominees&category=' . $category['id']); ?>" class="filter-tab <?php echo $selected_category === (int)$category['id'] ? 'active' : ''; ?>">
                    <?php echo get_main_category_name($category); ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Nominees Grid -->
        <?php if (!empty($nominees)): ?>
        <div class="nominees-grid">
            <?php foreach ($nominees as $nominee): ?>
            <div class="nominee-card">
                <div class="nominee-image">
                    <img src="<?php echo asset_url('uploads/' . (isset($nominee['image']) ? $nominee['image'] : 'default.jpg')); ?>" alt="<?php echo htmlspecialchars($nominee['name']); ?>">
                    <div class="nominee-category">
                        <?php echo isset($nominee['category_name']) ? ($current_lang === 'ar' ? $nominee['category_name_ar'] : $nominee['category_name']) : ''; ?>
                    </div>
                </div>
                <div class="nominee-info">
                    <h3 class="nominee-name"><?php echo $current_lang === 'ar' ? $nominee['name_ar'] : $nominee['name']; ?></h3>
                    <div class="nominee-description">
                        <?php
                        $description = $current_lang === 'ar' ?
                            (isset($nominee['description_ar']) ? $nominee['description_ar'] : '') :
                            (isset($nominee['description']) ? $nominee['description'] : '');
                        echo mb_substr($description, 0, 100) . (mb_strlen($description) > 100 ? '...' : '');
                        ?>
                    </div>
                    <div class="nominee-meta">
                        <div class="nominee-votes">
                            <i class="fas fa-vote-yea"></i>
                            <span><?php echo number_format($nominee['votes_count']); ?> <?php echo get_content('nominees', 'votes', 'Votes'); ?></span>
                        </div>
                        <div class="nominee-actions">
                            <a href="<?php echo base_url('?page=nominee&id=' . $nominee['id']); ?>" class="btn-view">
                                <i class="fas fa-eye"></i> <?php echo get_content('nominees', 'view_profile', 'View Profile'); ?>
                            </a>
                            <?php if (is_logged_in()): ?>
                                <?php if (!in_array($nominee['id'], $user_votes)): ?>
                                <a href="<?php echo base_url('?page=vote&main_category=' . ($nominee['category_id'] ?? '') . '&subcategory=' . ($nominee['subcategory_id'] ?? '')); ?>" class="btn-vote">
                                    <i class="fas fa-vote-yea"></i> <?php echo get_content('nominees', 'vote_now', 'Vote Now'); ?>
                                </a>
                                <?php endif; ?>
                            <?php else: ?>
                                <a href="<?php echo base_url('?page=login&redirect=nominees'); ?>" class="btn-vote">
                                    <i class="fas fa-vote-yea"></i> <?php echo get_content('nominees', 'vote_now', 'Vote Now'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="nominees-pagination">
            <?php if ($current_page > 1): ?>
            <a href="<?php echo base_url('?page=nominees' . ($selected_category ? '&category=' . $selected_category : '') . '&p=' . ($current_page - 1)); ?>" class="page-item">
                <i class="fas fa-chevron-left"></i>
            </a>
            <?php endif; ?>

            <?php
            // Show limited page numbers with ellipsis
            $start_page = max(1, $current_page - 2);
            $end_page = min($total_pages, $current_page + 2);

            if ($start_page > 1) {
                echo '<a href="' . base_url('?page=nominees' . ($selected_category ? '&category=' . $selected_category : '') . '&p=1') . '" class="page-item">1</a>';
                if ($start_page > 2) {
                    echo '<span class="page-item">...</span>';
                }
            }

            for ($i = $start_page; $i <= $end_page; $i++) {
                echo '<a href="' . base_url('?page=nominees' . ($selected_category ? '&category=' . $selected_category : '') . '&p=' . $i) . '" class="page-item ' . ($i === $current_page ? 'active' : '') . '">' . $i . '</a>';
            }

            if ($end_page < $total_pages) {
                if ($end_page < $total_pages - 1) {
                    echo '<span class="page-item">...</span>';
                }
                echo '<a href="' . base_url('?page=nominees' . ($selected_category ? '&category=' . $selected_category : '') . '&p=' . $total_pages) . '" class="page-item">' . $total_pages . '</a>';
            }
            ?>

            <?php if ($current_page < $total_pages): ?>
            <a href="<?php echo base_url('?page=nominees' . ($selected_category ? '&category=' . $selected_category : '') . '&p=' . ($current_page + 1)); ?>" class="page-item">
                <i class="fas fa-chevron-right"></i>
            </a>
            <?php endif; ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="no-nominees">
            <i class="fas fa-search"></i>
            <p><?php echo get_content('nominees', 'no_nominees', 'No nominees found in this category'); ?></p>
        </div>
        <?php endif; ?>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animations to nominee cards
    const nomineeCards = document.querySelectorAll('.nominee-card');

    nomineeCards.forEach((card, index) => {
        // Stagger animation delay
        const delay = index * 0.1;

        // Apply animation with GSAP
        gsap.from(card, {
            y: 50,
            opacity: 0,
            duration: 0.8,
            delay: delay,
            ease: 'power2.out'
        });
    });
});
</script>
