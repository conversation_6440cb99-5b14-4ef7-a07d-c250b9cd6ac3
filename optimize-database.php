<?php
/**
 * أداة تحسين قاعدة البيانات الذكية
 * تعمل تلقائيًا على فحص الجداول، تحسينها، إضافة فهارس، وتنظيف البيانات
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>🗄️ أداة تحسين قاعدة البيانات الذكية</h2>";

// فحص حالة قاعدة البيانات
echo "<h3>📊 فحص حالة قاعدة البيانات</h3>";
checkDatabaseStatus();

// تحسين الجداول
echo "<h3>⚡ تحسين الجداول</h3>";
optimizeTables();

// إضافة فهارس للأداء
echo "<h3>🔍 إضافة فهارس ذكية</h3>";
addPerformanceIndexesSmart();

// تنظيف البيانات القديمة
echo "<h3>🧹 تنظيف البيانات</h3>";
cleanOldData();

// تحسين إعدادات MySQL
echo "<h3>⚙️ تحسين إعدادات MySQL</h3>";
optimizeMySQLSettings();

echo "<br><a href='index.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>العودة</a>";

/**
 * فحص حالة قاعدة البيانات
 */
function checkDatabaseStatus() {
    global $conn;

    $result = $conn->query("SELECT
        table_schema AS 'Database',
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
        FROM information_schema.tables
        WHERE table_schema = '" . DB_NAME . "'
        GROUP BY table_schema");

    if ($result && $row = $result->fetch_assoc()) {
        echo "<p>📏 الحجم: " . $row['Size (MB)'] . " MB</p>";
    }

    $result = $conn->query("SHOW TABLES");
    $table_count = $result ? $result->num_rows : 0;
    echo "<p>📋 عدد الجداول: $table_count</p>";
}

/**
 * تحسين جميع الجداول
 */
function optimizeTables() {
    global $conn;
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_array()) {
        $table = $row[0];
        echo "<div>🔧 $table ... ";
        if ($conn->query("OPTIMIZE TABLE `$table`")) {
            echo "✅ تم</div>";
        } else {
            echo "❌ فشل</div>";
        }
    }
}

/**
 * إضافة فهارس تلقائية ذكية
 */
function addPerformanceIndexesSmart() {
    global $conn;

    // جلب الجداول
    $tables = [];
    $result = $conn->query("SHOW TABLES");
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
    }

    // جلب الأعمدة
    $existing_columns = [];
    foreach ($tables as $table) {
        $columns_result = $conn->query("SHOW COLUMNS FROM `$table`");
        if ($columns_result) {
            while ($column = $columns_result->fetch_assoc()) {
                $existing_columns[$table][] = $column['Field'];
            }
        }
    }

    // الأعمدة الشائعة التي نريد فهرستها
    $common_fields = ['email', 'user_id', 'nominee_id', 'category_id', 'featured', 'created_at', 'status'];

    $indexes = [];
    foreach ($existing_columns as $table => $columns) {
        foreach ($common_fields as $field) {
            if (in_array($field, $columns)) {
                $index_name = "idx_{$field}";
                $indexes[] = "ALTER TABLE `$table` ADD INDEX `$index_name` (`$field`)";
            }
        }

        // فهرس مركب إذا كان موجود
        if (in_array('user_id', $columns) && in_array('nominee_id', $columns)) {
            $indexes[] = "ALTER TABLE `$table` ADD INDEX `idx_user_nominee` (`user_id`, `nominee_id`)";
        }
    }

    // تنفيذ الفهارس
    foreach ($indexes as $sql) {
        preg_match('/ADD INDEX `([^`]+)`/', $sql, $matches);
        $index_name = $matches[1] ?? 'unknown';
        echo "<div>📇 $index_name ... ";

        try {
            if ($conn->query($sql)) {
                echo "✅ تم</div>";
            } else {
                throw new Exception($conn->error);
            }
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "ℹ️ موجود</div>";
            } else {
                echo "❌ فشل: " . $e->getMessage() . "</div>";
            }
        }
    }
}

/**
 * تنظيف بيانات مكررة وغير فعالة
 */
function cleanOldData() {
    global $conn;

    // التحقق من وجود العمود "status" في جدول users
    $result = $conn->query("SHOW COLUMNS FROM `users` LIKE 'status'");
    if ($result && $result->num_rows > 0) {
        // حذف المستخدمين غير النشطين إذا كان العمود موجود
        $conn->query("DELETE FROM users WHERE status = 'inactive'");
        echo "🧹 تم حذف المستخدمين ذوي الحالة 'inactive'<br>";
    } else {
        echo "⚠️ لم يتم العثور على عمود 'status' في جدول users، تم تخطي حذف المستخدمين غير النشطين<br>";
    }

    // يمكن إضافة إجراءات تنظيف أخرى هنا
}


/**
 * عرض توصيات إعدادات MySQL
 */
function optimizeMySQLSettings() {
    global $conn;

    $settings = [
        'innodb_buffer_pool_size',
        'query_cache_size',
        'tmp_table_size',
        'max_heap_table_size',
        'innodb_log_file_size'
    ];

    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الإعداد</th><th>الحالي</th><th>التوصية</th></tr>";

    foreach ($settings as $setting) {
        $result = $conn->query("SHOW VARIABLES LIKE '$setting'");
        $value = ($result && $row = $result->fetch_assoc()) ? $row['Value'] : 'غير متاح';
        $recommendation = getRecommendation($setting, $value);
        echo "<tr><td>$setting</td><td>$value</td><td>$recommendation</td></tr>";
    }

    echo "</table>";
}

/**
 * توصيات إعدادات MySQL
 */
function getRecommendation($setting, $current_value) {
    switch ($setting) {
        case 'innodb_buffer_pool_size': return "70-80% من RAM";
        case 'query_cache_size': return "64M - 256M";
        case 'tmp_table_size': return "64M - 128M";
        case 'max_heap_table_size': return "64M - 128M";
        case 'innodb_log_file_size': return "256M - 512M";
        default: return "راجع الوثائق الرسمية";
    }
}
