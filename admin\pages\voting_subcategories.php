<?php
/**
 * Voting Subcategories Management Page
 */

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Get category ID from URL
$category_id = isset($_GET['category_id']) ? (int)$_GET['category_id'] : 0;

// Get category details
$category = $category_id > 0 ? get_main_category($category_id) : null;

// Process form submission for adding/editing subcategory
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save') {
    $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    $name = $_POST['name'] ?? '';
    $name_ar = $_POST['name_ar'] ?? '';
    $description = $_POST['description'] ?? '';
    $description_ar = $_POST['description_ar'] ?? '';
    $display_order = (int)($_POST['display_order'] ?? 0);
    $subcategory_category_id = (int)($_POST['category_id'] ?? 0);
    
    // Handle image upload
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/uploads/';
        $temp_name = $_FILES['image']['tmp_name'];
        $original_name = $_FILES['image']['name'];
        $extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $new_name = 'voting_subcategory_' . time() . '.' . $extension;
        
        if (move_uploaded_file($temp_name, $upload_dir . $new_name)) {
            $image = $new_name;
        }
    }
    
    // Check if table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_subcategories'");
    if ($check_table->num_rows == 0) {
        // Create table if it doesn't exist
        $create_table_sql = file_get_contents('../sql/voting_hierarchy.sql');
        $conn->multi_query($create_table_sql);
        
        // Clear results
        while ($conn->more_results() && $conn->next_result()) {
            if ($result = $conn->store_result()) {
                $result->free();
            }
        }
    }
    
    if ($id > 0) {
        // Update existing subcategory
        $update_sql = "UPDATE voting_subcategories SET 
                      name = ?, 
                      name_ar = ?, 
                      description = ?, 
                      description_ar = ?, 
                      category_id = ?,
                      display_order = ?";
        
        // Only update image if a new one was uploaded
        if (!empty($image)) {
            $update_sql .= ", image = ?";
        }
        
        $update_sql .= " WHERE id = ?";
        
        $stmt = $conn->prepare($update_sql);
        
        if (!empty($image)) {
            $stmt->bind_param("ssssiisi", $name, $name_ar, $description, $description_ar, $subcategory_category_id, $display_order, $image, $id);
        } else {
            $stmt->bind_param("ssssiii", $name, $name_ar, $description, $description_ar, $subcategory_category_id, $display_order, $id);
        }
        
        if ($stmt->execute()) {
            $success_message = "تم تحديث الفئة الفرعية بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء تحديث البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    } else {
        // Insert new subcategory
        $insert_sql = "INSERT INTO voting_subcategories (name, name_ar, description, description_ar, category_id, display_order, image) 
                      VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("ssssiss", $name, $name_ar, $description, $description_ar, $subcategory_category_id, $display_order, $image);
        
        if ($stmt->execute()) {
            $success_message = "تم إضافة الفئة الفرعية بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء إضافة البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    }
    
    // Update category_id in URL if it changed
    if ($id == 0 && $subcategory_category_id != $category_id) {
        $category_id = $subcategory_category_id;
        $category = get_main_category($category_id);
    }
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $id = (int)$_POST['id'];
    
    // Get the image filename before deleting
    $image_sql = "SELECT image FROM voting_subcategories WHERE id = ?";
    $stmt = $conn->prepare($image_sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($image_filename);
    $stmt->fetch();
    $stmt->close();
    
    // Delete the subcategory
    $delete_sql = "DELETE FROM voting_subcategories WHERE id = ?";
    $stmt = $conn->prepare($delete_sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        // Delete the image file if it exists
        if (!empty($image_filename)) {
            $image_path = '../assets/uploads/' . $image_filename;
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }
        
        $success_message = "تم حذف الفئة الفرعية بنجاح";
    } else {
        $error_message = "حدث خطأ أثناء حذف البيانات: " . $stmt->error;
    }
    
    $stmt->close();
}

// Get all categories for dropdown
$all_categories = get_main_categories();

// Get subcategories for the selected category
$subcategories = $category_id > 0 ? get_subcategories($category_id) : [];

// Get subcategory for editing
$edit_subcategory = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    
    // Get subcategory details
    $edit_subcategory = get_subcategory($edit_id);
    
    // Update category_id if subcategory belongs to a different category
    if ($edit_subcategory && $edit_subcategory['category_id'] != $category_id) {
        $category_id = $edit_subcategory['category_id'];
        $category = get_main_category($category_id);
        $subcategories = get_subcategories($category_id);
    }
}
?>

<div class="page-header">
    <h1><i class="fas fa-folder-open"></i> إدارة الفئات الفرعية للتصويت</h1>
    <?php if ($category): ?>
    <p>إدارة الفئات الفرعية للفئة: <strong><?php echo htmlspecialchars(get_main_category_name($category)); ?></strong></p>
    <?php else: ?>
    <p>إدارة الفئات الفرعية لنظام التصويت</p>
    <?php endif; ?>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success">
    <?php echo $success_message; ?>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger">
    <?php echo $error_message; ?>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h2><?php echo $edit_subcategory ? 'تعديل الفئة الفرعية' : 'إضافة فئة فرعية جديدة'; ?></h2>
            </div>
            <div class="card-body">
                <form action="" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="save">
                    <?php if ($edit_subcategory): ?>
                    <input type="hidden" name="id" value="<?php echo $edit_subcategory['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="category_id">الفئة الرئيسية</label>
                        <select id="category_id" name="category_id" class="form-control" required>
                            <option value="">-- اختر الفئة الرئيسية --</option>
                            <?php foreach ($all_categories as $cat): ?>
                            <option value="<?php echo $cat['id']; ?>" <?php echo ($edit_subcategory && $edit_subcategory['category_id'] == $cat['id']) || (!$edit_subcategory && $category_id == $cat['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars(get_main_category_name($cat)); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="name">الاسم (الإنجليزية)</label>
                        <input type="text" id="name" name="name" class="form-control" value="<?php echo $edit_subcategory ? htmlspecialchars($edit_subcategory['name']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="name_ar">الاسم (العربية)</label>
                        <input type="text" id="name_ar" name="name_ar" class="form-control" value="<?php echo $edit_subcategory ? htmlspecialchars($edit_subcategory['name_ar']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="description">الوصف (الإنجليزية)</label>
                        <textarea id="description" name="description" class="form-control" rows="3"><?php echo $edit_subcategory ? htmlspecialchars($edit_subcategory['description']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="description_ar">الوصف (العربية)</label>
                        <textarea id="description_ar" name="description_ar" class="form-control" rows="3"><?php echo $edit_subcategory ? htmlspecialchars($edit_subcategory['description_ar']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="display_order">ترتيب العرض</label>
                        <input type="number" id="display_order" name="display_order" class="form-control" value="<?php echo $edit_subcategory ? (int)$edit_subcategory['display_order'] : count($subcategories) + 1; ?>" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="image">الصورة</label>
                        <?php if ($edit_subcategory && !empty($edit_subcategory['image'])): ?>
                        <div class="current-image">
                            <img src="<?php echo asset_url('uploads/' . $edit_subcategory['image']); ?>" alt="<?php echo htmlspecialchars($edit_subcategory['name']); ?>" style="max-width: 100px; margin-bottom: 10px;">
                            <p>الصورة الحالية</p>
                        </div>
                        <?php endif; ?>
                        <input type="file" id="image" name="image" class="form-control-file" <?php echo !$edit_subcategory ? 'required' : ''; ?>>
                        <small class="form-text text-muted">يفضل صورة دائرية بأبعاد 200×200 بكسل</small>
                    </div>
                    
                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                        <?php if ($edit_subcategory): ?>
                        <a href="index.php?page=voting_subcategories<?php echo $category_id ? '&category_id=' . $category_id : ''; ?>" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h2>الفئات الفرعية</h2>
                <?php if (!$category_id && count($all_categories) > 0): ?>
                <div class="category-filter">
                    <form action="" method="get" class="form-inline">
                        <input type="hidden" name="page" value="voting_subcategories">
                        <div class="form-group mr-2">
                            <select name="category_id" class="form-control" onchange="this.form.submit()">
                                <option value="">-- جميع الفئات --</option>
                                <?php foreach ($all_categories as $cat): ?>
                                <option value="<?php echo $cat['id']; ?>" <?php echo $category_id == $cat['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars(get_main_category_name($cat)); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (empty($all_categories)): ?>
                <div class="alert alert-warning">
                    لا توجد فئات رئيسية. يرجى <a href="index.php?page=voting_categories">إضافة فئات رئيسية</a> أولاً.
                </div>
                <?php elseif ($category_id > 0 && empty($subcategories)): ?>
                <div class="alert alert-info">
                    لا توجد فئات فرعية لهذه الفئة. قم بإضافة فئات فرعية جديدة.
                </div>
                <?php elseif (empty($subcategories) && !$category_id): ?>
                <div class="alert alert-info">
                    لا توجد فئات فرعية. قم بإضافة فئات فرعية جديدة.
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <?php if (!$category_id): ?>
                                <th>الفئة الرئيسية</th>
                                <?php endif; ?>
                                <th>الترتيب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subcategories as $subcategory): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($subcategory['image'])): ?>
                                    <img src="<?php echo asset_url('uploads/' . $subcategory['image']); ?>" alt="<?php echo htmlspecialchars($subcategory['name']); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%;">
                                    <?php else: ?>
                                    <div class="no-image">لا توجد صورة</div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($current_lang === 'ar' ? $subcategory['name_ar'] : $subcategory['name']); ?></td>
                                <?php if (!$category_id): ?>
                                <td>
                                    <?php 
                                    $parent_category = get_main_category($subcategory['category_id']);
                                    echo $parent_category ? htmlspecialchars(get_main_category_name($parent_category)) : 'غير محدد';
                                    ?>
                                </td>
                                <?php endif; ?>
                                <td><?php echo (int)$subcategory['display_order']; ?></td>
                                <td>
                                    <a href="index.php?page=voting_subcategories&category_id=<?php echo $subcategory['category_id']; ?>&edit=<?php echo $subcategory['id']; ?>" class="btn btn-sm btn-primary"><i class="fas fa-edit"></i> تعديل</a>
                                    <a href="index.php?page=voting_nominees&subcategory_id=<?php echo $subcategory['id']; ?>" class="btn btn-sm btn-info"><i class="fas fa-user-friends"></i> المرشحين</a>
                                    <form action="" method="post" class="d-inline delete-form" onsubmit="return confirm('هل أنت متأكد من حذف هذه الفئة الفرعية؟ سيتم حذف جميع المرشحين المرتبطين بها.');">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="<?php echo $subcategory['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> حذف</button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
