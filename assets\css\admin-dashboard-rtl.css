/* ===== RTL Support for Enhanced Dashboard ===== */

/* Dashboard Header */
.dashboard-header {
    flex-direction: row-reverse;
}

.dashboard-actions {
    flex-direction: row-reverse;
}

.date-display i {
    margin-right: 0;
    margin-left: 8px;
}

/* Welcome Banner */
.welcome-banner {
    flex-direction: row-reverse;
}

.welcome-banner::before {
    background: radial-gradient(circle at top left, rgba(212, 175, 55, 0.1), transparent 70%);
}

.welcome-image {
    margin-left: 0;
    margin-right: var(--spacing-lg);
}

/* Section Titles */
.section-title {
    flex-direction: row-reverse;
}

.section-title i {
    margin-right: 0;
    margin-left: 10px;
}

/* Stats Cards */
.stat-icon {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

/* Quick Actions */
.action-buttons {
    direction: rtl;
}

/* Charts Section */
.chart-header {
    flex-direction: row-reverse;
}

.chart-actions {
    flex-direction: row-reverse;
}

/* Tables Section */
.table-header {
    flex-direction: row-reverse;
}

.table-actions {
    flex-direction: row-reverse;
}

.data-table th,
.data-table td {
    text-align: right;
}

.user-info, .nominee-info {
    flex-direction: row-reverse;
}

.user-avatar, .nominee-avatar {
    margin-right: 0;
    margin-left: 10px;
}

.progress-container {
    flex-direction: row-reverse;
}

.progress-container .progress-bar {
    margin-right: 0;
    margin-left: 10px;
}

.progress-text {
    text-align: left;
}

/* System Status */
.status-cards {
    direction: rtl;
}

.status-card {
    flex-direction: row-reverse;
}

.status-icon {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

.status-progress .progress-text {
    right: auto;
    left: 0;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .welcome-banner {
        flex-direction: column;
    }
    
    .welcome-image {
        margin-right: 0;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        align-items: flex-end;
    }
    
    .dashboard-actions {
        justify-content: flex-start;
    }
}
