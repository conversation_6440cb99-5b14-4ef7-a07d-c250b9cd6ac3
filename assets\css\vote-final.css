/* Final Vote Page Styles */
.vote-section {
    padding: 0;
    background: #111111;
    min-height: 100vh;
}

.container {
    max-width: 100%;
    padding: 0;
}

.alert {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    padding: 10px 20px;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: auto;
    max-width: 90%;
    text-align: center;
    animation: fadeOut 0.5s ease 3s forwards;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; visibility: hidden; }
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.9);
    border: none;
    color: white;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.9);
    border: none;
    color: white;
}

/* Main Categories Section */
.main-categories-section {
    position: sticky;
    top: 60px;
    z-index: 100;
    background: #000000;
    padding: 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.main-categories-slider {
    overflow: hidden;
    position: relative;
}

.categories-scroll {
    display: flex;
    overflow-x: auto;
    padding: 5px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}

.categories-scroll::-webkit-scrollbar {
    display: none;
}

.main-category-item {
    flex: 0 0 auto;
    width: 90px;
    margin-right: 5px;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 5px;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    border: 1px solid #333;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.main-category-item:hover {
    transform: translateY(-3px);
    border-color: #ffd700;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.main-category-item.active {
    background: rgba(255, 215, 0, 0.1);
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.category-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-icon i {
    font-size: 1.5rem;
    color: #ffd700;
}

/* Voting Content */
.voting-content {
    padding: 5px;
}

/* Subcategories Section */
.subcategories-section {
    margin-bottom: 10px;
    background: #1a1a1a;
    padding: 5px;
    border-radius: 8px;
    border: 1px solid #333;
}

.subcategories-slider {
    overflow: hidden;
    position: relative;
}

.subcategories-scroll {
    display: flex;
    overflow-x: auto;
    padding: 5px 0;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}

.subcategories-scroll::-webkit-scrollbar {
    display: none;
}

.subcategory-item {
    flex: 0 0 auto;
    width: 90px;
    margin-right: 5px;
    background: #222;
    border-radius: 8px;
    padding: 5px;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    border: 1px solid #333;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.subcategory-item:hover {
    transform: translateY(-3px);
    border-color: #ffd700;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.subcategory-item.active {
    background: rgba(255, 215, 0, 0.1);
    border-color: #ffd700;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.subcategory-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.subcategory-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.subcategory-icon i {
    font-size: 1.2rem;
    color: #ffd700;
}

/* Nominees Section */
.nominees-section {
    margin-bottom: 10px;
    background: #1a1a1a;
    padding: 5px;
    border-radius: 8px;
    border: 1px solid #333;
}

.nominees-container {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 5px;
    scrollbar-width: thin;
    scrollbar-color: #ffd700 #333;
}

.nominees-container::-webkit-scrollbar {
    width: 5px;
}

.nominees-container::-webkit-scrollbar-track {
    background: #333;
    border-radius: 5px;
}

.nominees-container::-webkit-scrollbar-thumb {
    background-color: #ffd700;
    border-radius: 5px;
}

.nominees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 5px;
}

.nominee-card {
    background: #222;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #333;
    height: 100%;
    position: relative;
}

.nominee-card:hover {
    transform: translateY(-3px);
    border-color: #ffd700;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.nominee-link {
    display: block;
    text-decoration: none;
    height: 100%;
    color: white;
}

.nominee-image {
    height: 180px;
    overflow: hidden;
    position: relative;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.nominee-card:hover .nominee-image img {
    transform: scale(1.05);
}

.nominee-rank {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255, 215, 0, 0.8);
    color: black;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.nominee-info {
    padding: 8px;
    text-align: center;
}

.nominee-info h3 {
    color: #ffd700;
    font-size: 0.9rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nominee-votes {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.nominee-votes i {
    color: #ffd700;
    font-size: 0.9rem;
    margin-right: 3px;
}

.votes-count {
    color: white;
    font-size: 0.9rem;
    font-weight: bold;
    margin-right: 3px;
}

.votes-label {
    color: #aaa;
    font-size: 0.7rem;
}

.vote-status {
    margin: 3px 0;
}

.voted-badge {
    display: inline-block;
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
    padding: 2px 5px;
    border-radius: 10px;
    font-size: 0.7rem;
}

.voted-badge i {
    margin-right: 2px;
}

.quick-vote-form {
    margin-top: 5px;
}

.btn-vote-small {
    background-color: #ffd700;
    color: black;
    border: none;
    padding: 3px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
}

.btn-vote-small i {
    margin-right: 3px;
}

.btn-vote-small:hover {
    background-color: #ffdf4d;
    transform: translateY(-2px);
}

.btn-vote-small.voted {
    background-color: #28a745;
    cursor: default;
}

.btn-vote-small.voted:hover {
    transform: none;
}

.btn-login-small {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 3px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    text-decoration: none;
}

.btn-login-small i {
    margin-right: 3px;
}

.btn-login-small:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}
