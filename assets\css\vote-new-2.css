/* Nominees Section */
.nominees-section {
    margin-bottom: var(--spacing-xl);
    background: linear-gradient(145deg, rgba(20, 20, 20, 0.7), rgba(10, 10, 10, 0.5));
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.nominees-section h2 {
    color: var(--color-white);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    text-align: center;
    position: relative;
    display: inline-block;
}

.nominees-container {
    max-height: 800px;
    overflow-y: auto;
    padding-right: var(--spacing-sm);
    scrollbar-width: thin;
    scrollbar-color: var(--color-gold) rgba(0, 0, 0, 0.3);
}

.nominees-container::-webkit-scrollbar {
    width: 8px;
}

.nominees-container::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.nominees-container::-webkit-scrollbar-thumb {
    background-color: var(--color-gold);
    border-radius: 10px;
}

.nominees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.nominee-card {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 100%;
    position: relative;
}

.nominee-card:hover {
    transform: translateY(-5px);
    border-color: var(--color-gold);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.nominee-link {
    display: block;
    text-decoration: none;
    height: 100%;
    color: var(--color-white);
}

.nominee-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.nominee-card:hover .nominee-image img {
    transform: scale(1.1);
}

.nominee-rank {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 215, 0, 0.8);
    color: var(--color-black);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.nominee-info {
    padding: var(--spacing-md);
    text-align: center;
}

.nominee-info h3 {
    color: var(--color-gold);
    font-size: 1.2rem;
    margin-bottom: var(--spacing-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nominee-votes {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
}

.nominee-votes i {
    color: var(--color-gold);
    font-size: 1.1rem;
    margin-right: 5px;
}

.votes-count {
    color: var(--color-white);
    font-size: 1.1rem;
    font-weight: bold;
    margin-right: 5px;
}

.votes-label {
    color: var(--color-light);
    font-size: 0.8rem;
}

.vote-status {
    margin-top: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
}

.voted-badge {
    display: inline-block;
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
    padding: 3px 8px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.voted-badge i {
    margin-right: 3px;
}

.quick-vote-form {
    margin-top: var(--spacing-sm);
}

.btn-vote-small {
    background-color: var(--color-gold);
    color: var(--color-black);
    border: none;
    padding: 5px 10px;
    font-size: 0.9rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
}

.btn-vote-small i {
    margin-right: 5px;
}

.btn-vote-small:hover {
    background-color: var(--color-gold-light);
    transform: translateY(-2px);
}

.btn-vote-small.voted {
    background-color: #28a745;
    cursor: default;
}

.btn-vote-small.voted:hover {
    transform: none;
}

.btn-login-small {
    background-color: rgba(255, 255, 255, 0.2);
    color: var(--color-white);
    border: none;
    padding: 5px 10px;
    font-size: 0.9rem;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    text-decoration: none;
}

.btn-login-small i {
    margin-right: 5px;
}

.btn-login-small:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .nominee-image {
        height: 220px;
    }
}

@media (max-width: 768px) {
    .main-category-item {
        width: 100px;
    }
    
    .category-icon {
        width: 60px;
        height: 60px;
    }
    
    .subcategory-item {
        width: 90px;
    }
    
    .subcategory-icon {
        width: 45px;
        height: 45px;
    }
    
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
    
    .nominee-image {
        height: 180px;
    }
    
    .nominee-info h3 {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .main-category-item {
        width: 90px;
    }
    
    .category-icon {
        width: 50px;
        height: 50px;
    }
    
    .main-category-item h3 {
        font-size: 0.8rem;
    }
    
    .subcategory-item {
        width: 80px;
    }
    
    .subcategory-icon {
        width: 40px;
        height: 40px;
    }
    
    .subcategory-item h3 {
        font-size: 0.75rem;
    }
    
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: var(--spacing-sm);
    }
    
    .nominee-image {
        height: 160px;
    }
    
    .nominee-info {
        padding: var(--spacing-sm);
    }
    
    .nominee-info h3 {
        font-size: 0.9rem;
    }
    
    .votes-count {
        font-size: 1rem;
    }
    
    .votes-label {
        font-size: 0.7rem;
    }
    
    .btn-vote-small, .btn-login-small {
        font-size: 0.8rem;
        padding: 4px 8px;
    }
}
