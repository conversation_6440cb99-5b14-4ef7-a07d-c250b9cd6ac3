/**
 * Main CSS for Social Media Festival
 */

/* ===== Base Styles ===== */
:root {
    /* Colors */
    --color-black: #000000;
    --color-dark: #121212;
    --color-gold: #d4af37;
    --color-gold-light: #f1e5ac;
    --color-gold-dark: #aa8c2c;
    --color-white: #ffffff;
    --color-gray: #888888;
    --color-light-gray: #f5f5f5;

    /* Typography */
    --font-primary: 'Poppins', sans-serif;
    --font-arabic: 'Cairo', sans-serif;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;

    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    color: var(--color-white);
    background-color: var(--color-black);
    line-height: 1.6;
    overflow-x: hidden;
}

[lang="ar"] body {
    font-family: var(--font-arabic);
}

a {
    color: var(--color-gold);
    text-decoration: none;
    transition: color var(--transition-normal);
}

a:hover {
    color: var(--color-gold-light);
}

img {
    max-width: 100%;
    height: auto;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: 3rem;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 2rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: var(--spacing-md);
}

/* ===== Buttons ===== */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--color-gold);
    color: var(--color-black);
}

.btn-primary:hover {
    background-color: var(--color-gold-light);
    color: var(--color-black);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background-color: transparent;
    color: var(--color-gold);
    border: 2px solid var(--color-gold);
}

.btn-secondary:hover {
    background-color: var(--color-gold);
    color: var(--color-black);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-outline {
    background-color: transparent;
    color: var(--color-white);
    border: 2px solid var(--color-white);
}

.btn-outline:hover {
    background-color: var(--color-white);
    color: var(--color-black);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-3d-effect {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--color-gold), var(--color-gold-light));
    opacity: 0;
    z-index: -1;
    transition: opacity var(--transition-normal);
}

.btn:hover .btn-3d-effect {
    opacity: 0.3;
}

/* ===== Header ===== */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    padding: var(--spacing-md) 0;
    transition: background-color var(--transition-normal);
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.header-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo img {
    height: 50px;
}

.main-nav {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    list-style: none;
}

.nav-menu li {
    margin: 0 var(--spacing-md);
}

.nav-menu a {
    color: var(--color-white);
    font-weight: 500;
    position: relative;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--color-gold);
    transition: width var(--transition-normal);
}

.nav-menu a:hover::after,
.nav-menu li.active a::after {
    width: 100%;
}

.btn-vote {
    background-color: var(--color-gold);
    color: var(--color-black);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
}

.btn-vote:hover {
    background-color: var(--color-gold-light);
    color: var(--color-black);
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    width: 30px;
    height: 25px;
    position: relative;
    cursor: pointer;
    z-index: 100;
    margin-right: 10px;
    padding: 0;
}

.menu-toggle-bar {
    display: block;
    width: 100%;
    height: 3px;
    background-color: var(--color-white);
    position: absolute;
    left: 0;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.menu-toggle-bar:nth-child(1) {
    top: 0;
}

.menu-toggle-bar:nth-child(2) {
    top: 50%;
    transform: translateY(-50%);
}

.menu-toggle-bar:nth-child(3) {
    bottom: 0;
}

body.menu-open .menu-toggle-bar:nth-child(1) {
    transform: translateY(11px) rotate(45deg);
    background-color: var(--color-gold);
}

body.menu-open .menu-toggle-bar:nth-child(2) {
    opacity: 0;
}

body.menu-open .menu-toggle-bar:nth-child(3) {
    transform: translateY(-11px) rotate(-45deg);
    background-color: var(--color-gold);
}

html[dir="rtl"] .menu-toggle {
    margin-right: 0;
    margin-left: 10px;
}

html[dir="rtl"] .menu-toggle-bar {
    left: auto;
    right: 0;
}

.header-actions {
    display: flex;
    align-items: center;
}

.language-switcher {
    margin-right: var(--spacing-md);
}

.lang-item {
    display: flex;
    align-items: center;
    margin-left: var(--spacing-sm);
    color: var(--color-white);
}

.lang-item img {
    width: 16px;
    height: 12px;
    margin-right: var(--spacing-xs);
}

.lang-item.active {
    color: var(--color-gold);
}

.auth-buttons {
    display: flex;
}

.btn-login,
.btn-register {
    margin-left: var(--spacing-sm);
}

.user-menu {
    position: relative;
}

.user-toggle {
    display: flex;
    align-items: center;
    color: var(--color-white);
}

.user-toggle i {
    margin-right: var(--spacing-xs);
    font-size: 1.5rem;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--color-dark);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    display: none;
    z-index: 100;
}

.user-menu:hover .dropdown-menu {
    display: block;
}

.dropdown-menu li {
    list-style: none;
    margin-bottom: var(--spacing-sm);
}

.dropdown-menu a {
    color: var(--color-white);
    display: block;
    padding: var(--spacing-xs) 0;
}

.dropdown-menu a:hover {
    color: var(--color-gold);
}

/* ===== Hero Section ===== */
.hero-section {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
}

.video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
}

.hero-content {
    max-width: 800px;
    padding: var(--spacing-lg);
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    margin-bottom: var(--spacing-md);
    color: var(--color-gold);
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    color: var(--color-white);
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out 0.2s;
    animation-fill-mode: both;
}

.hero-text {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-lg);
    color: var(--color-white);
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 1s ease-out 0.4s;
    animation-fill-mode: both;
}

.btn-vote-hero {
    font-size: 1.2rem;
    padding: 1rem 2rem;
    animation: fadeInUp 1s ease-out 0.6s;
    animation-fill-mode: both;
}

/* ===== Section Styles ===== */
section {
    padding: var(--spacing-xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    color: var(--color-gold);
    margin-bottom: var(--spacing-sm);
}

.section-header p {
    color: var(--color-white);
    font-size: 1.2rem;
}

.section-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* ===== Reels Section ===== */
.reels-section {
    background-color: var(--color-dark);
    padding: var(--spacing-xl) 0 var(--spacing-xl);
    overflow: hidden;
    position: relative;
    width: 100%;
    margin-bottom: var(--spacing-xl);
}

.reels-section .section-header {
    padding: 0 var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.reels-slider-container {
    width: 100%;
    overflow: hidden;
    position: relative;
}

.reels-slider {
    display: flex;
    width: max-content;
    animation: slideReels 120s linear infinite;
    padding: var(--spacing-md) 0;
    height: 420px; /* Ensure enough height for vertical reels */
}

@keyframes slideReels {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-100% + 100vw));
    }
}

.reels-slider:hover {
    animation-play-state: paused;
}

.reel-slide {
    flex: 0 0 auto;
    margin-right: var(--spacing-md);
    cursor: pointer;
    position: relative;
    height: 400px; /* Taller for vertical reels */
    direction: ltr; /* Reset direction for content */
}

.reel-thumbnail {
    width: 225px; /* Width for vertical reels (9:16 aspect ratio) */
    height: 100%;
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.reel-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.reel-slide:hover .reel-thumbnail img {
    transform: scale(1.05);
}

.reel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.1) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: var(--spacing-md);
    opacity: 0;
    transition: opacity var(--transition-normal);
    border-radius: var(--border-radius-md);
}

.reel-slide:hover .reel-overlay {
    opacity: 1;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    z-index: 10;
    border: 2px solid var(--color-gold);
}

.play-button i {
    color: var(--color-gold);
    font-size: 1.8rem;
    transition: all var(--transition-normal);
}

.reel-slide:hover .play-button {
    transform: translate(-50%, -50%) scale(1.1);
    background-color: var(--color-gold);
}

.reel-slide:hover .play-button i {
    color: var(--color-black);
}

.reel-title {
    align-self: flex-end;
    width: 100%;
    text-align: center;
}

.reel-title h3 {
    color: var(--color-white);
    font-size: 1.2rem;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.reel-video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.reel-video-modal.active {
    opacity: 1;
    visibility: visible;
}

.reel-video-container {
    height: 90vh;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.reel-video-container video {
    height: 100%;
    max-width: 100%;
    border-radius: var(--border-radius-md);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    object-fit: contain;
    /* Ensure vertical video display */
    max-height: 90vh;
    width: auto;
}

.close-modal {
    position: absolute;
    top: -50px;
    right: 0;
    color: var(--color-white);
    font-size: 2.5rem;
    cursor: pointer;
    transition: color var(--transition-normal);
}

.close-modal:hover {
    color: var(--color-gold);
}

/* ===== Featured Celebrities Section ===== */
.celebrities-section {
    background: linear-gradient(to bottom, #000000, #111111);
    padding: var(--spacing-xl) 0;
    position: relative;
    z-index: 1;
    min-height: 500px;
    overflow: hidden;
}

.celebrities-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../img/pattern.svg');
    background-size: 200px;
    opacity: 0.05;
    pointer-events: none;
}

.celebrities-section .section-header {
    position: relative;
    z-index: 2;
    margin-bottom: var(--spacing-xl);
}

.celebrities-section .section-header h2 {
    position: relative;
    display: inline-block;
}

.celebrities-section .section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, var(--color-gold), #ffd700, var(--color-gold));
    border-radius: 3px;
}

/* Podcasts Section Styles */
.podcasts-section {
    padding: var(--spacing-section) 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
}

.podcasts-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(212,175,55,0.1)"/></svg>') repeat;
    background-size: 50px 50px;
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
}

.podcasts-swiper {
    margin-top: 50px;
    padding-bottom: 20px;
    overflow: visible;
}

.podcast-item {
    width: 280px; /* نفس عرض الريلز */
    height: 500px; /* نفس ارتفاع الريلز */
    margin: 0 auto;
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
    position: relative;
    border: 1px solid rgba(255, 215, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.podcast-item:hover {
    transform: translateY(-15px) scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.3);
}

/* فيديو البودكاست بنفس تصميم الريلز */
.podcast-video {
    height: 400px; /* نفس ارتفاع الريلز */
    overflow: hidden;
    position: relative;
    flex: 0 0 auto;
    background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
}

.podcast-video::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 70%, rgba(0, 0, 0, 0.8));
    z-index: 1;
}

.podcast-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
    filter: saturate(0.9);
    cursor: pointer;
    display: block;
    background: #000;
}

.podcast-item:hover .podcast-video video {
    transform: scale(1.05);
    filter: saturate(1.1) brightness(1.1);
}

.default-video {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #2a2a2a, #1a1a1a);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-gold);
    font-size: 4rem;
}

.podcast-video .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70px;
    height: 70px;
    background: rgba(255, 215, 0, 0.95);
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    font-size: 1.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 100;
    opacity: 1;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
}

.podcast-item:hover .play-button {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

.podcast-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--color-gold);
    padding: 8px 15px;
    border-radius: 25px;
    font-size: 0.85rem;
    font-weight: 700;
    z-index: 2;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(212, 175, 55, 0.3);
}

/* معلومات البودكاست مثل الريلز */
.podcast-info {
    padding: 15px;
    text-align: center;
    background: linear-gradient(to top, #0a0a0a, rgba(10, 10, 10, 0.9));
    position: relative;
    z-index: 2;
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100px; /* ارتفاع ثابت للمعلومات */
}

.podcast-info h3 {
    color: var(--color-white);
    margin-bottom: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease, color 0.3s ease;
    line-height: 1.3;
    /* تحديد عدد الأسطر */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.podcast-item:hover .podcast-info h3 {
    transform: translateY(-2px);
    color: var(--color-gold);
}

/* معلومات التاريخ والنوع مثل الريلز */
.podcast-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 10px;
    border-top: 1px solid rgba(212, 175, 55, 0.2);
}

.podcast-date {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.podcast-date::before {
    content: '\f073'; /* أيقونة التقويم */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: var(--color-gold);
    font-size: 0.8rem;
}

.podcast-type {
    background: linear-gradient(135deg, var(--color-gold), #e6ac00);
    color: var(--color-black);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Simple Celebrities Styles */
.celebrities-swiper {
    margin-top: 50px;
    padding-bottom: 20px;
    overflow: visible;
}

.swiper-slide {
    height: auto;
    margin-left: 60px !important;
}

.categories-swiper .swiper-slide {
    margin-left: 60px !important;
}

.celebrity-item {
    width: 220px;
    height: 350px;
    margin: 0 auto;
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
    position: relative;
    border: 1px solid rgba(255, 215, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.celebrity-item:hover {
    transform: translateY(-15px) scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.3);
}

.celebrity-image {
    height: 220px;
    overflow: hidden;
    position: relative;
    flex: 0 0 auto;
}

.celebrity-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 70%, rgba(0, 0, 0, 0.8));
    z-index: 1;
}

.celebrity-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
    filter: saturate(0.9);
}

.celebrity-item:hover .celebrity-image img {
    transform: scale(1.15);
    filter: saturate(1.1) brightness(1.1);
}

.celebrity-info {
    padding: var(--spacing-md);
    text-align: center;
    background: linear-gradient(to top, #0a0a0a, rgba(10, 10, 10, 0.8));
    position: relative;
    z-index: 2;
    flex: 1 1 auto;
}

.celebrity-info h3 {
    color: var(--color-gold);
    margin-bottom: var(--spacing-xs);
    font-size: 1.2rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease, color 0.3s ease;
}

.celebrity-item:hover .celebrity-info h3 {
    transform: translateY(-3px);
    color: #ffd700;
}

.celebrity-info p {
    color: var(--color-white);
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.8;
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.celebrity-item:hover .celebrity-info p {
    opacity: 1;
    transform: translateY(-2px);
}

.celebrity-social {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 10px;
    padding: 10px 0;
    background: rgba(0, 0, 0, 0.8);
    transition: transform 0.3s ease;
    z-index: 3;
    transform: translateY(100%);
}

.celebrity-item:hover .celebrity-social {
    transform: translateY(0);
}

.social-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--color-gold);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    font-size: 0.8rem;
    transition: transform 0.3s ease, background-color 0.3s ease;
}

.social-icon:hover {
    transform: scale(1.2);
    background-color: #ffd700;
}

/* ===== Nominees Section ===== */
.nominees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

/* Swiper Categories Styles */
.categories-swiper {
    width: 100%;
    padding: var(--spacing-lg) 0;
    overflow: visible;
}

.category-item {
    width: 220px;
    margin: 0 auto;
    transition: transform var(--transition-normal);
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    height: 100%;
    position: relative;
}

.category-item.voting-category {
    background: linear-gradient(145deg, rgba(40, 40, 20, 0.7), rgba(20, 20, 10, 0.9));
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.category-item:hover {
    transform: translateY(-10px) scale(1.05);
}

.category-link {
    display: block;
    text-decoration: none;
    color: var(--color-white);
    text-align: center;
}

.category-image {
    width: 180px;
    height: 180px;
    margin: 0 auto var(--spacing-md);
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--color-gold);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.category-item:hover .category-image {
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
    border-width: 4px;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-info {
    padding: var(--spacing-sm);
}

.category-info h3 {
    margin-bottom: var(--spacing-sm);
    font-size: 1.2rem;
    color: var(--color-white);
    transition: color var(--transition-normal);
}

.category-item:hover .category-info h3 {
    color: var(--color-gold);
}

.vote-button {
    display: inline-block;
    background-color: var(--color-gold);
    color: var(--color-black);
    padding: 5px 15px;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-weight: bold;
    margin-top: var(--spacing-xs);
    transition: transform var(--transition-normal), background-color var(--transition-normal);
}

.category-item:hover .vote-button {
    transform: scale(1.1);
    background-color: #ffd700;
}

.nominee-card {
    background-color: var(--color-dark);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.nominee-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.nominee-image {
    height: 250px;
    overflow: hidden;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.nominee-card:hover .nominee-image img {
    transform: scale(1.1);
}

.nominee-info {
    padding: var(--spacing-md);
    text-align: center;
}

.nominee-info h3 {
    margin-bottom: var(--spacing-xs);
    font-size: 1.5rem;
}

.nominee-category {
    color: var(--color-gold);
    margin-bottom: var(--spacing-sm);
    font-size: 0.9rem;
    text-transform: uppercase;
}

.nominee-votes {
    margin-bottom: var(--spacing-md);
}

.votes-count {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-gold);
}

.votes-label {
    font-size: 0.9rem;
    color: var(--color-gray);
    margin-left: var(--spacing-xs);
}

/* ===== Stories Section ===== */
.stories-section {
    background-color: var(--color-dark);
}

/* Swiper Stories Styles */
.stories-swiper {
    width: 100%;
    padding: var(--spacing-lg) 0;
    overflow: visible;
}

.stories-swiper .swiper-slide {
    margin-left: 30px !important;
}

.story-item {
    width: 300px;
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    margin: 0 auto;
}

.story-video {
    position: relative;
    height: 533px; /* 9:16 aspect ratio for 300px width */
    overflow: hidden;
    background-color: #000;
}

.story-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    z-index: 2;
    position: relative;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(255, 215, 0, 0.9);
    border-radius: 50%;
    display: flex !important;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 100;
    color: var(--color-black);
    font-size: 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    pointer-events: auto;
}

.play-button i {
    color: var(--color-black);
    font-size: 1.5rem;
}

.play-button:hover {
    transform: translate(-50%, -50%) scale(1.1);
    background-color: rgba(255, 215, 0, 1);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.play-button:hover {
    background-color: var(--color-gold);
}

.play-button:hover i {
    color: var(--color-black);
}

.story-info {
    padding: var(--spacing-md);
}

.story-info h3 {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-xs);
}

.story-info p {
    font-size: 0.9rem;
    color: var(--color-gray);
}

/* ===== Sponsors Section ===== */
.sponsors-category {
    margin-bottom: var(--spacing-lg);
}

.category-title {
    text-align: center;
    margin-bottom: var(--spacing-md);
    font-size: 1.5rem;
    color: var(--color-gold);
}

.sponsors-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-lg);
    position: relative;
}

/* ===== Sponsors Pyramid Layout ===== */
.sponsors-pyramid {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    max-width: 1000px;
    margin: 0 auto;
}

.pyramid-level {
    width: 100%;
    text-align: center;
}

.level-title {
    color: var(--color-gold);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 40px;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
}

.level-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, var(--color-gold), #f1e5ac);
    border-radius: 2px;
}

/* Swiper Container for Sponsors */
.level-sponsors {
    position: relative;
    width: 100%;
    padding: 20px 0;
}

.platinum-sponsors-swiper,
.gold-sponsors-swiper,
.silver-sponsors-swiper {
    overflow: visible;
    padding: 20px 0;
}

.platinum-sponsors-swiper .swiper-slide,
.gold-sponsors-swiper .swiper-slide,
.silver-sponsors-swiper .swiper-slide {
    width: auto;
    height: auto;
}

/* Marquee Animation */
@keyframes marquee-scroll {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Different speeds for each level */
.platinum-level .sponsors-marquee {
    animation-duration: 15s;
    gap: 30px;
}

.gold-level .sponsors-marquee {
    animation-duration: 20s;
    gap: 25px;
}

.silver-level .sponsors-marquee {
    animation-duration: 25s;
    animation-direction: reverse; /* Reverse direction for variety */
    gap: 20px;
}

/* Sponsor Item Sizes - All 200x200 */
.platinum-level .sponsor-item,
.gold-level .sponsor-item,
.silver-level .sponsor-item {
    width: 200px !important;
    height: 200px !important;
    flex-shrink: 0;
}

/* Enhanced Sponsor Items */
.sponsor-item {
    position: relative;
    border: none;
    background: transparent;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    width: 200px !important;
    height: 200px !important;
}

.sponsor-item:hover {
    transform: scale(1.05);
}

.sponsor-link {
    display: block;
    text-decoration: none;
    color: inherit;
    padding: 0;
    height: 100%;
    position: relative;
    width: 100%;
}

.sponsor-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 200px;
    height: 200px;
    margin: 0;
    position: relative;
    background: transparent;
}

.sponsor-logo img {
    width: 200px !important;
    height: 200px !important;
    object-fit: contain;
    background: transparent;
    border: none;
    transition: all 0.3s ease;
}

.sponsor-item:hover .sponsor-logo img {
    transform: scale(1.05);
}

.sponsor-info {
    display: none;
}

/* All sponsor levels have same size - 200x200 */
.sponsor-item.platinum,
.sponsor-item.gold,
.sponsor-item.silver {
    width: 200px !important;
    height: 200px !important;
    border: none;
    background: transparent;
}

.sponsor-item.platinum:hover,
.sponsor-item.gold:hover,
.sponsor-item.silver:hover {
    transform: scale(1.05);
    box-shadow: none;
}

.sponsor-item.platinum .sponsor-logo,
.sponsor-item.gold .sponsor-logo,
.sponsor-item.silver .sponsor-logo {
    width: 200px;
    height: 200px;
}

.sponsor-item.platinum .sponsor-logo img,
.sponsor-item.gold .sponsor-logo img,
.sponsor-item.silver .sponsor-logo img {
    width: 200px !important;
    height: 200px !important;
}



/* Mobile Responsive Styles for Sponsors */
@media (max-width: 768px) {
    .sponsors-pyramid {
        gap: 30px;
        padding: 20px 0;
    }

    .level-title {
        font-size: 1.4rem;
        margin-bottom: 25px;
    }

    .level-title::after {
        width: 60px;
        height: 3px;
    }

    .level-sponsors {
        padding: 15px 0;
    }

    /* Mobile sizes for sponsor items - keep 200x200 */
    .sponsor-item.platinum,
    .sponsor-item.gold,
    .sponsor-item.silver {
        width: 200px !important;
        height: 200px !important;
    }

    .sponsor-item.platinum .sponsor-logo,
    .sponsor-item.gold .sponsor-logo,
    .sponsor-item.silver .sponsor-logo {
        width: 200px;
        height: 200px;
    }

    .sponsor-item.platinum .sponsor-logo img,
    .sponsor-item.gold .sponsor-logo img,
    .sponsor-item.silver .sponsor-logo img {
        width: 200px !important;
        height: 200px !important;
    }

    .sponsor-link {
        padding: 0;
    }

    .sponsor-info {
        display: none;
    }
}

/* ===== Footer ===== */
.site-footer {
    background-color: var(--color-dark);
    padding: var(--spacing-xl) 0 var(--spacing-md);
}

.footer-top {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.footer-logo img {
    max-width: 150px;
    margin-bottom: var(--spacing-md);
}

.footer-nav h3,
.footer-contact h3,
.footer-newsletter h3 {
    color: var(--color-gold);
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
}

.footer-nav ul,
.footer-contact ul {
    list-style: none;
}

.footer-nav li,
.footer-contact li {
    margin-bottom: var(--spacing-xs);
}

.footer-nav a,
.footer-contact a {
    color: var(--color-white);
}

.footer-nav a:hover,
.footer-contact a:hover {
    color: var(--color-gold);
}

.footer-contact i {
    margin-right: var(--spacing-xs);
    color: var(--color-gold);
}

.social-links {
    display: flex;
    margin-top: var(--spacing-md);
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--color-black);
    border-radius: 50%;
    margin-right: var(--spacing-sm);
    transition: background-color var(--transition-normal);
}

.social-links a:hover {
    background-color: var(--color-gold);
}

.social-links a:hover i {
    color: var(--color-black);
}

/* CEO Section Styles */
.ceo-section {
    background: linear-gradient(to bottom, #111111, #000000);
    padding: var(--spacing-xl) 0;
    color: var(--color-white);
}

.ceo-container {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-xl);
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 215, 0, 0.1);
    padding: 0;
}

.ceo-image {
    flex: 0 0 40%;
    height: 100%;
    overflow: hidden;
    position: relative;
}

.ceo-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.ceo-container:hover .ceo-image img {
    transform: scale(1.05);
}

.ceo-info {
    flex: 1;
    padding: var(--spacing-lg);
    position: relative;
}

.ceo-title h2 {
    font-size: 1.5rem;
    color: var(--color-gold);
    margin-bottom: var(--spacing-sm);
    position: relative;
    display: inline-block;
}

.ceo-title h2::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--color-gold);
    transition: width 0.3s ease;
}

.ceo-container:hover .ceo-title h2::after {
    width: 100%;
}

.ceo-name h3 {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
    background: linear-gradient(to right, #ffffff, #ffd700);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.ceo-position p {
    font-size: 1.2rem;
    color: var(--color-light);
    margin-bottom: var(--spacing-md);
    font-style: italic;
}

.ceo-bio {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.ceo-social {
    display: flex;
    gap: var(--spacing-sm);
}

.ceo-social .social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.ceo-social .social-icon:hover {
    background-color: var(--color-gold);
    color: var(--color-black);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

@media (max-width: 992px) {
    .ceo-container {
        flex-direction: column;
    }

    .ceo-image {
        flex: 0 0 auto;
        width: 100%;
        height: 400px;
    }

    .ceo-name h3 {
        font-size: 2rem;
    }
}

/* Media Coverage Section */
.media-coverage-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
}

.media-coverage-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../img/pattern.png') repeat;
    opacity: 0.03;
    z-index: 1;
}

.media-coverage-section .container {
    position: relative;
    z-index: 2;
}

.coverage-subsection {
    margin: 60px 0;
}

.subsection-title {
    text-align: center;
    color: var(--color-gold);
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 40px;
    position: relative;
}

.subsection-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--color-gold), #f1e5ac);
    border-radius: 2px;
}

/* Coverage Swiper */
.advertising-swiper,
.tv-swiper {
    padding: 20px 0;
    overflow: visible;
}

.advertising-swiper .swiper-slide,
.tv-swiper .swiper-slide {
    width: auto;
}

/* Coverage Items */
.coverage-item {
    width: 300px;
    height: 200px;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid rgba(212, 175, 55, 0.2);
    cursor: pointer;
    position: relative;
}

.coverage-item:hover {
    transform: translateY(-5px);
    border-color: var(--color-gold);
    box-shadow: 0 15px 30px rgba(212, 175, 55, 0.3);
}

.coverage-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.coverage-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    filter: brightness(0.8);
}

.coverage-item:hover .coverage-image img {
    transform: scale(1.05);
    filter: brightness(1);
}

.coverage-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.coverage-item:hover .coverage-overlay {
    transform: translateY(0);
}

.coverage-info h4 {
    color: var(--color-white);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.coverage-info p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Responsive Design for Media Coverage */
@media (max-width: 768px) {
    .media-coverage-section {
        padding: 60px 0;
    }

    .subsection-title {
        font-size: 1.5rem;
        margin-bottom: 30px;
    }

    .coverage-subsection {
        margin: 40px 0;
    }

    .coverage-item {
        width: 240px;
        margin: 0 15px;
    }

    .coverage-image {
        height: 150px;
    }

    .coverage-info {
        padding: 15px;
    }

    .coverage-info h4 {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .coverage-item {
        width: 200px;
        margin: 0 10px;
    }

    .coverage-image {
        height: 120px;
    }

    .coverage-info {
        padding: 12px;
    }

    .coverage-info h4 {
        font-size: 0.9rem;
    }
}

/* Photo Gallery Section */
.gallery-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
    position: relative;
    overflow: hidden;
}

.gallery-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('../img/pattern.png') repeat;
    opacity: 0.02;
    z-index: 1;
}

.gallery-section .container {
    position: relative;
    z-index: 2;
}

/* Gallery Swiper */
.gallery-swiper {
    padding: 40px 0;
    overflow: visible;
}

.gallery-swiper .swiper-slide {
    width: auto;
    height: auto;
}

/* Gallery Items */
.gallery-item {
    width: 350px;
    height: 250px;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    transition: all 0.4s ease;
    border: 2px solid rgba(212, 175, 55, 0.1);
}

.gallery-item:hover {
    transform: translateY(-10px) scale(1.02);
    border-color: var(--color-gold);
    box-shadow: 0 25px 50px rgba(212, 175, 55, 0.3);
}

.gallery-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
    filter: brightness(0.8);
}

.gallery-item:hover .gallery-image img {
    transform: scale(1.1);
    filter: brightness(1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent 40%, rgba(0, 0, 0, 0.9));
    opacity: 0;
    transition: opacity 0.4s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 20px;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-info {
    margin-top: auto;
}

.gallery-info h4 {
    color: var(--color-white);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.gallery-info p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0 0 10px 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.gallery-category {
    display: inline-block;
    background: rgba(212, 175, 55, 0.2);
    color: var(--color-gold);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.gallery-actions {
    position: absolute;
    top: 20px;
    right: 20px;
}

.gallery-zoom {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(212, 175, 55, 0.9);
    border: none;
    color: var(--color-white);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-zoom:hover {
    background: var(--color-gold);
    transform: scale(1.1);
}

/* Gallery Lightbox */
.gallery-lightbox {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    z-index: 10000;
    backdrop-filter: blur(10px);
}

.gallery-lightbox .lightbox-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 60px 20px 20px;
}

.lightbox-image-container {
    position: relative;
    max-width: 90%;
    max-height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.lightbox-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    pointer-events: none;
}

.lightbox-prev,
.lightbox-next {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(212, 175, 55, 0.8);
    border: none;
    color: var(--color-white);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: all;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    background: var(--color-gold);
    transform: scale(1.1);
}

.lightbox-prev {
    margin-left: -80px;
}

.lightbox-next {
    margin-right: -80px;
}

.lightbox-info {
    text-align: center;
    margin-top: 30px;
    max-width: 600px;
}

.lightbox-info h3 {
    color: var(--color-white);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 10px 0;
}

.lightbox-info p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    line-height: 1.5;
    margin: 0;
}

.gallery-lightbox .close-lightbox {
    position: absolute;
    top: 30px;
    right: 40px;
    font-size: 3rem;
    color: var(--color-white);
    cursor: pointer;
    transition: color 0.3s ease;
    z-index: 10001;
}

.gallery-lightbox .close-lightbox:hover {
    color: var(--color-gold);
}

/* Performance Optimizations */
img, video {
    backface-visibility: hidden;
}

/* GPU acceleration for smooth animations */
.swiper-slide, .gallery-item, .sponsor-item, .celebrity-item {
    transform: translateZ(0);
}

/* Optimize font loading */
@font-face {
    font-family: 'Cairo';
    font-display: swap;
}

@font-face {
    font-family: 'Poppins';
    font-display: swap;
}

/* Mobile Responsive Styles for Gallery */
@media (max-width: 768px) {
    .gallery-section {
        padding: 60px 0;
    }

    .gallery-swiper {
        padding: 30px 0;
    }

    .gallery-item {
        width: 280px;
        height: 200px;
    }

    .gallery-info h4 {
        font-size: 1rem;
    }

    .gallery-info p {
        font-size: 0.8rem;
    }

    .gallery-zoom {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .lightbox-prev,
    .lightbox-next {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .lightbox-prev {
        margin-left: -60px;
    }

    .lightbox-next {
        margin-right: -60px;
    }

    .lightbox-info h3 {
        font-size: 1.2rem;
    }

    .lightbox-info p {
        font-size: 0.9rem;
    }

    .gallery-lightbox .close-lightbox {
        top: 20px;
        right: 20px;
        font-size: 2.5rem;
    }
}

@media (max-width: 480px) {
    .gallery-item {
        width: 250px;
        height: 180px;
    }

    .gallery-overlay {
        padding: 15px;
    }

    .gallery-info h4 {
        font-size: 0.9rem;
    }

    .gallery-info p {
        font-size: 0.75rem;
    }

    .gallery-category {
        font-size: 0.7rem;
        padding: 3px 8px;
    }

    .gallery-zoom {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .lightbox-image-container {
        max-width: 95%;
        max-height: 70%;
    }

    .lightbox-prev,
    .lightbox-next {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .lightbox-prev {
        margin-left: -50px;
    }

    .lightbox-next {
        margin-right: -50px;
    }

    .lightbox-info {
        margin-top: 20px;
        padding: 0 15px;
    }

    .lightbox-info h3 {
        font-size: 1rem;
    }

    .lightbox-info p {
        font-size: 0.8rem;
    }
}

/* Team Section Styles */
.team-section {
    background: linear-gradient(to bottom, #000000, #111111);
    padding: var(--spacing-xl) 0;
    color: var(--color-white);
}

.team-swiper {
    width: 100%;
    padding: var(--spacing-lg) 0;
    overflow: visible;
}

.team-swiper .swiper-slide {
    margin-left: 30px !important;
}

.team-member {
    width: 250px;
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 215, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    margin: 0 auto;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.3);
}

.member-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-info {
    padding: var(--spacing-md);
    text-align: center;
    position: relative;
}

.member-info h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
    color: var(--color-gold);
}

.member-position {
    font-size: 0.9rem;
    color: var(--color-light);
    margin-bottom: var(--spacing-md);
    font-style: italic;
}

.member-social {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: var(--spacing-sm);
}

.member-social .social-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.member-social .social-icon:hover {
    background-color: var(--color-gold);
    color: var(--color-black);
    transform: translateY(-3px);
}

/* Invitations Section Styles */
.invitations-section {
    background: linear-gradient(to bottom, #111111, #000000);
    padding: var(--spacing-xl) 0;
    color: var(--color-white);
}

.invitations-swiper {
    width: 100%;
    padding: var(--spacing-lg) 0;
    overflow: visible;
}

.invitations-swiper .swiper-slide {
    margin-left: 30px !important;
}

.invitation-item {
    width: 300px;
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 215, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    margin: 0 auto;
}

.invitation-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.3);
}

.invitation-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.invitation-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.invitation-item:hover .invitation-image img {
    transform: scale(1.1);
}

.invitation-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.invitation-item:hover .invitation-overlay {
    opacity: 1;
}

.invitation-zoom {
    width: 50px;
    height: 50px;
    background-color: var(--color-gold);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    transform: scale(0);
    transition: transform 0.3s ease;
}

.invitation-item:hover .invitation-zoom {
    transform: scale(1);
}

.invitation-info {
    padding: var(--spacing-md);
    text-align: center;
}

.invitation-info h3 {
    font-size: 1.2rem;
    margin: 0;
    color: var(--color-gold);
}

/* Lightbox Styles */
.invitation-lightbox {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 9999;
    align-items: center;
    justify-content: center;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 90vh;
    display: block;
    border: 3px solid var(--color-gold);
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);
}

.close-lightbox {
    position: absolute;
    top: -40px;
    right: 0;
    color: var(--color-white);
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-lightbox:hover {
    color: var(--color-gold);
}

@media (max-width: 768px) {
    .invitations-gallery {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .invitation-image {
        height: 250px;
    }
    .sponsors-header{
        margin-top: 15%;
    }
    .nominees-title , .celebrities-title , .stories-title{
        margin-top: 20%;
    }
}

.newsletter-form {
    display: flex;
    margin-top: var(--spacing-md);
}

.newsletter-form input {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    background-color: var(--color-black);
    color: var(--color-white);
}

.newsletter-form button {
    padding: 0.75rem 1rem;
    background-color: var(--color-gold);
    color: var(--color-black);
    border: none;
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
    cursor: pointer;
    transition: background-color var(--transition-normal);
}

.newsletter-form button:hover {
    background-color: var(--color-gold-light);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright {
    font-size: 0.9rem;
    color: var(--color-gray);
}

.footer-links a {
    color: var(--color-gray);
    margin-left: var(--spacing-md);
    font-size: 0.9rem;
}

.footer-links a:hover {
    color: var(--color-gold);
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== Media Queries ===== */
@media (max-width: 992px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.5rem;
    }
}

@media (max-width: 768px) {
    .menu-toggle {
        display: block;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background-color: rgba(0, 0, 0, 0.95);
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: left var(--transition-normal), right var(--transition-normal);
        z-index: 1000;
        backdrop-filter: blur(10px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    }

    .nav-menu.active {
        left: 0;
    }

    /* Fix for RTL on mobile */
    html[dir="rtl"] .nav-menu {
        left: auto;
        right: -100%;
    }

    html[dir="rtl"] .nav-menu.active {
        right: 0;
        left: auto;
    }

    .nav-menu li {
        margin: var(--spacing-md) 0;
        width: 100%;
        text-align: center;
    }

    .nav-menu a {
        display: block;
        padding: 10px 15px;
        font-size: 1.1rem;
    }

    .nav-menu a::after {
        bottom: 5px;
        width: 0;
        height: 2px;
        left: 50%;
        transform: translateX(-50%);
        transition: width var(--transition-normal);
    }

    .nav-menu a:hover::after,
    .nav-menu li.active a::after {
        width: 50px;
    }

    /* RTL specific styles for nav items */
    html[dir="rtl"] .nav-menu li {
        text-align: center;
    }

    html[dir="rtl"] .nav-menu a::after {
        right: 50%;
        left: auto;
        transform: translateX(50%);
    }

    .header-actions {
        flex-direction: column;
    }

    .language-switcher {
        margin-right: 0;
        margin-bottom: var(--spacing-sm);
    }

    /* RTL specific styles for language switcher */
    html[dir="rtl"] .language-switcher {
        margin-left: 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }





    /* Footer adjustments for mobile */
    .footer-top {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-logo {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: var(--spacing-md);
    }

    .footer-nav ul,
    .footer-contact ul {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .social-links {
        justify-content: center;
        margin-top: var(--spacing-sm);
    }

    .newsletter-form {
        max-width: 300px;
        margin: var(--spacing-md) auto 0;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .copyright {
        margin-bottom: var(--spacing-md);
    }

    .footer-links a {
        margin: 0 var(--spacing-xs);
    }
}

@media (max-width: 576px) {
    .hero-content {
        padding: var(--spacing-md);
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn-vote-hero {
        font-size: 1rem;
        padding: 0.75rem 1.5rem;
    }

    /* Extra Small Screens - Sponsors Adjustments */
    .sponsors-pyramid {
        gap: 20px;
        padding: 15px 0;
    }

    .level-title {
        font-size: 1.2rem;
        margin-bottom: 20px;
    }

    .level-title::after {
        width: 50px;
        height: 2px;
    }

    .sponsor-item.platinum,
    .sponsor-item.gold,
    .sponsor-item.silver {
        width: 200px !important;
        height: 200px !important;
    }

    .sponsor-item.platinum .sponsor-logo,
    .sponsor-item.gold .sponsor-logo,
    .sponsor-item.silver .sponsor-logo {
        width: 200px;
        height: 200px;
    }

    .sponsor-item.platinum .sponsor-logo img,
    .sponsor-item.gold .sponsor-logo img,
    .sponsor-item.silver .sponsor-logo img {
        width: 200px !important;
        height: 200px !important;
    }

    .sponsor-link {
        padding: 0;
    }

    .sponsor-info {
        display: none;
    }





    /* Footer adjustments for very small screens */
    .footer-top {
        gap: var(--spacing-md);
    }

    .footer-nav h3,
    .footer-contact h3,
    .footer-newsletter h3 {
        font-size: 1.1rem;
        margin-bottom: var(--spacing-sm);
    }

    .footer-nav li,
    .footer-contact li {
        margin-bottom: 8px;
    }

    .newsletter-form {
        max-width: 250px;
    }

    .newsletter-form input {
        padding: 0.6rem;
    }

    .newsletter-form button {
        padding: 0.6rem 0.8rem;
    }

    .social-links a {
        width: 35px;
        height: 35px;
    }
}

/* ===== Enhanced Influencers Section ===== */
.influencers-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, var(--color-black) 100%);
    position: relative;
    overflow: hidden;
    min-height: 100vh;
}

/* Background Elements */
.section-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-icon {
    position: absolute;
    color: rgba(212, 175, 55, 0.1);
    font-size: 3rem;
    animation: float 6s ease-in-out infinite;
}

.floating-icon.icon-1 {
    top: 10%;
    left: 5%;
    animation-delay: 0s;
}

.floating-icon.icon-2 {
    top: 20%;
    right: 10%;
    animation-delay: 1s;
}

.floating-icon.icon-3 {
    top: 60%;
    left: 8%;
    animation-delay: 2s;
}

.floating-icon.icon-4 {
    top: 70%;
    right: 5%;
    animation-delay: 3s;
}

.floating-icon.icon-5 {
    top: 40%;
    left: 50%;
    animation-delay: 4s;
}

.floating-icon.icon-6 {
    top: 80%;
    left: 50%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

.influencers-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 2;
}

/* Enhanced Section Header */
.influencers-section .section-header {
    position: relative;
    z-index: 3;
    margin-bottom: 60px;
}

.header-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    gap: 20px;
}

.decoration-line {
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, var(--color-gold) 50%, transparent 100%);
}

.decoration-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    font-size: 24px;
    box-shadow: 0 10px 30px rgba(212, 175, 55, 0.4);
    animation: pulse 2s infinite;
}

.section-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 20px;
    text-align: center;
    line-height: 1.2;
}

.title-highlight {
    color: var(--color-gold);
    display: block;
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.title-main {
    color: var(--color-white);
    display: block;
    font-size: 4rem;
    font-weight: 900;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.3rem;
    color: var(--color-light-gray);
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.subtitle-icon {
    color: var(--color-gold);
    font-size: 1.5rem;
}

/* Stats Bar */
.stats-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    margin-top: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    backdrop-filter: blur(10px);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    color: var(--color-gold);
    line-height: 1;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: var(--color-light-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-divider {
    width: 1px;
    height: 40px;
    background: linear-gradient(180deg, transparent 0%, var(--color-gold) 50%, transparent 100%);
}

.influencers-swiper {
    padding: 20px 0 60px;
    position: relative;
}

.influencers-swiper .swiper-slide {
    margin-right: var(--spacing-md); /* نفس مساحة Festival Highlights (16px) */
}

/* Enhanced Influencer Cards */
.influencer-item {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border-radius: 25px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    padding: 35px 25px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(15px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Rank Badge */
.rank-badge {
    position: absolute;
    top: -10px;
    left: -10px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
    border: 3px solid var(--color-black);
}

.rank-number {
    font-size: 14px;
    font-weight: 900;
    color: var(--color-black);
    text-shadow: none;
}

.influencer-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity var(--transition-normal);
    pointer-events: none;
}

.influencer-item:hover {
    transform: translateY(-10px);
    border-color: var(--color-gold);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.influencer-item:hover::before {
    opacity: 1;
}

/* Enhanced Image Container */
.influencer-image {
    position: relative;
    width: 140px;
    height: 140px;
    margin: 0 auto 25px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--color-gold);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.4);
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    padding: 3px;
}

.default-avatar {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #333 0%, #555 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-gold);
    font-size: 3rem;
}

.influencer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.influencer-item:hover .influencer-image img {
    transform: scale(1.1);
}

/* Enhanced Influence Badge */
.influence-badge {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    z-index: 5;
    animation: pulse 2s infinite;
}

.badge-inner {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    font-weight: bold;
    border: 3px solid var(--color-black);
    position: relative;
    z-index: 2;
}

.badge-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.6) 0%, transparent 70%);
    border-radius: 50%;
    z-index: 1;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        opacity: 0.5;
        transform: scale(1);
    }
    to {
        opacity: 1;
        transform: scale(1.1);
    }
}

.influence-badge .score {
    font-size: 14px;
    line-height: 1;
}

.influence-badge .label {
    font-size: 8px;
    line-height: 1;
}

/* Enhanced Platform Badge */
.platform-badge {
    position: absolute;
    bottom: -8px;
    left: -8px;
    background: rgba(0, 0, 0, 0.9);
    color: var(--color-gold);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    border: 3px solid var(--color-gold);
    z-index: 4;
    transition: all var(--transition-normal);
}

.platform-badge:hover {
    transform: scale(1.1);
    background: var(--color-gold);
    color: var(--color-black);
}

.platform-tooltip {
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-black);
    color: var(--color-white);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 10;
}

.platform-badge:hover .platform-tooltip {
    opacity: 1;
    visibility: visible;
    bottom: 55px;
}

/* Verification Badge */
.verification-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #1da1f2;
    font-size: 20px;
    z-index: 4;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.influencer-info {
    flex: 1;
    margin-bottom: 20px;
}

.influencer-info h3 {
    color: var(--color-white);
    font-size: 1.4rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.influencer-title {
    color: var(--color-gold);
    font-size: 1rem;
    margin-bottom: 15px;
    font-weight: 500;
}

/* Enhanced Metrics */
.influence-metrics {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.metric-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    flex: 1;
}

.metric-item i {
    color: var(--color-gold);
    font-size: 1.2rem;
}

.metric-item span {
    color: var(--color-white);
    font-size: 1.1rem;
    font-weight: 700;
}

.metric-item small {
    color: var(--color-light-gray);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-divider {
    width: 1px;
    height: 30px;
    background: linear-gradient(180deg, transparent 0%, var(--color-gold) 50%, transparent 100%);
}

/* Bio Preview */
.bio-preview {
    margin: 15px 0;
    padding: 10px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    border-left: 3px solid var(--color-gold);
}

.bio-preview p {
    color: var(--color-light-gray);
    font-size: 0.85rem;
    line-height: 1.4;
    margin: 0;
    font-style: italic;
}

/* Legacy followers count (for backward compatibility) */
.followers-count {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--color-light-gray);
    font-size: 0.9rem;
    margin-bottom: 20px;
}

.followers-count i {
    color: var(--color-gold);
}

.influencer-social {
    display: flex;
    justify-content: center;
    gap: 12px;
    flex-wrap: wrap;
}

.influencer-social .social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    text-decoration: none;
    transition: all var(--transition-normal);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 16px;
}

.influencer-social .social-icon:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.influencer-social .social-icon.facebook {
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
}

.influencer-social .social-icon.twitter {
    background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
}

.influencer-social .social-icon.instagram {
    background: linear-gradient(135deg, #e4405f 0%, #fd1d1d 50%, #fcb045 100%);
}

.influencer-social .social-icon.youtube {
    background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
}

.influencer-social .social-icon.tiktok {
    background: linear-gradient(135deg, #000000 0%, #ff0050 50%, #00f2ea 100%);
}

.influencer-social .social-icon.linkedin {
    background: linear-gradient(135deg, #0077b5 0%, #005885 100%);
}

.influencer-social .social-icon.website {
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
}

/* Enhanced Social Icons with Tooltips */
.influencer-social .social-icon[data-tooltip] {
    position: relative;
}

.influencer-social .social-icon[data-tooltip]:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--color-black);
    color: var(--color-white);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 10;
    opacity: 1;
    visibility: visible;
}

/* Action Button */
.influencer-action {
    margin-top: auto;
    padding-top: 20px;
}

.follow-btn {
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.follow-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
    background: linear-gradient(135deg, #ffd700 0%, var(--color-gold) 100%);
}

.follow-btn i {
    transition: transform var(--transition-normal);
}

.follow-btn:hover i {
    transform: scale(1.2);
}

/* Section Footer */
.section-footer {
    margin-top: 60px;
}

.view-all-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: linear-gradient(135deg, rgba(212, 175, 55, 0.2) 0%, rgba(212, 175, 55, 0.1) 100%);
    color: var(--color-gold);
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 50px;
    border: 2px solid var(--color-gold);
    font-weight: 600;
    font-size: 1.1rem;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.view-all-btn:hover {
    background: var(--color-gold);
    color: var(--color-black);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);
}

.view-all-btn i {
    transition: transform var(--transition-normal);
}

.view-all-btn:hover i {
    transform: translateX(5px);
}

/* Enhanced Swiper Navigation */
.swiper-navigation {
    position: relative;
    z-index: 10;
}

.influencers-swiper .swiper-button-next,
.influencers-swiper .swiper-button-prev {
    color: var(--color-gold);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 2px solid var(--color-gold);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.influencers-swiper .swiper-button-next:hover,
.influencers-swiper .swiper-button-prev:hover {
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    transform: scale(1.15);
    box-shadow: 0 15px 40px rgba(212, 175, 55, 0.4);
}

.influencers-swiper .swiper-button-next::after,
.influencers-swiper .swiper-button-prev::after {
    font-size: 20px;
    font-weight: 900;
    display: none;
}

.influencers-swiper .swiper-button-next i,
.influencers-swiper .swiper-button-prev i {
    font-size: 20px;
    font-weight: 900;
}

/* Enhanced Pagination */
.influencers-swiper .swiper-pagination {
    bottom: 30px;
    z-index: 10;
}

.influencers-swiper .swiper-pagination-bullet {
    background: rgba(212, 175, 55, 0.4);
    opacity: 1;
    width: 15px;
    height: 15px;
    margin: 0 8px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    cursor: pointer;
}

.influencers-swiper .swiper-pagination-bullet:hover {
    background: rgba(212, 175, 55, 0.7);
    transform: scale(1.1);
}

.influencers-swiper .swiper-pagination-bullet-active {
    background: var(--color-gold);
    transform: scale(1.3);
    border-color: var(--color-white);
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
}

/* Pulse animation for influence badge */
@keyframes pulse {
    0% {
        box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
    }
    50% {
        box-shadow: 0 5px 25px rgba(212, 175, 55, 0.6);
    }
    100% {
        box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
    }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .title-main {
        font-size: 3.5rem;
    }

    .title-highlight {
        font-size: 2.2rem;
    }

    .stats-bar {
        gap: 20px;
        padding: 15px;
    }

    .stat-number {
        font-size: 1.8rem;
    }
}

@media (max-width: 992px) {
    .influencers-section {
        padding: 80px 0;
    }

    .section-header {
        margin-bottom: 50px;
    }

    .title-main {
        font-size: 3rem;
    }

    .title-highlight {
        font-size: 2rem;
    }

    .stats-bar {
        flex-direction: column;
        gap: 15px;
        padding: 20px;
    }

    .stat-divider {
        width: 80%;
        height: 1px;
        background: linear-gradient(90deg, transparent 0%, var(--color-gold) 50%, transparent 100%);
    }

    .floating-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .influencers-section {
        padding: 60px 0;
    }

    .section-header {
        margin-bottom: 40px;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .title-highlight {
        font-size: 1.8rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }

    .decoration-line {
        width: 60px;
    }

    .decoration-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .influencer-item {
        padding: 25px 20px;
    }

    .influencers-swiper .swiper-slide {
        margin-right: var(--spacing-sm); /* مساحة أصغر للموبايل (8px) */
    }

    .influencer-image {
        width: 120px;
        height: 120px;
    }

    .influence-badge {
        width: 50px;
        height: 50px;
        top: -12px;
        right: -12px;
    }

    .badge-inner .score {
        font-size: 12px;
    }

    .badge-inner .label {
        font-size: 7px;
    }

    .platform-badge {
        width: 35px;
        height: 35px;
        font-size: 16px;
        bottom: -6px;
        left: -6px;
    }

    .rank-badge {
        width: 45px;
        height: 45px;
        top: -8px;
        left: -8px;
    }

    .rank-number {
        font-size: 12px;
    }

    .influencer-info h3 {
        font-size: 1.3rem;
    }

    .influencer-title {
        font-size: 1rem;
    }

    .influence-metrics {
        padding: 12px;
        gap: 12px;
    }

    .metric-item span {
        font-size: 1rem;
    }

    .metric-item small {
        font-size: 0.75rem;
    }

    .bio-preview {
        padding: 8px;
    }

    .bio-preview p {
        font-size: 0.8rem;
    }

    .influencer-social .social-icon {
        width: 38px;
        height: 38px;
        font-size: 15px;
    }

    .follow-btn {
        padding: 10px 20px;
        font-size: 0.85rem;
    }

    .view-all-btn {
        padding: 12px 25px;
        font-size: 1rem;
    }

    .influencers-swiper .swiper-button-next,
    .influencers-swiper .swiper-button-prev {
        width: 50px;
        height: 50px;
    }

    .influencers-swiper .swiper-button-next i,
    .influencers-swiper .swiper-button-prev i {
        font-size: 18px;
    }

    .floating-icon {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .title-main {
        font-size: 2rem;
    }

    .title-highlight {
        font-size: 1.5rem;
    }

    .section-subtitle {
        font-size: 1rem;
        flex-direction: column;
        gap: 5px;
    }

    .stats-bar {
        padding: 15px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .influencer-item {
        padding: 20px 15px;
    }

    .influencers-swiper .swiper-slide {
        margin-right: var(--spacing-sm); /* مساحة أصغر للموبايل الصغير (8px) */
    }

    .influencer-image {
        width: 100px;
        height: 100px;
    }

    .influence-badge {
        width: 45px;
        height: 45px;
        top: -10px;
        right: -10px;
    }

    .platform-badge {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .rank-badge {
        width: 40px;
        height: 40px;
    }

    .influencer-info h3 {
        font-size: 1.2rem;
    }

    .influencer-title {
        font-size: 0.9rem;
    }

    .influence-metrics {
        padding: 10px;
        gap: 10px;
    }

    .influencer-social .social-icon {
        width: 35px;
        height: 35px;
        font-size: 14px;
    }

    .influencers-swiper .swiper-button-next,
    .influencers-swiper .swiper-button-prev {
        width: 45px;
        height: 45px;
    }

    .floating-icon {
        font-size: 1.5rem;
    }

    .floating-icon.icon-5,
    .floating-icon.icon-6 {
        display: none;
    }
}

/* ===== Influencers Page Styles ===== */
.influencers-page-header {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, var(--color-black) 100%);
    padding: 100px 0 80px;
    position: relative;
    overflow: hidden;
}

.influencers-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 50%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.breadcrumb {
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--color-light-gray);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.breadcrumb a:hover {
    color: var(--color-gold);
}

.breadcrumb .separator {
    margin: 0 10px;
    color: var(--color-light-gray);
}

.breadcrumb .current {
    color: var(--color-gold);
    font-weight: 600;
}

.page-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--color-white);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.page-title i {
    color: var(--color-gold);
}

.page-subtitle {
    font-size: 1.3rem;
    color: var(--color-light-gray);
    margin-bottom: 40px;
}

.stats-overview {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-top: 30px;
}

.stats-overview .stat-item {
    text-align: center;
}

.stats-overview .stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--color-gold);
    line-height: 1;
    margin-bottom: 8px;
}

.stats-overview .stat-label {
    font-size: 1rem;
    color: var(--color-light-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Filters Section */
.filters-section {
    background: rgba(0, 0, 0, 0.5);
    padding: 30px 0;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.filters-container {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(10px);
}

.filters-form {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--color-gold);
    z-index: 2;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 25px;
    background: rgba(0, 0, 0, 0.3);
    color: var(--color-white);
    font-size: 1rem;
    transition: all var(--transition-normal);
}

.search-box input:focus {
    outline: none;
    border-color: var(--color-gold);
    background: rgba(0, 0, 0, 0.5);
}

.search-box input::placeholder {
    color: var(--color-light-gray);
}

.filter-group select {
    padding: 12px 20px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 25px;
    background: rgba(0, 0, 0, 0.3);
    color: var(--color-white);
    font-size: 1rem;
    min-width: 150px;
    transition: all var(--transition-normal);
}

.filter-group select:focus {
    outline: none;
    border-color: var(--color-gold);
    background: rgba(0, 0, 0, 0.5);
}

.filter-group select option {
    background: var(--color-black);
    color: var(--color-white);
}

.search-btn {
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 700;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.clear-filters {
    color: var(--color-light-gray);
    text-decoration: none;
    padding: 12px 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 8px;
}

.clear-filters:hover {
    color: var(--color-white);
    border-color: var(--color-gold);
    background: rgba(212, 175, 55, 0.1);
}

/* Results Section */
.influencers-grid-section {
    padding: 60px 0;
    background: var(--color-black);
}

.no-results {
    text-align: center;
    padding: 80px 20px;
}

.no-results-icon {
    font-size: 4rem;
    color: var(--color-gold);
    margin-bottom: 20px;
}

.no-results h3 {
    color: var(--color-white);
    font-size: 2rem;
    margin-bottom: 15px;
}

.no-results p {
    color: var(--color-light-gray);
    font-size: 1.1rem;
    margin-bottom: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.results-header h2 {
    color: var(--color-gold);
    font-size: 1.8rem;
    margin: 0;
}

.view-toggle {
    display: flex;
    gap: 10px;
}

.view-btn {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    background: transparent;
    color: var(--color-light-gray);
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-btn.active,
.view-btn:hover {
    border-color: var(--color-gold);
    color: var(--color-gold);
    background: rgba(212, 175, 55, 0.1);
}

/* Influencers Grid */
.influencers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 30px;
}

.influencer-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(15px);
}

.influencer-card:hover {
    transform: translateY(-8px);
    border-color: var(--color-gold);
    box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
}

.rank-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    padding: 5px 12px;
    border-radius: 15px;
    font-weight: 700;
    font-size: 0.9rem;
    z-index: 3;
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
    z-index: 3;
}

.card-header {
    padding: 30px 25px 20px;
    text-align: center;
    position: relative;
}

.influencer-card .influencer-image {
    width: 120px;
    height: 120px;
    margin: 0 auto 20px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--color-gold);
    position: relative;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    padding: 3px;
}

.influencer-card .influencer-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.influencer-card .default-avatar {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #333 0%, #555 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-gold);
    font-size: 2.5rem;
}

.score-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.8rem;
    border: 3px solid var(--color-black);
    z-index: 2;
}

.score-badge .score {
    font-size: 1rem;
    line-height: 1;
}

.score-badge .max {
    font-size: 0.7rem;
    opacity: 0.8;
}

.influencer-card .platform-badge {
    position: absolute;
    bottom: -8px;
    left: -8px;
    background: rgba(0, 0, 0, 0.9);
    color: var(--color-gold);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    border: 3px solid var(--color-gold);
    z-index: 2;
}

.card-body {
    padding: 0 25px 20px;
    text-align: center;
}

.influencer-name {
    color: var(--color-white);
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 8px 0;
}

.influencer-title {
    color: var(--color-gold);
    font-size: 1rem;
    margin: 0 0 20px 0;
}

.metrics {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 20px 0;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    flex: 1;
}

.metric i {
    color: var(--color-gold);
    font-size: 1.2rem;
}

.metric span {
    color: var(--color-white);
    font-weight: 700;
    font-size: 1.1rem;
}

.metric small {
    color: var(--color-light-gray);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bio-preview {
    margin: 15px 0;
    padding: 12px;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border-left: 3px solid var(--color-gold);
}

.bio-preview p {
    color: var(--color-light-gray);
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 0;
    font-style: italic;
}

.card-footer {
    padding: 20px 25px;
    border-top: 1px solid rgba(212, 175, 55, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.social-links {
    display: flex;
    gap: 8px;
}

.social-link {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all var(--transition-normal);
    font-size: 14px;
}

.social-link.facebook { background: #1877f2; }
.social-link.twitter { background: #1da1f2; }
.social-link.instagram { background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%); }
.social-link.youtube { background: #ff0000; }
.social-link.tiktok { background: #000000; }
.social-link.linkedin { background: #0077b5; }
.social-link.website { background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%); color: var(--color-black); }

.social-link:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.follow-btn {
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 6px;
}

.follow-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(212, 175, 55, 0.4);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all var(--transition-normal);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
}

/* List View */
.influencers-grid.list-view {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.influencers-grid.list-view .influencer-card {
    display: flex;
    align-items: center;
    text-align: left;
    padding: 20px;
}

.influencers-grid.list-view .card-header {
    padding: 0;
    margin-right: 20px;
}

.influencers-grid.list-view .influencer-image {
    width: 80px;
    height: 80px;
    margin: 0;
}

.influencers-grid.list-view .card-body {
    flex: 1;
    padding: 0;
    text-align: left;
}

.influencers-grid.list-view .metrics {
    justify-content: flex-start;
    margin: 10px 0;
}

.influencers-grid.list-view .card-footer {
    padding: 0;
    border: none;
    margin-left: 20px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .influencers-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 25px;
    }
}

@media (max-width: 768px) {
    .influencers-page-header {
        padding: 80px 0 60px;
    }

    .page-title {
        font-size: 2.5rem;
        flex-direction: column;
        gap: 10px;
    }

    .page-subtitle {
        font-size: 1.1rem;
    }

    .stats-overview {
        flex-direction: column;
        gap: 20px;
    }

    .stats-overview .stat-number {
        font-size: 2rem;
    }

    .filters-form {
        flex-direction: column;
        gap: 15px;
    }

    .search-box {
        min-width: auto;
    }

    .filter-group select {
        min-width: auto;
        width: 100%;
    }

    .results-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .influencers-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .influencer-card {
        margin: 0 10px;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 576px) {
    .influencers-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .influencer-card {
        margin: 0;
    }

    .card-footer {
        flex-direction: column;
        gap: 15px;
    }

    .social-links {
        justify-content: center;
    }
}




