/* Vote Page Styles */
.vote-section {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(to bottom, #000000, #111111);
    min-height: 80vh;
}

.vote-section .section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.vote-section .section-header h1 {
    color: var(--color-gold);
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.vote-section .section-header p {
    color: var(--color-light);
    font-size: 1.2rem;
}

.login-prompt {
    background: rgba(0, 0, 0, 0.5);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.login-prompt .alert {
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
}

.login-prompt .btn {
    font-size: 1.1rem;
    padding: 0.75rem 2rem;
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.5);
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.5);
    color: #dc3545;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.5);
    color: #ffc107;
}

/* Main Categories Section */
.main-categories-section {
    margin-bottom: var(--spacing-xl);
}

.main-categories-section h2 {
    color: var(--color-white);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    text-align: center;
    position: relative;
    display: inline-block;
}

.main-categories-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--color-gold), transparent);
}

.main-categories {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.no-categories-message {
    grid-column: 1 / -1;
    background: rgba(0, 0, 0, 0.5);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.2);
    color: var(--color-light);
}

.no-categories-message i {
    font-size: 3rem;
    color: var(--color-gold);
    margin-bottom: var(--spacing-md);
}

.no-categories-message p {
    font-size: 1.2rem;
    margin-bottom: var(--spacing-md);
}

.setup-instructions {
    text-align: right;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.setup-instructions p {
    font-size: 1rem;
    color: var(--color-gold);
    margin-bottom: var(--spacing-sm);
}

.setup-instructions ol {
    text-align: right;
    padding-right: var(--spacing-md);
    margin-bottom: 0;
}

.setup-instructions li {
    margin-bottom: var(--spacing-xs);
}

.setup-instructions code {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
}

.main-category-item {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
}

.main-category-item:hover {
    transform: translateY(-5px);
    border-color: var(--color-gold);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.main-category-item.active {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    border-color: var(--color-gold);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.category-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-icon i {
    font-size: 2rem;
    color: var(--color-gold);
}

.main-category-item h3 {
    color: var(--color-white);
    font-size: 1.2rem;
    margin: 0;
    transition: color 0.3s ease;
}

.main-category-item:hover h3 {
    color: var(--color-gold);
}

.category-name {
    color: var(--color-white);
    font-size: 1rem;
    font-weight: 600;
    margin-top: 10px;
    text-align: center;
    transition: color 0.3s ease;
    line-height: 1.3;
}

.main-category-item:hover .category-name {
    color: var(--color-gold);
}

.main-category-item.active .category-name {
    color: var(--color-gold);
}

/* Subcategories Section */
.subcategories-section {
    margin-bottom: var(--spacing-xl);
}

.subcategories-section h2 {
    color: var(--color-white);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    text-align: center;
    position: relative;
    display: inline-block;
}

.subcategories-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--color-gold), transparent);
}

.subcategories {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.subcategory-item {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
}

.subcategory-item:hover {
    transform: translateY(-5px);
    border-color: var(--color-gold);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.subcategory-item.active {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    border-color: var(--color-gold);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.subcategory-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.subcategory-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.subcategory-icon i {
    font-size: 1.8rem;
    color: var(--color-gold);
}

.subcategory-item h3 {
    color: var(--color-white);
    font-size: 1.1rem;
    margin: 0;
    transition: color 0.3s ease;
}

.subcategory-item:hover h3 {
    color: var(--color-gold);
}

.subcategory-name {
    color: var(--color-white);
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 8px;
    text-align: center;
    transition: color 0.3s ease;
    line-height: 1.3;
}

.subcategory-item:hover .subcategory-name {
    color: var(--color-gold);
}

.subcategory-item.active .subcategory-name {
    color: var(--color-gold);
}

/* Nominees Section */
.nominees-section {
    margin-bottom: var(--spacing-xl);
}

.nominees-section h2 {
    color: var(--color-white);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    text-align: center;
    position: relative;
    display: inline-block;
}

.nominees-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--color-gold), transparent);
}

.nominees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-top: var(--spacing-lg);
}

.nominee-card {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
    border: 1px solid rgba(212, 175, 55, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.nominee-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(212, 175, 55, 0.1), transparent);
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.nominee-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
    border-color: rgba(212, 175, 55, 0.3);
}

.nominee-card:hover::before {
    opacity: 1;
}

.nominee-image {
    height: 300px;
    overflow: hidden;
    position: relative;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.nominee-card:hover .nominee-image img {
    transform: scale(1.05);
}

.nominee-rank {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: var(--color-gold);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    z-index: 1;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.nominee-info {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.nominee-name {
    color: var(--color-gold);
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.nominee-description {
    color: var(--color-white);
    opacity: 0.8;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 15px;
    flex: 1;
}

.nominee-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    flex-wrap: wrap;
}

.nominee-votes {
    display: flex;
    align-items: center;
    color: var(--color-white);
    font-size: 0.9rem;
}

.nominee-votes i {
    color: var(--color-gold);
    margin-right: 5px;
}

.nominee-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 10px;
    width: 100%;
    justify-content: center;
}

.btn-view, .btn-vote, .btn-compare {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    text-decoration: none;
}

.btn-view {
    background: rgba(255, 255, 255, 0.1);
    color: var(--color-white);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-vote {
    background: linear-gradient(145deg, var(--color-gold), var(--color-gold-dark));
    color: #000;
    border: none;
}

.btn-compare {
    background: rgba(0, 123, 255, 0.2);
    color: #0d6efd;
    border: 1px solid rgba(0, 123, 255, 0.3);
}

.btn-view:hover, .btn-vote:hover, .btn-compare:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-view i, .btn-vote i, .btn-compare i {
    margin-right: 5px;
}

.voted-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.voted-badge i {
    margin-right: 5px;
}

/* Nominee Profile */
.nominee-profile {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid rgba(255, 215, 0, 0.2);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.back-link {
    margin-bottom: var(--spacing-md);
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.back-link a {
    color: var(--color-light);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: color 0.3s ease;
}

.back-link a:hover {
    color: var(--color-gold);
}

.back-link i {
    margin-right: 5px;
}

.view-profile-link {
    background-color: rgba(255, 215, 0, 0.1);
    padding: 5px 10px;
    border-radius: var(--border-radius-sm);
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.nominee-profile-content {
    display: flex;
    gap: var(--spacing-lg);
}

.nominee-profile-image {
    flex: 0 0 300px;
    height: 400px;
    overflow: hidden;
    border-radius: var(--border-radius-md);
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.nominee-profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.nominee-profile-info {
    flex: 1;
}

.nominee-profile-info h2 {
    color: var(--color-gold);
    font-size: 2rem;
    margin-bottom: var(--spacing-xs);
}

.nominee-category {
    color: var(--color-light);
    font-size: 1.1rem;
    margin-bottom: var(--spacing-md);
    font-style: italic;
}

.nominee-bio {
    color: var(--color-white);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.nominee-stats {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-md);
    background-color: rgba(0, 0, 0, 0.3);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-md);
    display: inline-flex;
}

.votes-count {
    display: flex;
    align-items: center;
}

.votes-count i {
    color: var(--color-gold);
    font-size: 1.5rem;
    margin-right: 10px;
}

.votes-count span {
    color: var(--color-white);
    font-size: 1.5rem;
    font-weight: bold;
    margin-right: 5px;
}

.votes-label {
    color: var(--color-light);
    font-size: 1rem;
}

.nominee-social {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.nominee-social .social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.nominee-social .social-icon:hover {
    background-color: var(--color-gold);
    color: var(--color-black);
    transform: translateY(-3px);
}

.nominee-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    align-items: center;
}

.btn-vote {
    background-color: var(--color-gold);
    color: var(--color-black);
    border: none;
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
}

.btn-vote i {
    margin-right: 8px;
}

.btn-vote:hover {
    background-color: var(--color-gold-light);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.btn-vote.voted {
    background-color: #28a745;
    cursor: default;
}

.btn-vote.voted:hover {
    transform: none;
    box-shadow: none;
}

.share-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.share-buttons span {
    color: var(--color-light);
}

.share-button {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    transition: all 0.3s ease;
}

.share-button.facebook {
    background-color: #3b5998;
}

.share-button.twitter {
    background-color: #1da1f2;
}

.share-button.whatsapp {
    background-color: #25d366;
}

.share-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .nominee-profile-content {
        flex-direction: column;
    }

    .nominee-profile-image {
        flex: 0 0 auto;
        height: 350px;
        margin-bottom: var(--spacing-md);
    }
}

@media (max-width: 992px) {
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .nominee-image {
        height: 250px;
    }

    .nominee-profile-image {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .main-categories {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .subcategories {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }

    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 15px;
    }

    .nominee-image {
        height: 220px;
    }

    .nominee-profile-image {
        height: 300px;
    }

    .nominee-meta {
        flex-direction: column;
        align-items: center;
    }

    .nominee-votes {
        margin-bottom: 10px;
    }
}

@media (max-width: 576px) {
    .vote-section .section-header h1 {
        font-size: 2rem;
    }

    .vote-section .section-header p {
        font-size: 1rem;
    }

    .main-categories {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    }

    .category-icon {
        width: 60px;
        height: 60px;
    }

    .main-category-item h3 {
        font-size: 1rem;
    }

    .category-name {
        font-size: 0.85rem;
        margin-top: 8px;
    }

    .subcategory-icon {
        width: 50px;
        height: 50px;
    }

    .subcategory-item h3 {
        font-size: 0.9rem;
    }

    .subcategory-name {
        font-size: 0.8rem;
        margin-top: 6px;
    }

    .nominees-grid {
        grid-template-columns: 1fr;
    }

    .nominee-image {
        height: 250px;
    }

    .nominee-profile-image {
        height: 250px;
    }

    .nominee-profile-info h2 {
        font-size: 1.5rem;
    }

    .nominee-actions {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .share-buttons {
        margin-top: var(--spacing-sm);
    }
}
