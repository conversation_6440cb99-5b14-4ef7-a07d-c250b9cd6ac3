<?php
/**
 * Visits Tracking System for Social Media Festival
 *
 * This file contains functions for tracking and retrieving website visits statistics.
 */

/**
 * Record a visit to the website
 *
 * @param string $page The page being visited
 * @param int $user_id The ID of the logged-in user (if any)
 * @return bool True if the visit was recorded successfully, false otherwise
 */
function record_visit($page = 'home', $user_id = null) {
    global $conn;

    // Get visitor information
    $ip_address = get_ip_address();
    $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
    $current_date = date('Y-m-d');
    $current_time = date('H:i:s');
    $current_datetime = date('Y-m-d H:i:s');

    // Check if this is a unique visit (first visit from this IP today)
    $is_unique = is_unique_visit($ip_address, $current_date);

    // Get geo information
    $geo_info = get_geo_info($ip_address);
    $country = isset($geo_info['country']) ? $geo_info['country'] : null;
    $city = isset($geo_info['city']) ? $geo_info['city'] : null;
    $region = isset($geo_info['region']) ? $geo_info['region'] : null;

    // Get device information
    $device_info = get_device_info($user_agent);
    $device_type = $device_info['device_type'];
    $browser = $device_info['browser'];
    $os = $device_info['os'];

    // Get screen resolution (will be updated via JavaScript)
    $screen_resolution = null;

    // Get language
    $language = isset($_SERVER['HTTP_ACCEPT_LANGUAGE']) ? substr($_SERVER['HTTP_ACCEPT_LANGUAGE'], 0, 5) : null;

    // Get referrer
    $referrer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : null;

    // Insert visit record
    $sql = "INSERT INTO visits (
                ip_address, user_agent, page, user_id, visit_date, visit_time, visit_datetime, is_unique,
                country, city, region, device_type, browser, os, screen_resolution, language, referrer
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
            )";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return false;
    }

    $stmt->bind_param(
        "sssisssisssssssss",
        $ip_address, $user_agent, $page, $user_id, $current_date, $current_time, $current_datetime, $is_unique,
        $country, $city, $region, $device_type, $browser, $os, $screen_resolution, $language, $referrer
    );
    $result = $stmt->execute();

    if (!$result) {
        error_log("Error recording visit: " . $stmt->error);
        $stmt->close();
        return false;
    }

    $visit_id = $stmt->insert_id;
    $stmt->close();

    // Update summary tables
    update_visits_summary($current_date, $is_unique);

    if ($is_unique && $country) {
        update_geo_stats($country, $city, $region, $is_unique);
    }

    if ($device_type) {
        update_device_stats($device_type, $browser, $os, $is_unique);
    }

    if ($referrer) {
        update_referrer_stats($referrer, $is_unique);
    }

    // Add JavaScript to update screen resolution
    add_screen_resolution_script($visit_id);

    return true;
}

/**
 * Check if this is a unique visit (first visit from this IP today)
 *
 * @param string $ip_address The visitor's IP address
 * @param string $date The current date
 * @return bool True if this is a unique visit, false otherwise
 */
function is_unique_visit($ip_address, $date) {
    global $conn;

    $sql = "SELECT COUNT(*) as count FROM visits
            WHERE ip_address = ? AND visit_date = ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return true; // Assume unique if error
    }

    $stmt->bind_param("ss", $ip_address, $date);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        error_log("Error checking unique visit: " . $stmt->error);
        $stmt->close();
        return true; // Assume unique if error
    }

    $row = $result->fetch_assoc();
    $count = $row['count'];

    $stmt->close();

    return ($count == 0);
}

/**
 * Update the visits summary table
 *
 * @param string $date The date to update
 * @param bool $is_unique Whether this is a unique visit
 * @return bool True if the summary was updated successfully, false otherwise
 */
function update_visits_summary($date, $is_unique) {
    global $conn;

    // Check if there's already a record for this date
    $sql = "SELECT id FROM visits_summary WHERE visit_date = ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return false;
    }

    $stmt->bind_param("s", $date);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Update existing record
        $sql = "UPDATE visits_summary
                SET total_visits = total_visits + 1" .
                ($is_unique ? ", unique_visits = unique_visits + 1" : "") .
                " WHERE visit_date = ?";

        $stmt->close();
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . $conn->error);
            return false;
        }

        $stmt->bind_param("s", $date);
    } else {
        // Insert new record
        $sql = "INSERT INTO visits_summary (visit_date, total_visits, unique_visits)
                VALUES (?, 1, ?)";

        $stmt->close();
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . $conn->error);
            return false;
        }

        $unique_count = $is_unique ? 1 : 0;
        $stmt->bind_param("si", $date, $unique_count);
    }

    $result = $stmt->execute();

    if (!$result) {
        error_log("Error updating visits summary: " . $stmt->error);
        $stmt->close();
        return false;
    }

    $stmt->close();
    return true;
}

/**
 * Get the visitor's IP address
 *
 * @return string The visitor's IP address
 */
function get_ip_address() {
    // Check for shared internet/ISP IP
    if (!empty($_SERVER['HTTP_CLIENT_IP']) && validate_ip($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    }

    // Check for IPs passing through proxies
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // Check if multiple IPs exist in HTTP_X_FORWARDED_FOR
        $iplist = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        foreach ($iplist as $ip) {
            if (validate_ip(trim($ip))) {
                return trim($ip);
            }
        }
    }

    if (!empty($_SERVER['HTTP_X_FORWARDED']) && validate_ip($_SERVER['HTTP_X_FORWARDED'])) {
        return $_SERVER['HTTP_X_FORWARDED'];
    }

    if (!empty($_SERVER['HTTP_X_CLUSTER_CLIENT_IP']) && validate_ip($_SERVER['HTTP_X_CLUSTER_CLIENT_IP'])) {
        return $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
    }

    if (!empty($_SERVER['HTTP_FORWARDED_FOR']) && validate_ip($_SERVER['HTTP_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_FORWARDED_FOR'];
    }

    if (!empty($_SERVER['HTTP_FORWARDED']) && validate_ip($_SERVER['HTTP_FORWARDED'])) {
        return $_SERVER['HTTP_FORWARDED'];
    }

    // Return remote address if nothing else works
    return $_SERVER['REMOTE_ADDR'];
}

/**
 * Validate an IP address
 *
 * @param string $ip The IP address to validate
 * @return bool True if the IP is valid, false otherwise
 */
function validate_ip($ip) {
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4 | FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
        return false;
    }
    return true;
}

/**
 * Get daily visits statistics
 *
 * @param int $days Number of days to retrieve (default: 7)
 * @return array Array of daily visits statistics
 */
function get_daily_visits($days = 7) {
    global $conn;

    $end_date = date('Y-m-d');
    $start_date = date('Y-m-d', strtotime("-" . ($days - 1) . " days"));

    $sql = "SELECT visit_date, total_visits, unique_visits
            FROM visits_summary
            WHERE visit_date BETWEEN ? AND ?
            ORDER BY visit_date DESC";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return [];
    }

    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        error_log("Error getting daily visits: " . $stmt->error);
        $stmt->close();
        return [];
    }

    $daily_visits = [];

    // Initialize array with all dates in range
    $current = strtotime($start_date);
    $end = strtotime($end_date);

    while ($current <= $end) {
        $date = date('Y-m-d', $current);
        $daily_visits[$date] = [
            'date' => $date,
            'formatted_date' => date('d M', $current),
            'total_visits' => 0,
            'unique_visits' => 0
        ];
        $current = strtotime('+1 day', $current);
    }

    // Fill in actual data
    while ($row = $result->fetch_assoc()) {
        $date = $row['visit_date'];
        if (isset($daily_visits[$date])) {
            $daily_visits[$date]['total_visits'] = (int)$row['total_visits'];
            $daily_visits[$date]['unique_visits'] = (int)$row['unique_visits'];
        }
    }

    $stmt->close();

    // Convert to indexed array
    return array_values($daily_visits);
}

/**
 * Get monthly visits statistics
 *
 * @param int $months Number of months to retrieve (default: 12)
 * @return array Array of monthly visits statistics
 */
function get_monthly_visits($months = 12) {
    global $conn;

    $end_date = date('Y-m-d');
    $start_date = date('Y-m-d', strtotime("-" . $months . " months"));

    $sql = "SELECT
                DATE_FORMAT(visit_date, '%Y-%m') as month,
                SUM(total_visits) as total_visits,
                SUM(unique_visits) as unique_visits
            FROM visits_summary
            WHERE visit_date BETWEEN ? AND ?
            GROUP BY DATE_FORMAT(visit_date, '%Y-%m')
            ORDER BY month DESC";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return [];
    }

    $stmt->bind_param("ss", $start_date, $end_date);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        error_log("Error getting monthly visits: " . $stmt->error);
        $stmt->close();
        return [];
    }

    $monthly_visits = [];

    // Initialize array with all months in range
    $current = strtotime(date('Y-m-01', strtotime($start_date)));
    $end = strtotime(date('Y-m-01', strtotime($end_date)));

    while ($current <= $end) {
        $month_key = date('Y-m', $current);
        $monthly_visits[$month_key] = [
            'month' => $month_key,
            'formatted_month' => date('M Y', $current),
            'total_visits' => 0,
            'unique_visits' => 0
        ];
        $current = strtotime('+1 month', $current);
    }

    // Fill in actual data
    while ($row = $result->fetch_assoc()) {
        $month = $row['month'];
        if (isset($monthly_visits[$month])) {
            $monthly_visits[$month]['total_visits'] = (int)$row['total_visits'];
            $monthly_visits[$month]['unique_visits'] = (int)$row['unique_visits'];
        }
    }

    $stmt->close();

    // Convert to indexed array
    return array_values($monthly_visits);
}

/**
 * Get total visits statistics
 *
 * @return array Array of total visits statistics
 */
function get_total_visits() {
    global $conn;

    $sql = "SELECT
                SUM(total_visits) as total_visits,
                SUM(unique_visits) as unique_visits
            FROM visits_summary";

    $result = $conn->query($sql);

    if (!$result) {
        error_log("Error getting total visits: " . $conn->error);
        return [
            'total_visits' => 0,
            'unique_visits' => 0
        ];
    }

    $row = $result->fetch_assoc();

    return [
        'total_visits' => (int)$row['total_visits'],
        'unique_visits' => (int)$row['unique_visits']
    ];
}

/**
 * Get most visited pages
 *
 * @param int $limit Number of pages to retrieve (default: 5)
 * @return array Array of most visited pages
 */
function get_most_visited_pages($limit = 5) {
    global $conn;

    $sql = "SELECT
                page,
                COUNT(*) as visit_count
            FROM visits
            GROUP BY page
            ORDER BY visit_count DESC
            LIMIT ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return [];
    }

    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        error_log("Error getting most visited pages: " . $stmt->error);
        $stmt->close();
        return [];
    }

    $pages = [];

    while ($row = $result->fetch_assoc()) {
        $pages[] = [
            'page' => $row['page'],
            'visit_count' => (int)$row['visit_count']
        ];
    }

    $stmt->close();

    return $pages;
}

/**
 * Get geographic information based on IP address
 *
 * @param string $ip_address The IP address to look up
 * @return array Array containing country, city, and region information
 */
function get_geo_info($ip_address) {
    // Skip for localhost or private IPs
    if ($ip_address == '127.0.0.1' || $ip_address == '::1' || !filter_var($ip_address, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
        return [
            'country' => 'Local',
            'city' => 'Local',
            'region' => 'Local',
            'country_code' => 'LO'
        ];
    }

    // Try to use IP-API.com (free for non-commercial use)
    $url = "http://ip-api.com/json/{$ip_address}?fields=status,country,countryCode,regionName,city";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3);
    $response = curl_exec($ch);
    curl_close($ch);

    if ($response) {
        $data = json_decode($response, true);
        if (isset($data['status']) && $data['status'] == 'success') {
            return [
                'country' => $data['country'],
                'city' => $data['city'],
                'region' => $data['regionName'],
                'country_code' => $data['countryCode']
            ];
        }
    }

    // Fallback to ipinfo.io if IP-API fails
    $url = "https://ipinfo.io/{$ip_address}/json";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3);
    $response = curl_exec($ch);
    curl_close($ch);

    if ($response) {
        $data = json_decode($response, true);
        if (isset($data['country'])) {
            return [
                'country' => isset($data['country']) ? $data['country'] : null,
                'city' => isset($data['city']) ? $data['city'] : null,
                'region' => isset($data['region']) ? $data['region'] : null,
                'country_code' => isset($data['country']) ? $data['country'] : null
            ];
        }
    }

    // Return empty data if both services fail
    return [
        'country' => null,
        'city' => null,
        'region' => null,
        'country_code' => null
    ];
}

/**
 * Get device information from user agent string
 *
 * @param string $user_agent The user agent string
 * @return array Array containing device type, browser, and OS information
 */
function get_device_info($user_agent) {
    $device_type = 'Unknown';
    $browser = 'Unknown';
    $os = 'Unknown';

    // Detect device type
    if (preg_match('/(tablet|ipad|playbook|silk)|(android(?!.*mobile))/i', $user_agent)) {
        $device_type = 'Tablet';
    } else if (preg_match('/Mobile|Android|iP(hone|od)|IEMobile|BlackBerry|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/', $user_agent)) {
        $device_type = 'Mobile';
    } else {
        $device_type = 'Desktop';
    }

    // Detect browser
    if (preg_match('/MSIE|Trident/i', $user_agent)) {
        $browser = 'Internet Explorer';
    } else if (preg_match('/Firefox/i', $user_agent)) {
        $browser = 'Firefox';
    } else if (preg_match('/Chrome/i', $user_agent)) {
        if (preg_match('/Edge/i', $user_agent)) {
            $browser = 'Edge';
        } else if (preg_match('/Edg/i', $user_agent)) {
            $browser = 'Edge';
        } else if (preg_match('/OPR/i', $user_agent)) {
            $browser = 'Opera';
        } else {
            $browser = 'Chrome';
        }
    } else if (preg_match('/Safari/i', $user_agent)) {
        $browser = 'Safari';
    } else if (preg_match('/Opera|OPR/i', $user_agent)) {
        $browser = 'Opera';
    }

    // Detect OS
    if (preg_match('/Windows NT 10.0/i', $user_agent)) {
        $os = 'Windows 10';
    } else if (preg_match('/Windows NT 6.3/i', $user_agent)) {
        $os = 'Windows 8.1';
    } else if (preg_match('/Windows NT 6.2/i', $user_agent)) {
        $os = 'Windows 8';
    } else if (preg_match('/Windows NT 6.1/i', $user_agent)) {
        $os = 'Windows 7';
    } else if (preg_match('/Windows NT 6.0/i', $user_agent)) {
        $os = 'Windows Vista';
    } else if (preg_match('/Windows NT 5.1/i', $user_agent)) {
        $os = 'Windows XP';
    } else if (preg_match('/Windows NT/i', $user_agent)) {
        $os = 'Windows';
    } else if (preg_match('/Mac OS X/i', $user_agent)) {
        if (preg_match('/iPhone|iPad|iPod/i', $user_agent)) {
            $os = 'iOS';
        } else {
            $os = 'Mac OS X';
        }
    } else if (preg_match('/Android/i', $user_agent)) {
        $os = 'Android';
    } else if (preg_match('/Linux/i', $user_agent)) {
        $os = 'Linux';
    }

    return [
        'device_type' => $device_type,
        'browser' => $browser,
        'os' => $os
    ];
}

/**
 * Update geographic statistics
 *
 * @param string $country The country name
 * @param string $city The city name
 * @param string $region The region name
 * @param bool $is_unique Whether this is a unique visit
 * @return bool True if the statistics were updated successfully, false otherwise
 */
function update_geo_stats($country, $city, $region, $is_unique) {
    global $conn;

    // Extract country code from country name (simplified)
    $country_code = substr($country, 0, 2);

    // Check if there's already a record for this country and city
    $sql = "SELECT id FROM visits_geo_stats WHERE country = ? AND city = ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return false;
    }

    $stmt->bind_param("ss", $country, $city);
    $stmt->execute();
    $result = $stmt->get_result();

    $current_datetime = date('Y-m-d H:i:s');

    if ($result->num_rows > 0) {
        // Update existing record
        $sql = "UPDATE visits_geo_stats
                SET visits_count = visits_count + 1" .
                ($is_unique ? ", unique_visits = unique_visits + 1" : "") .
                ", last_updated = ?
                WHERE country = ? AND city = ?";

        $stmt->close();
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . $conn->error);
            return false;
        }

        $stmt->bind_param("sss", $current_datetime, $country, $city);
    } else {
        // Insert new record
        $sql = "INSERT INTO visits_geo_stats (country, country_code, city, region, visits_count, unique_visits, last_updated)
                VALUES (?, ?, ?, ?, 1, ?, ?)";

        $stmt->close();
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . $conn->error);
            return false;
        }

        $unique_count = $is_unique ? 1 : 0;
        $stmt->bind_param("ssssss", $country, $country_code, $city, $region, $unique_count, $current_datetime);
    }

    $result = $stmt->execute();

    if (!$result) {
        error_log("Error updating geo stats: " . $stmt->error);
        $stmt->close();
        return false;
    }

    $stmt->close();
    return true;
}

/**
 * Update device statistics
 *
 * @param string $device_type The device type
 * @param string $browser The browser name
 * @param string $os The operating system
 * @param bool $is_unique Whether this is a unique visit
 * @return bool True if the statistics were updated successfully, false otherwise
 */
function update_device_stats($device_type, $browser, $os, $is_unique) {
    global $conn;

    // Check if there's already a record for this device, browser, and OS
    $sql = "SELECT id FROM visits_device_stats WHERE device_type = ? AND browser = ? AND os = ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return false;
    }

    $stmt->bind_param("sss", $device_type, $browser, $os);
    $stmt->execute();
    $result = $stmt->get_result();

    $current_datetime = date('Y-m-d H:i:s');

    if ($result->num_rows > 0) {
        // Update existing record
        $sql = "UPDATE visits_device_stats
                SET visits_count = visits_count + 1" .
                ($is_unique ? ", unique_visits = unique_visits + 1" : "") .
                ", last_updated = ?
                WHERE device_type = ? AND browser = ? AND os = ?";

        $stmt->close();
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . $conn->error);
            return false;
        }

        $stmt->bind_param("ssss", $current_datetime, $device_type, $browser, $os);
    } else {
        // Insert new record
        $sql = "INSERT INTO visits_device_stats (device_type, browser, os, visits_count, unique_visits, last_updated)
                VALUES (?, ?, ?, 1, ?, ?)";

        $stmt->close();
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . $conn->error);
            return false;
        }

        $unique_count = $is_unique ? 1 : 0;
        $stmt->bind_param("sssss", $device_type, $browser, $os, $unique_count, $current_datetime);
    }

    $result = $stmt->execute();

    if (!$result) {
        error_log("Error updating device stats: " . $stmt->error);
        $stmt->close();
        return false;
    }

    $stmt->close();
    return true;
}

/**
 * Update referrer statistics
 *
 * @param string $referrer The referrer URL
 * @param bool $is_unique Whether this is a unique visit
 * @return bool True if the statistics were updated successfully, false otherwise
 */
function update_referrer_stats($referrer, $is_unique) {
    global $conn;

    // Extract domain from referrer URL
    $referrer_domain = parse_url($referrer, PHP_URL_HOST);
    if (!$referrer_domain) {
        $referrer_domain = $referrer;
    }

    // Check if there's already a record for this referrer domain
    $sql = "SELECT id FROM visits_referrer_stats WHERE referrer_domain = ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return false;
    }

    $stmt->bind_param("s", $referrer_domain);
    $stmt->execute();
    $result = $stmt->get_result();

    $current_datetime = date('Y-m-d H:i:s');

    if ($result->num_rows > 0) {
        // Update existing record
        $sql = "UPDATE visits_referrer_stats
                SET visits_count = visits_count + 1" .
                ($is_unique ? ", unique_visits = unique_visits + 1" : "") .
                ", last_updated = ?
                WHERE referrer_domain = ?";

        $stmt->close();
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . $conn->error);
            return false;
        }

        $stmt->bind_param("ss", $current_datetime, $referrer_domain);
    } else {
        // Insert new record
        $sql = "INSERT INTO visits_referrer_stats (referrer_domain, referrer_url, visits_count, unique_visits, last_updated)
                VALUES (?, ?, 1, ?, ?)";

        $stmt->close();
        $stmt = $conn->prepare($sql);
        if (!$stmt) {
            error_log("Error preparing statement: " . $conn->error);
            return false;
        }

        $unique_count = $is_unique ? 1 : 0;
        $stmt->bind_param("ssss", $referrer_domain, $referrer, $unique_count, $current_datetime);
    }

    $result = $stmt->execute();

    if (!$result) {
        error_log("Error updating referrer stats: " . $stmt->error);
        $stmt->close();
        return false;
    }

    $stmt->close();
    return true;
}

/**
 * Add JavaScript to update screen resolution
 *
 * @param int $visit_id The ID of the visit record
 */
function add_screen_resolution_script($visit_id) {
    echo "
    <script>
    (function() {
        // Don't run in admin area
        if (window.location.href.indexOf('/admin/') !== -1) return;

        // Get screen resolution
        var width = window.screen.width;
        var height = window.screen.height;
        var resolution = width + 'x' + height;

        // Send resolution to server
        var xhr = new XMLHttpRequest();
        xhr.open('POST', 'update_resolution.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        xhr.send('visit_id=" . $visit_id . "&resolution=' + resolution);
    })();
    </script>
    ";
}

/**
 * Get country statistics
 *
 * @param int $limit Number of countries to retrieve (default: 10)
 * @return array Array of country statistics
 */
function get_country_stats($limit = 10) {
    global $conn;

    $sql = "SELECT
                country,
                country_code,
                SUM(visits_count) as total_visits,
                SUM(unique_visits) as unique_visits
            FROM visits_geo_stats
            GROUP BY country, country_code
            ORDER BY total_visits DESC
            LIMIT ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return [];
    }

    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        error_log("Error getting country stats: " . $stmt->error);
        $stmt->close();
        return [];
    }

    $countries = [];

    while ($row = $result->fetch_assoc()) {
        $countries[] = [
            'country' => $row['country'],
            'country_code' => $row['country_code'],
            'total_visits' => (int)$row['total_visits'],
            'unique_visits' => (int)$row['unique_visits']
        ];
    }

    $stmt->close();

    return $countries;
}

/**
 * Get city statistics
 *
 * @param int $limit Number of cities to retrieve (default: 10)
 * @return array Array of city statistics
 */
function get_city_stats($limit = 10) {
    global $conn;

    $sql = "SELECT
                city,
                country,
                region,
                SUM(visits_count) as total_visits,
                SUM(unique_visits) as unique_visits
            FROM visits_geo_stats
            GROUP BY city, country, region
            ORDER BY total_visits DESC
            LIMIT ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return [];
    }

    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        error_log("Error getting city stats: " . $stmt->error);
        $stmt->close();
        return [];
    }

    $cities = [];

    while ($row = $result->fetch_assoc()) {
        $cities[] = [
            'city' => $row['city'],
            'country' => $row['country'],
            'region' => $row['region'],
            'total_visits' => (int)$row['total_visits'],
            'unique_visits' => (int)$row['unique_visits']
        ];
    }

    $stmt->close();

    return $cities;
}

/**
 * Get device statistics
 *
 * @return array Array of device statistics
 */
function get_device_stats() {
    global $conn;

    $sql = "SELECT
                device_type,
                SUM(visits_count) as total_visits,
                SUM(unique_visits) as unique_visits
            FROM visits_device_stats
            GROUP BY device_type
            ORDER BY total_visits DESC";

    $result = $conn->query($sql);

    if (!$result) {
        error_log("Error getting device stats: " . $conn->error);
        return [];
    }

    $devices = [];

    while ($row = $result->fetch_assoc()) {
        $devices[] = [
            'device_type' => $row['device_type'],
            'total_visits' => (int)$row['total_visits'],
            'unique_visits' => (int)$row['unique_visits']
        ];
    }

    return $devices;
}

/**
 * Get browser statistics
 *
 * @return array Array of browser statistics
 */
function get_browser_stats() {
    global $conn;

    $sql = "SELECT
                browser,
                SUM(visits_count) as total_visits,
                SUM(unique_visits) as unique_visits
            FROM visits_device_stats
            GROUP BY browser
            ORDER BY total_visits DESC";

    $result = $conn->query($sql);

    if (!$result) {
        error_log("Error getting browser stats: " . $conn->error);
        return [];
    }

    $browsers = [];

    while ($row = $result->fetch_assoc()) {
        $browsers[] = [
            'browser' => $row['browser'],
            'total_visits' => (int)$row['total_visits'],
            'unique_visits' => (int)$row['unique_visits']
        ];
    }

    return $browsers;
}

/**
 * Get OS statistics
 *
 * @return array Array of OS statistics
 */
function get_os_stats() {
    global $conn;

    $sql = "SELECT
                os,
                SUM(visits_count) as total_visits,
                SUM(unique_visits) as unique_visits
            FROM visits_device_stats
            GROUP BY os
            ORDER BY total_visits DESC";

    $result = $conn->query($sql);

    if (!$result) {
        error_log("Error getting OS stats: " . $conn->error);
        return [];
    }

    $os_stats = [];

    while ($row = $result->fetch_assoc()) {
        $os_stats[] = [
            'os' => $row['os'],
            'total_visits' => (int)$row['total_visits'],
            'unique_visits' => (int)$row['unique_visits']
        ];
    }

    return $os_stats;
}

/**
 * Get referrer statistics
 *
 * @param int $limit Number of referrers to retrieve (default: 10)
 * @return array Array of referrer statistics
 */
function get_referrer_stats($limit = 10) {
    global $conn;

    $sql = "SELECT
                referrer_domain,
                SUM(visits_count) as total_visits,
                SUM(unique_visits) as unique_visits
            FROM visits_referrer_stats
            GROUP BY referrer_domain
            ORDER BY total_visits DESC
            LIMIT ?";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Error preparing statement: " . $conn->error);
        return [];
    }

    $stmt->bind_param("i", $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result) {
        error_log("Error getting referrer stats: " . $stmt->error);
        $stmt->close();
        return [];
    }

    $referrers = [];

    while ($row = $result->fetch_assoc()) {
        $referrers[] = [
            'referrer' => $row['referrer_domain'],
            'total_visits' => (int)$row['total_visits'],
            'unique_visits' => (int)$row['unique_visits']
        ];
    }

    $stmt->close();

    return $referrers;
}
