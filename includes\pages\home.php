<?php

/**
 * Homepage for the Social Media Festival website
 */

// Get content from database
$title = get_content('home', 'title', 'Social Media Festival');
$subtitle = get_content('home', 'subtitle', 'Celebrating Digital Excellence');
$intro_text = get_content('home', 'intro_text', 'Join us for the most prestigious digital awards event of the year.');
$vote_button = get_content('home', 'vote_button', 'Vote Now');

// Get categories

// Get sponsors
$platinum_sponsors = get_sponsors('platinum');
$gold_sponsors = get_sponsors('gold');
$silver_sponsors = get_sponsors('silver');

// Get reels
$reels = get_reels();

// Get all featured celebrities
$featured_celebrities = get_featured_celebrities(0);

// Get featured podcasts
$featured_podcasts = get_featured_podcasts(6);

// Get gallery images
$gallery_images = get_gallery_images(null, false, 12);
?>

<!-- Hero Section with Video Background -->
<section class="hero-section">
    <div class="video-container">
        <video autoplay muted loop playsinline id="hero-video">
            <source src="<?php echo asset_url('videos/desktop.mp4'); ?>" type="video/mp4" media="(min-width: 768px)">
            <source src="<?php echo asset_url('videos/mobile.mp4'); ?>" type="video/mp4" media="(max-width: 767px)">
        </video>
        <div class="video-overlay"></div>
    </div>

    <div class="hero-content">
        <h1 class="hero-title"><?php echo $title; ?></h1>
        <h2 class="hero-subtitle"><?php echo $subtitle; ?></h2>
        <p class="hero-text"><?php echo $intro_text; ?></p>
        <a href="<?php echo base_url('?page=vote'); ?>" class="btn btn-primary btn-vote-hero">
            <?php echo $vote_button; ?>
            <span class="btn-3d-effect"></span>
        </a>
    </div>
</section>

<!-- Festival Highlights Section -->
<section class="stories-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('stories', 'title', 'Festival Highlights'); ?></h2>
            <p><?php echo get_content('stories', 'subtitle', 'Memorable moments'); ?></p>
        </div>

        <!-- Swiper Slider for Stories -->
        <div class="swiper stories-swiper">
            <div class="swiper-wrapper">
                <?php foreach ($reels as $reel): ?>
                <div class="swiper-slide">
                    <div class="story-item">
                        <div class="story-video">
                            <video poster="<?php echo asset_url('uploads/' . $reel['thumbnail']); ?>" preload="metadata"
                                data-reel-id="<?php echo $reel['id']; ?>" controlsList="nodownload">
                                <source src="<?php echo asset_url('uploads/' . $reel['video']); ?>" type="video/mp4">
                            </video>
                            <div class="play-button" data-reel-id="<?php echo $reel['id']; ?>">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="story-info">
                            <h3><?php echo get_reel_title($reel); ?></h3>
                            <p><?php echo $current_lang === 'ar' ? $reel['description_ar'] : $reel['description']; ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Festival Podcasts Section -->
<section class="podcasts-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('podcasts', 'title', 'Festival Podcasts'); ?></h2>
            <p><?php echo get_content('podcasts', 'subtitle', 'Exclusive Conversations & Insights'); ?></p>
        </div>

        <!-- Swiper Slider for Podcasts -->
        <div class="swiper podcasts-swiper">
            <div class="swiper-wrapper">
                <?php foreach ($featured_podcasts as $podcast): ?>
                <div class="swiper-slide">
                    <div class="podcast-item">
                        <div class="podcast-video">
                            <?php if (!empty($podcast['audio_file'])): ?>
                            <video poster="<?php echo !empty($podcast['thumbnail']) ? asset_url('uploads/' . $podcast['thumbnail']) : asset_url('img/podcast-default.jpg'); ?>"
                                   preload="metadata"
                                   data-podcast-id="<?php echo $podcast['id']; ?>"
                                   controlsList="nodownload">
                                <source src="<?php echo asset_url('uploads/' . $podcast['audio_file']); ?>" type="video/mp4">
                            </video>
                            <?php else: ?>
                            <div class="default-video">
                                <i class="fas fa-microphone"></i>
                            </div>
                            <?php endif; ?>
                            <div class="play-button" data-podcast-id="<?php echo $podcast['id']; ?>">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="podcast-badge">
                                <span>بودكاست</span>
                            </div>
                        </div>
                        <div class="podcast-info">
                            <h3><?php echo get_podcast_title($podcast); ?></h3>
                            <div class="podcast-meta">
                                <span class="podcast-date"><?php echo date('Y/m/d', strtotime($podcast['created_at'])); ?></span>
                                <span class="podcast-type">بودكاست</span>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Featured Celebrities Section -->
<section class="celebrities-section" id="celebrities-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('celebrities', 'title', 'Festival Stars'); ?></h2>
            <p><?php echo get_content('celebrities', 'subtitle', 'Prominent Attendees'); ?></p>
        </div>

        <!-- Swiper Slider for Celebrities -->
        <div class="swiper celebrities-swiper">
            <div class="swiper-wrapper">
                <?php foreach ($featured_celebrities as $celebrity): ?>
                <div class="swiper-slide" style="margin-left:5px !important;">
                    <div class="celebrity-item">
                        <div class="celebrity-image">
                            <img src="<?php echo asset_url('uploads/' . $celebrity['image']); ?>"
                                alt="<?php echo get_celebrity_name($celebrity); ?>">
                        </div>
                        <div class="celebrity-info">
                            <h3><?php echo get_celebrity_name($celebrity); ?></h3>
                            <p><?php echo get_celebrity_role($celebrity); ?></p>
                        </div>
                        <div class="celebrity-social">
                            <?php if (!empty($celebrity['facebook'])): ?>
                            <a href="<?php echo htmlspecialchars($celebrity['facebook']); ?>" class="social-icon"
                                title="Facebook" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($celebrity['twitter'])): ?>
                            <a href="<?php echo htmlspecialchars($celebrity['twitter']); ?>" class="social-icon"
                                title="Twitter" target="_blank">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($celebrity['instagram'])): ?>
                            <a href="<?php echo htmlspecialchars($celebrity['instagram']); ?>" class="social-icon"
                                title="Instagram" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($celebrity['youtube'])): ?>
                            <a href="<?php echo htmlspecialchars($celebrity['youtube']); ?>" class="social-icon"
                                title="YouTube" target="_blank">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Most Influential Bloggers Section -->
<section class="influencers-section" id="influencers-section">
    <!-- Background Elements -->
    <div class="section-bg-elements">
        <div class="floating-icon icon-1"><i class="fab fa-instagram"></i></div>
        <div class="floating-icon icon-2"><i class="fab fa-youtube"></i></div>
        <div class="floating-icon icon-3"><i class="fab fa-tiktok"></i></div>
        <div class="floating-icon icon-4"><i class="fab fa-linkedin"></i></div>
        <div class="floating-icon icon-5"><i class="fas fa-star"></i></div>
        <div class="floating-icon icon-6"><i class="fas fa-crown"></i></div>
    </div>
    <style>
    @media (max-width: 576px) {
        .swiper-slide {
            padding: 0 2px !important;
            /* تقليل المسافة الجانبية أكثر */
        }

        .influencer-item {
            width: 100% !important;
            margin: 0 auto !important;
            padding: 0 !important;
            /* إزالة أي padding داخلي */
        }
    }
    </style>
    <div class="container">
        <!-- Enhanced Section Header -->
        <div class="section-header text-center">
            <div class="header-decoration">
                <div class="decoration-line"></div>
                <div class="decoration-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="decoration-line"></div>
            </div>

            <h2 class="section-title">
                <span
                    class="title-highlight"><?php echo get_content('home', 'influencers_section_title', 'Most Influential Bloggers'); ?></span>
            </h2>
            <p class="section-subtitle">
                <span class="subtitle-icon"><i class="fas fa-users"></i></span>
                <?php echo get_content('home', 'influencers_section_subtitle', 'Digital Leaders & Content Creators'); ?>
            </p>

            <?php
            // Get featured influencers for stats
            $featured_influencers = get_featured_influencers();
            if (!empty($featured_influencers)):
            ?>
            <div class="stats-bar">
                <div class="stat-item">
                    <span class="stat-number"><?php echo count($featured_influencers); ?></span>
                    <span
                        class="stat-label"><?php echo get_content('home', 'influencers_stat_bloggers', 'Influential Bloggers'); ?></span>
                </div>
                <div class="stat-divider"></div>
                <div class="stat-item">
                    <span class="stat-number"><?php
                                                    $total_followers = array_sum(array_column($featured_influencers, 'followers_count'));
                                                    echo format_followers_count($total_followers);
                                                    ?></span>
                    <span
                        class="stat-label"><?php echo get_content('home', 'influencers_stat_followers', 'Total Followers'); ?></span>
                </div>
                <div class="stat-divider"></div>
                <div class="stat-item">
                    <span class="stat-number"><?php
                                                    $avg_score = array_sum(array_column($featured_influencers, 'influence_score')) / count($featured_influencers);
                                                    echo number_format($avg_score, 1);
                                                    ?></span>
                    <span
                        class="stat-label"><?php echo get_content('home', 'influencers_stat_avg_score', 'Average Score'); ?></span>
                </div>
            </div>
        </div>
        <style>
        @media (max-width: 576px) {
            .swiper-wrapper {
                gap: 8px !important;
                /* تقليل الفراغ بين الكروت */
            }

            .influencer-image {
                margin-top: 20px;
            }

            .influencer-social {
                position: relative;
                bottom: 20px;
            }

            .swiper-slide {
                display: flex !important;
                justify-content: center !important;
                padding: 0 !important;
                margin: 0 !important;
                width: auto !important;
            }

            .influencer-item {
                width: 300px !important;
                margin: 0 !important;
            }
        }

        .influencer-item {
            position: relative;
            top: 0 !important;
            transform: none !important;
            box-shadow: none !important;
            transition: none !important;
        }
        </style>
        <!-- Enhanced Swiper -->
        <div class="swiper influencers-swiper">
            <div class="swiper-wrapper">
                <?php foreach ($featured_influencers as $index => $influencer): ?>
                <div class="swiper-slide" style="position: relative; left: 0px;">
                    <div class="influencer-item" style="width: 300px;">
                        <!-- Rank Badge -->
                        <div class="rank-badge" style="display: none;">
                            <span class="rank-number">#<?php echo $index + 1; ?></span>
                        </div>

                        <div class="influencer-image" style="border-radius:0px !important;width: 200px;height: 200px;">
                            <?php if (!empty($influencer['image'])): ?>
                            <img src="<?php echo asset_url('img/influencers/' . $influencer['image']); ?>"
                                alt="<?php echo get_influencer_name($influencer); ?>"
                                onerror="this.src='assets/img/default-avatar.png'" style="width: 200px;height: 200px;">
                            <?php else: ?>
                            <div class="default-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <?php endif; ?>

                            <!-- Enhanced Influence Badge -->
                            <div class="influence-badge">
                                <div class="badge-inner">
                                    <span class="score"><?php echo $influencer['influence_score']; ?></span>
                                    <span class="label">SCORE</span>
                                </div>
                                <div class="badge-glow"></div>
                            </div>

                            <!-- Enhanced Platform Badge -->
                            <?php if (!empty($influencer['platform'])): ?>
                            <div class="platform-badge">
                                <?php
                                        $platform_icons = [
                                            'Instagram' => 'fab fa-instagram',
                                            'YouTube' => 'fab fa-youtube',
                                            'TikTok' => 'fab fa-tiktok',
                                            'LinkedIn' => 'fab fa-linkedin',
                                            'Facebook' => 'fab fa-facebook',
                                            'Twitter' => 'fab fa-twitter'
                                        ];
                                        $icon = $platform_icons[$influencer['platform']] ?? 'fas fa-globe';
                                        ?>
                                <i class="<?php echo $icon; ?>"></i>
                                <span class="platform-tooltip"><?php echo $influencer['platform']; ?></span>
                            </div>
                            <?php endif; ?>

                            <!-- Verification Badge -->
                            <div class="verification-badge">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>

                        <div class="influencer-info">
                            <h3><?php echo get_influencer_name($influencer); ?></h3>
                            <p class="influencer-title"><?php echo get_influencer_title($influencer); ?></p>

                            <div class="influence-metrics">
                                <div class="metric-item">
                                    <i class="fas fa-users"></i>
                                    <span><?php echo format_followers_count($influencer['followers_count']); ?></span>
                                    <small><?php echo get_content('influencers', 'followers_label', 'Followers'); ?></small>
                                </div>
                                <div class="metric-divider"></div>
                                <div class="metric-item">
                                    <i class="fas fa-star"></i>
                                    <span><?php echo $influencer['influence_score']; ?></span>
                                    <small><?php echo get_content('influencers', 'influence_score_label', 'Influence Score'); ?></small>
                                </div>
                            </div>

                            <!-- Bio Preview -->
                            <?php if (!empty($influencer['bio']) || !empty($influencer['bio_ar'])): ?>
                            <div class="bio-preview">
                                <p><?php echo substr(htmlspecialchars(get_influencer_bio($influencer)), 0, 80) . '...'; ?>
                                </p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Enhanced Social Links -->
                        <div class="influencer-social">
                            <?php if (!empty($influencer['facebook'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['facebook']); ?>" target="_blank"
                                class="social-icon facebook" data-tooltip="Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($influencer['twitter'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['twitter']); ?>" target="_blank"
                                class="social-icon twitter" data-tooltip="Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($influencer['instagram'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['instagram']); ?>" target="_blank"
                                class="social-icon instagram" data-tooltip="Instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($influencer['youtube'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['youtube']); ?>" target="_blank"
                                class="social-icon youtube" data-tooltip="YouTube">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($influencer['tiktok'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['tiktok']); ?>" target="_blank"
                                class="social-icon tiktok" data-tooltip="TikTok">
                                <i class="fab fa-tiktok"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($influencer['linkedin'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['linkedin']); ?>" target="_blank"
                                class="social-icon linkedin" data-tooltip="LinkedIn">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <?php endif; ?>

                            <?php if (!empty($influencer['website'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['website']); ?>" target="_blank"
                                class="social-icon website" data-tooltip="Website">
                                <i class="fas fa-globe"></i>
                            </a>
                            <?php endif; ?>
                            <a onclick="followInfluencer(<?php echo $influencer['id']; ?>)" target="_blank"
                                class="social-icon website" data-tooltip="follow">
                                <i class="fas fa-plus"></i>
                            </a>

                        </div>

                        <!-- Action Button -->
                        <div class="influencer-action" style="display: none;">
                            <button class="follow-btn" onclick="followInfluencer(<?php echo $influencer['id']; ?>)">
                                <i class="fas fa-plus"></i>
                                <span><?php echo get_content('influencers', 'follow_button', 'Follow'); ?></span>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Enhanced Navigation -->
            <div class="swiper-navigation" style="display: none;">
                <div class="swiper-button-prev">
                    <i class="fas fa-chevron-left"></i>
                </div>
                <div class="swiper-button-next">
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>

            <!-- Enhanced Pagination -->
            <div class="swiper-pagination" style="display: none;"></div>
        </div>

        <!-- View All Button -->
        <div class="section-footer text-center" style="display: none;">
            <a href="?page=influencers" class="view-all-btn">
                <span><?php echo get_content('home', 'view_all_influencers', 'View All Influencers'); ?></span>
                <i class="fas fa-arrow-<?php echo get_current_language() === 'ar' ? 'left' : 'right'; ?>"></i>
            </a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Award Categories Section -->
<section class="nominees-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('nominees', 'title', 'Award Categories'); ?></h2>
            <p><?php echo get_content('nominees', 'subtitle', 'Vote for your favorites'); ?></p>
        </div>
    </div>

    <!-- Swiper Slider for Categories -->
    <div class="swiper categories-swiper">
        <div class="swiper-wrapper">
            <?php
            // Get regular categories
            ?>
            <?php
            // Get voting categories
            $voting_categories = get_main_categories();
            foreach ($voting_categories as $category):
            ?>
            <div class="swiper-slide" style=" margin-right: 50px !important;">
                <div class="category-item voting-category">
                    <a href="<?php echo base_url('?page=vote&main_category=' . $category['id']); ?>"
                        class="category-link">
                        <div class="category-image">
                            <?php if (!empty($category['image'])): ?>
                            <img src="<?php echo asset_url('uploads/' . $category['image']); ?>"
                                alt="<?php echo get_main_category_name($category); ?>">
                            <?php else: ?>
                            <img src="<?php echo asset_url('img/logo.png'); ?>"
                                alt="<?php echo get_main_category_name($category); ?>">
                            <?php endif; ?>
                        </div>
                        <div class="category-info">
                            <h3><?php echo get_main_category_name($category); ?></h3>
                            <div class="vote-button">
                                <span><?php echo get_content('nominees', 'vote_now', 'Vote Now'); ?></span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="container">
        <div class="section-footer">
            <a href="<?php echo base_url('?page=vote'); ?>" class="btn btn-primary">
                <?php echo get_content('vote', 'vote_button', 'Vote Now'); ?>
            </a>
            <a href="<?php echo base_url('?page=nominees'); ?>" class="btn btn-outline">
                <?php echo get_content('nominees', 'view_all', 'View All Categories'); ?>
            </a>
        </div>
    </div>
</section>

<!-- CEO Section -->
<section class="ceo-section">
    <div class="container">
        <?php
        // Get CEO information
        $ceo = get_ceo_info();
        if ($ceo):
        ?>
        <div class="ceo-container">
            <div class="ceo-image">
                <img src="<?php echo asset_url('uploads/' . $ceo['image']); ?>" alt="<?php echo $ceo['name']; ?>">
            </div>
            <div class="ceo-info">
                <div class="ceo-title">
                    <h2><?php echo get_content('ceo', 'title', 'Festival CEO'); ?></h2>
                </div>
                <div class="ceo-name">
                    <h3><?php echo $ceo['name']; ?></h3>
                </div>
                <div class="ceo-position">
                    <p><?php echo $current_lang === 'ar' ? $ceo['position_ar'] : $ceo['position']; ?></p>
                </div>
                <div class="ceo-bio">
                    <p><?php echo $current_lang === 'ar' ? $ceo['bio_ar'] : $ceo['bio']; ?></p>
                </div>
                <div class="ceo-social">
                    <?php if (!empty($ceo['facebook'])): ?>
                    <a href="<?php echo htmlspecialchars($ceo['facebook']); ?>" class="social-icon" title="Facebook"
                        target="_blank">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($ceo['twitter'])): ?>
                    <a href="<?php echo htmlspecialchars($ceo['twitter']); ?>" class="social-icon" title="Twitter"
                        target="_blank">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($ceo['instagram'])): ?>
                    <a href="<?php echo htmlspecialchars($ceo['instagram']); ?>" class="social-icon" title="Instagram"
                        target="_blank">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($ceo['linkedin'])): ?>
                    <a href="<?php echo htmlspecialchars($ceo['linkedin']); ?>" class="social-icon" title="LinkedIn"
                        target="_blank">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Sponsors Section -->
<section class="sponsors-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('sponsors', 'title', 'Our Sponsors'); ?></h2>
            <p><?php echo get_content('sponsors', 'subtitle', 'Partners in Excellence'); ?></p>
        </div>

        <!-- Enhanced Sponsors Layout with Swiper -->
        <div class="sponsors-pyramid">

            <!-- Platinum Level (Top - all sponsors) -->
            <?php if (!empty($platinum_sponsors)): ?>
            <div class="pyramid-level platinum-level">
                <h3 class="level-title"><?php echo get_content('sponsors', 'platinum', 'Platinum Sponsors'); ?></h3>
                <div class="level-sponsors">
                    <div class="swiper platinum-sponsors-swiper">
                        <div class="swiper-wrapper">
                            <?php foreach ($platinum_sponsors as $sponsor): ?>
                            <div class="swiper-slide">
                                <div class="sponsor-item platinum">
                                    <a href="<?php echo !empty($sponsor['website']) ? $sponsor['website'] : '#'; ?>"
                                       target="_blank" rel="noopener" class="sponsor-link">
                                        <div class="sponsor-logo">
                                            <img src="<?php echo asset_url('uploads/' . $sponsor['logo']); ?>"
                                                 alt="<?php echo get_sponsor_name($sponsor); ?>"
                                                 onerror="this.src='<?php echo asset_url('img/default-sponsor.png'); ?>'">
                                        </div>
                                        <div class="sponsor-info">
                                            <h4><?php echo get_sponsor_name($sponsor); ?></h4>
                                            <span class="sponsor-level">Platinum Partner</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Gold Level (Middle - all sponsors) -->
            <?php if (!empty($gold_sponsors)): ?>
            <div class="pyramid-level gold-level">
                <h3 class="level-title"><?php echo get_content('sponsors', 'gold', 'Gold Sponsors'); ?></h3>
                <div class="level-sponsors">
                    <div class="swiper gold-sponsors-swiper">
                        <div class="swiper-wrapper">
                            <?php foreach ($gold_sponsors as $sponsor): ?>
                            <div class="swiper-slide">
                                <div class="sponsor-item gold">
                                    <a href="<?php echo !empty($sponsor['website']) ? $sponsor['website'] : '#'; ?>"
                                       target="_blank" rel="noopener" class="sponsor-link">
                                        <div class="sponsor-logo">
                                            <img src="<?php echo asset_url('uploads/' . $sponsor['logo']); ?>"
                                                 alt="<?php echo get_sponsor_name($sponsor); ?>"
                                                 onerror="this.src='<?php echo asset_url('img/default-sponsor.png'); ?>'">
                                        </div>
                                        <div class="sponsor-info">
                                            <h4><?php echo get_sponsor_name($sponsor); ?></h4>
                                            <span class="sponsor-level">Gold Partner</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Silver Level (Bottom - all sponsors) -->
            <?php if (!empty($silver_sponsors)): ?>
            <div class="pyramid-level silver-level">
                <h3 class="level-title"><?php echo get_content('sponsors', 'silver', 'Silver Sponsors'); ?></h3>
                <div class="level-sponsors">
                    <div class="swiper silver-sponsors-swiper">
                        <div class="swiper-wrapper">
                            <?php foreach ($silver_sponsors as $sponsor): ?>
                            <div class="swiper-slide">
                                <div class="sponsor-item silver">
                                    <a href="<?php echo !empty($sponsor['website']) ? $sponsor['website'] : '#'; ?>"
                                       target="_blank" rel="noopener" class="sponsor-link">
                                        <div class="sponsor-logo">
                                            <img src="<?php echo asset_url('uploads/' . $sponsor['logo']); ?>"
                                                 alt="<?php echo get_sponsor_name($sponsor); ?>"
                                                 onerror="this.src='<?php echo asset_url('img/default-sponsor.png'); ?>'">
                                        </div>
                                        <div class="sponsor-info">
                                            <h4><?php echo get_sponsor_name($sponsor); ?></h4>
                                            <span class="sponsor-level">Silver Partner</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div>

        <div class="section-footer">
            <a href="<?php echo base_url('?page=sponsors'); ?>" class="btn btn-outline">
                <?php echo get_content('sponsors', 'view_all', 'View All Sponsors'); ?>
            </a>
        </div>
    </div>
</section>


<!-- Media Coverage Section -->
<section class="media-coverage-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('media_coverage_title', 'content_value', 'Media Coverage'); ?></h2>
            <p><?php echo get_content('media_coverage_subtitle', 'content_value', 'See how the world\'s leading media outlets covered our festival'); ?></p>
        </div>

        <!-- Advertising Coverage -->
        <div class="coverage-subsection">
            <h3 class="subsection-title"><?php echo get_content('advertising_coverage_title', 'content_value', 'Advertising Coverage'); ?></h3>
            <div class="swiper advertising-swiper">
                <div class="swiper-wrapper">
                    <?php
                    $advertising_coverage = get_media_coverage('advertising');
                    foreach ($advertising_coverage as $coverage):
                    ?>
                    <div class="swiper-slide">
                        <div class="coverage-item">
                            <div class="coverage-image">
                                <img src="<?php echo asset_url('uploads/' . $coverage['image']); ?>"
                                     alt="<?php echo get_coverage_title($coverage); ?>"
                                     onerror="this.src='<?php echo asset_url('img/default-media.jpg'); ?>'">
                                <div class="coverage-overlay">
                                    <div class="coverage-info">
                                        <h4><?php echo get_coverage_title($coverage); ?></h4>
                                        <?php if (!empty($coverage['description']) || !empty($coverage['description_ar'])): ?>
                                        <p><?php echo get_coverage_description($coverage); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- TV Coverage -->
        <div class="coverage-subsection">
            <h3 class="subsection-title"><?php echo get_content('tv_coverage_title', 'content_value', 'Television Coverage'); ?></h3>
            <div class="swiper tv-swiper">
                <div class="swiper-wrapper">
                    <?php
                    $tv_coverage = get_media_coverage('tv');
                    foreach ($tv_coverage as $coverage):
                    ?>
                    <div class="swiper-slide">
                        <div class="coverage-item">
                            <div class="coverage-image">
                                <img src="<?php echo asset_url('uploads/' . $coverage['image']); ?>"
                                     alt="<?php echo get_coverage_title($coverage); ?>"
                                     onerror="this.src='<?php echo asset_url('img/default-media.jpg'); ?>'">
                                <div class="coverage-overlay">
                                    <div class="coverage-info">
                                        <h4><?php echo get_coverage_title($coverage); ?></h4>
                                        <?php if (!empty($coverage['description']) || !empty($coverage['description_ar'])): ?>
                                        <p><?php echo get_coverage_description($coverage); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Team Section -->
<section class="team-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('team', 'title', 'Our Team'); ?></h2>
            <p><?php echo get_content('team', 'subtitle', 'The People Behind the Festival'); ?></p>
        </div>

        <!-- Swiper Slider for Team -->
        <div class="swiper team-swiper">
            <div class="swiper-wrapper">
                <?php
                // Get team members
                $team_members = get_team_members();
                foreach ($team_members as $member):
                ?>
                <div class="swiper-slide">
                    <div class="team-member">
                        <div class="member-image">
                            <img src="<?php echo asset_url('uploads/' . $member['image']); ?>"
                                alt="<?php echo $member['name']; ?>">
                        </div>
                        <div class="member-info">
                            <h3><?php echo $member['name']; ?></h3>
                            <p class="member-position">
                                <?php echo $current_lang === 'ar' ? $member['position_ar'] : $member['position']; ?></p>
                            <div class="member-social">
                                <?php if (!empty($member['facebook'])): ?>
                                <a href="<?php echo htmlspecialchars($member['facebook']); ?>" class="social-icon"
                                    title="Facebook" target="_blank">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <?php endif; ?>

                                <?php if (!empty($member['twitter'])): ?>
                                <a href="<?php echo htmlspecialchars($member['twitter']); ?>" class="social-icon"
                                    title="Twitter" target="_blank">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <?php endif; ?>

                                <?php if (!empty($member['instagram'])): ?>
                                <a href="<?php echo htmlspecialchars($member['instagram']); ?>" class="social-icon"
                                    title="Instagram" target="_blank">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <?php endif; ?>

                                <?php if (!empty($member['linkedin'])): ?>
                                <a href="<?php echo htmlspecialchars($member['linkedin']); ?>" class="social-icon"
                                    title="LinkedIn" target="_blank">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Photo Gallery Section -->
<section class="gallery-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('gallery_title', 'content_value', 'Photo Gallery'); ?></h2>
            <p><?php echo get_content('gallery_subtitle', 'content_value', 'Capturing the best moments of our festival'); ?></p>
        </div>

        <!-- Gallery Grid with Swiper -->
        <div class="swiper gallery-swiper">
            <div class="swiper-wrapper">
                <?php foreach ($gallery_images as $image): ?>
                <div class="swiper-slide">
                    <div class="gallery-item" data-category="<?php echo $image['category']; ?>">
                        <div class="gallery-image">
                            <img data-src="<?php echo asset_url('uploads/' . $image['image']); ?>"
                                 src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='300' height='200'%3E%3Crect width='100%25' height='100%25' fill='%23f0f0f0'/%3E%3C/svg%3E"
                                 alt="<?php echo get_gallery_title($image); ?>"
                                 class="lazy-load gallery-lazy"
                                 onerror="this.src='<?php echo asset_url('img/default-gallery.jpg'); ?>'"
                                 data-lightbox="gallery"
                                 data-title="<?php echo get_gallery_title($image); ?>"
                                 data-description="<?php echo get_gallery_description($image); ?>">
                            <div class="gallery-overlay">
                                <div class="gallery-info">
                                    <h4><?php echo get_gallery_title($image); ?></h4>
                                    <p><?php echo get_gallery_description($image); ?></p>
                                    <span class="gallery-category"><?php echo get_gallery_category($image); ?></span>
                                </div>
                                <div class="gallery-actions">
                                    <button class="gallery-zoom" onclick="openLightbox('<?php echo asset_url('uploads/' . $image['image']); ?>', '<?php echo addslashes(get_gallery_title($image)); ?>', '<?php echo addslashes(get_gallery_description($image)); ?>')"
                                        <i class="fas fa-search-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- View All Button -->
        <div class="section-footer">
            <a href="<?php echo base_url('?page=gallery'); ?>" class="btn btn-outline">
                <?php echo get_content('gallery_view_all', 'content_value', 'View All Photos'); ?>
                <i class="fas fa-arrow-<?php echo get_current_language() === 'ar' ? 'left' : 'right'; ?>"></i>
            </a>
        </div>
    </div>
</section>

<!-- Invitations Section -->
<section class="invitations-section">
    <div class="container">
        <div class="section-header">
            <h2><?php echo get_content('invitations', 'title', 'Our Invitations'); ?></h2>
            <p><?php echo get_content('invitations', 'subtitle', 'Special Invitations for Our Distinguished Guests'); ?>
            </p>
        </div>

        <!-- Swiper Slider for Invitations -->
        <div class="swiper invitations-swiper">
            <div class="swiper-wrapper">
                <?php
                // Get invitations
                $invitations = get_invitations();
                foreach ($invitations as $invitation):
                ?>
                <div class="swiper-slide">
                    <div class="invitation-item"
                        data-image="<?php echo asset_url('uploads/' . $invitation['image']); ?>">
                        <div class="invitation-image">
                            <img src="<?php echo asset_url('uploads/' . $invitation['image']); ?>"
                                alt="<?php echo htmlspecialchars($invitation['title']); ?>">
                            <div class="invitation-overlay">
                                <div class="invitation-zoom">
                                    <i class="fas fa-search-plus"></i>
                                </div>
                            </div>
                        </div>
                        <div class="invitation-info">
                            <h3><?php echo htmlspecialchars($current_lang === 'ar' ? $invitation['title_ar'] : $invitation['title']); ?>
                            </h3>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<!-- Lightbox for Gallery -->
<div class="gallery-lightbox" id="galleryLightbox">
    <div class="lightbox-content">
        <span class="close-lightbox" onclick="closeLightbox()">&times;</span>
        <div class="lightbox-image-container">
            <img id="lightboxImage" src="" alt="">
            <div class="lightbox-navigation">
                <button class="lightbox-prev" onclick="previousImage()">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="lightbox-next" onclick="nextImage()">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
        <div class="lightbox-info">
            <h3 id="lightboxTitle"></h3>
            <p id="lightboxDescription"></p>
        </div>
    </div>
</div>

<!-- Lightbox for Invitations -->
<div class="invitation-lightbox">
    <div class="lightbox-content">
        <img src="" alt="Invitation">
        <span class="close-lightbox">&times;</span>
    </div>
</div>