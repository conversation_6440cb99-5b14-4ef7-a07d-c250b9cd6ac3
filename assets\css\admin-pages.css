/**
 * Enhanced CSS for Admin Pages
 * Includes styles for celebrities, stories, users, votes, and settings pages
 */

/* ===== Animations ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Ripple effect for buttons */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Modal animations */
.modal-animated {
    animation: fadeIn 0.3s ease-out;
}

.modal-content-animated {
    animation: slideInRight 0.3s ease-out;
}

/* ===== Common Page Styles ===== */
.page-title {
    position: relative;
    display: inline-block;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-sm);
    color: var(--color-gold);
    font-size: 2.2rem;
    font-weight: 700;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--color-gold), transparent);
    border-radius: 2px;
}

.action-buttons {
    margin-bottom: var(--spacing-lg);
    display: flex;
    justify-content: flex-start;
}

.action-buttons .btn {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.action-buttons .btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
}

.action-buttons .btn i {
    margin-right: var(--spacing-sm);
    font-size: 1.1rem;
}

.card {
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    border: 1px solid var(--color-gray);
    transition: all var(--transition-normal);
    margin-bottom: var(--spacing-xl);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    border-color: var(--color-gold);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.3);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--color-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin-bottom: 0;
    color: var(--color-gold);
    font-size: 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: var(--spacing-lg);
}

/* ===== Grid Layouts ===== */
.celebrities-grid,
.stories-grid,
.users-grid,
.categories-grid,
.reels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

/* ===== Card Items ===== */
.celebrity-card,
.story-card,
.user-card,
.category-card,
.reel-card {
    background-color: var(--color-dark-gray);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    border: 1px solid transparent;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.celebrity-card:hover,
.story-card:hover,
.user-card:hover,
.category-card:hover,
.reel-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
    border-color: var(--color-gold);
}

.category-card.main-category {
    border-left: 4px solid var(--color-gold);
}

.category-card.subcategory {
    border-left: 4px solid var(--color-blue);
}

.celebrity-image,
.story-video,
.category-image {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.reel-thumbnail {
    height: 350px;
    width: 200px;
    overflow: hidden;
    position: relative;
}

.category-image {
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md);
}

.celebrity-image img,
.story-video video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.category-image img {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid var(--color-gold);
    box-shadow: 0 0 15px rgba(212, 175, 55, 0.5);
    transition: all var(--transition-normal);
}

.celebrity-card:hover .celebrity-image img,
.story-card:hover .story-video video {
    transform: scale(1.05);
}

.category-card:hover .category-image img {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.7);
}

.placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-gray);
    color: var(--color-light-gray);
    font-size: 3rem;
}

.celebrity-info,
.story-info,
.user-info,
.category-info,
.reel-info {
    padding: var(--spacing-md);
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.celebrity-info h3,
.story-info h3,
.user-info h3,
.category-info h3,
.reel-info h3 {
    color: var(--color-white);
    margin-bottom: var(--spacing-xs);
    font-size: 1.3rem;
}

.celebrity-info h4,
.story-info h4,
.user-info h4,
.category-info h4,
.reel-info h4 {
    color: var(--color-gold);
    margin-bottom: var(--spacing-sm);
    font-size: 1.1rem;
}

.parent-category {
    font-size: 0.9rem;
    color: var(--color-light-gray);
    margin-bottom: var(--spacing-sm);
}

.parent-category .label {
    color: var(--color-gold);
    margin-right: var(--spacing-xs);
}

.celebrity-actions,
.story-actions,
.user-actions,
.category-actions,
.reel-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: auto;
    padding-top: var(--spacing-md);
}

.celebrity-actions .btn,
.story-actions .btn,
.user-actions .btn,
.category-actions .btn,
.reel-actions .btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.celebrity-actions .btn i,
.story-actions .btn i,
.user-actions .btn i,
.category-actions .btn i,
.reel-actions .btn i {
    margin-right: var(--spacing-xs);
}

.celebrity-actions .btn:hover,
.story-actions .btn:hover,
.user-actions .btn:hover,
.category-actions .btn:hover,
.reel-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
}

/* ===== Form Styles ===== */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.form-group {
    margin-bottom: var(--spacing-md);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--color-gold);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--color-gray);
    border-radius: var(--border-radius-md);
    background-color: var(--color-dark-gray);
    color: var(--color-white);
    transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--color-gold);
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
    outline: none;
}

.form-group input[type="file"] {
    padding: 0.5rem 0;
}

.file-preview {
    margin-top: var(--spacing-sm);
    max-height: 200px;
    overflow: hidden;
    border-radius: var(--border-radius-sm);
}

.file-preview img,
.file-preview video {
    max-width: 100%;
    max-height: 200px;
    border-radius: var(--border-radius-sm);
}

.file-preview img.circular-preview {
    width: 150px;
    height: 150px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid var(--color-gold);
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
    display: block;
    margin: 0 auto;
}

.form-text {
    display: block;
    margin-top: var(--spacing-xs);
    font-size: 0.85rem;
    color: var(--color-light-gray);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

/* ===== Settings Page ===== */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid var(--color-gray);
    margin-bottom: var(--spacing-lg);
}

.settings-tabs .tab {
    padding: var(--spacing-sm) var(--spacing-lg);
    cursor: pointer;
    color: var(--color-white);
    border-bottom: 3px solid transparent;
    transition: all var(--transition-normal);
    font-weight: 500;
}

.settings-tabs .tab:hover {
    color: var(--color-gold);
}

.settings-tabs .tab.active {
    color: var(--color-gold);
    border-bottom-color: var(--color-gold);
}

.tab-pane {
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

.tab-pane.active {
    display: block;
    opacity: 1;
}

.system-info {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.info-item {
    background-color: var(--color-dark-gray);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    display: flex;
    flex-direction: column;
    transition: all var(--transition-normal);
}

.info-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-sm);
}

.info-label {
    font-weight: 500;
    color: var(--color-gold);
    margin-bottom: var(--spacing-xs);
}

.info-value {
    font-size: 1.2rem;
}

/* ===== Reels Modal ===== */
.reel-video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.reel-video-modal.active {
    opacity: 1;
    visibility: visible;
}

.reel-video-container {
    width: 80%;
    max-width: 800px;
    position: relative;
}

.reel-video-container video {
    width: 100%;
    border-radius: var(--border-radius-md);
}

.close-modal {
    position: absolute;
    top: -40px;
    right: 0;
    color: var(--color-white);
    font-size: 2rem;
    cursor: pointer;
    transition: color var(--transition-normal);
}

.close-modal:hover {
    color: var(--color-gold);
}

/* Admin Reels Styles */
.reel-thumbnail .play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    z-index: 10;
}

.reel-thumbnail .play-button i {
    color: var(--color-gold);
    font-size: 1.5rem;
}

.reel-thumbnail .play-button:hover {
    background-color: var(--color-gold);
}

.reel-thumbnail .play-button:hover i {
    color: var(--color-black);
}

/* ===== Votes Page ===== */
.stats-row {
    margin-bottom: var(--spacing-lg);
}

.stat-item {
    margin-bottom: var(--spacing-md);
}

.stat-label {
    font-weight: 500;
    color: var(--color-gold);
    margin-bottom: var(--spacing-xs);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--color-white);
}

.stat-chart {
    height: 300px;
    margin-top: var(--spacing-md);
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .celebrities-grid,
    .stories-grid,
    .users-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
}

@media (max-width: 576px) {
    .celebrities-grid,
    .stories-grid,
    .users-grid {
        grid-template-columns: 1fr;
    }

    .system-info {
        grid-template-columns: 1fr;
    }
}
