<?php
/**
 * Influencers Management Page for Admin Dashboard
 */

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = $_POST['name'];
                $name_ar = $_POST['name_ar'];
                $title = $_POST['title'];
                $title_ar = $_POST['title_ar'];
                $bio = $_POST['bio'];
                $bio_ar = $_POST['bio_ar'];
                $followers_count = (int)$_POST['followers_count'];
                $influence_score = (float)$_POST['influence_score'];
                $platform = $_POST['platform'];
                $platform_url = $_POST['platform_url'];
                $facebook = $_POST['facebook'];
                $twitter = $_POST['twitter'];
                $instagram = $_POST['instagram'];
                $youtube = $_POST['youtube'];
                $tiktok = $_POST['tiktok'];
                $linkedin = $_POST['linkedin'];
                $website = $_POST['website'];
                $featured = isset($_POST['featured']) ? 1 : 0;
                $display_order = (int)$_POST['display_order'];
                $status = $_POST['status'];

                // Handle image upload
                $image = '';
                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/img/influencers/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
                    $image = 'influencer_' . time() . '.' . $file_extension;
                    $upload_path = $upload_dir . $image;

                    if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                        // Image uploaded successfully
                    } else {
                        $image = '';
                    }
                }

                $sql = "INSERT INTO influencers (name, name_ar, title, title_ar, bio, bio_ar, image, followers_count, influence_score, platform, platform_url, facebook, twitter, instagram, youtube, tiktok, linkedin, website, featured, display_order, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                $stmt = $conn->prepare($sql);
                $stmt->bind_param("ssssssidssssssssssiis", $name, $name_ar, $title, $title_ar, $bio, $bio_ar, $image, $followers_count, $influence_score, $platform, $platform_url, $facebook, $twitter, $instagram, $youtube, $tiktok, $linkedin, $website, $featured, $display_order, $status);

                if ($stmt->execute()) {
                    $success_message = "تم إضافة البلوجر المؤثر بنجاح!";
                } else {
                    $error_message = "حدث خطأ أثناء إضافة البلوجر المؤثر.";
                }
                break;

            case 'edit':
                $id = (int)$_POST['id'];
                $name = $_POST['name'];
                $name_ar = $_POST['name_ar'];
                $title = $_POST['title'];
                $title_ar = $_POST['title_ar'];
                $bio = $_POST['bio'];
                $bio_ar = $_POST['bio_ar'];
                $followers_count = (int)$_POST['followers_count'];
                $influence_score = (float)$_POST['influence_score'];
                $platform = $_POST['platform'];
                $platform_url = $_POST['platform_url'];
                $facebook = $_POST['facebook'];
                $twitter = $_POST['twitter'];
                $instagram = $_POST['instagram'];
                $youtube = $_POST['youtube'];
                $tiktok = $_POST['tiktok'];
                $linkedin = $_POST['linkedin'];
                $website = $_POST['website'];
                $featured = isset($_POST['featured']) ? 1 : 0;
                $display_order = (int)$_POST['display_order'];
                $status = $_POST['status'];

                // Handle image upload
                $update_image = false;
                $new_image = null;

                if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                    $upload_dir = '../assets/img/influencers/';
                    if (!is_dir($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
                    $new_image = 'influencer_' . time() . '.' . $file_extension;
                    $upload_path = $upload_dir . $new_image;

                    if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                        // Delete old image if exists
                        $old_result = $conn->query("SELECT image FROM influencers WHERE id = $id");
                        if ($old_result && $old_row = $old_result->fetch_assoc()) {
                            $old_image_path = $upload_dir . $old_row['image'];
                            if (file_exists($old_image_path)) {
                                unlink($old_image_path);
                            }
                        }
                        $update_image = true;
                    }
                }

                // Prepare SQL based on whether image is being updated
                if ($update_image) {
                    $sql = "UPDATE influencers SET name = ?, name_ar = ?, title = ?, title_ar = ?, bio = ?, bio_ar = ?, followers_count = ?, influence_score = ?, platform = ?, platform_url = ?, facebook = ?, twitter = ?, instagram = ?, youtube = ?, tiktok = ?, linkedin = ?, website = ?, featured = ?, display_order = ?, status = ?, image = ? WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("ssssssidsssssssssiissi", $name, $name_ar, $title, $title_ar, $bio, $bio_ar, $followers_count, $influence_score, $platform, $platform_url, $facebook, $twitter, $instagram, $youtube, $tiktok, $linkedin, $website, $featured, $display_order, $status, $new_image, $id);
                } else {
                    $sql = "UPDATE influencers SET name = ?, name_ar = ?, title = ?, title_ar = ?, bio = ?, bio_ar = ?, followers_count = ?, influence_score = ?, platform = ?, platform_url = ?, facebook = ?, twitter = ?, instagram = ?, youtube = ?, tiktok = ?, linkedin = ?, website = ?, featured = ?, display_order = ?, status = ? WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("ssssssidsssssssssiisi", $name, $name_ar, $title, $title_ar, $bio, $bio_ar, $followers_count, $influence_score, $platform, $platform_url, $facebook, $twitter, $instagram, $youtube, $tiktok, $linkedin, $website, $featured, $display_order, $status, $id);
                }

                if ($stmt->execute()) {
                    $success_message = "تم تحديث البلوجر المؤثر بنجاح!";
                } else {
                    $error_message = "حدث خطأ أثناء تحديث البلوجر المؤثر.";
                }
                break;

            case 'delete':
                $id = (int)$_POST['id'];

                // Get image path to delete it
                $result = $conn->query("SELECT image FROM influencers WHERE id = $id");
                if ($result && $row = $result->fetch_assoc()) {
                    $image_path = '../assets/img/influencers/' . $row['image'];
                    if (file_exists($image_path)) {
                        unlink($image_path);
                    }
                }

                $sql = "DELETE FROM influencers WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $id);

                if ($stmt->execute()) {
                    $success_message = "تم حذف البلوجر المؤثر بنجاح!";
                } else {
                    $error_message = "حدث خطأ أثناء حذف البلوجر المؤثر.";
                }
                break;
        }
    }
}

// Check if influencers table exists
$table_check = $conn->query("SHOW TABLES LIKE 'influencers'");
$table_exists = ($table_check && $table_check->num_rows > 0);

// Get all influencers
$influencers = [];
if ($table_exists) {
    $influencers_result = $conn->query("SELECT * FROM influencers ORDER BY display_order ASC, created_at DESC");
    if ($influencers_result) {
        while ($row = $influencers_result->fetch_assoc()) {
            $influencers[] = $row;
        }
    }
}

// Get influencer for editing
$edit_influencer = null;
if (isset($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    $edit_result = $conn->query("SELECT * FROM influencers WHERE id = $edit_id");
    if ($edit_result) {
        $edit_influencer = $edit_result->fetch_assoc();
    }
}
?>

<div class="influencers-management">
    <div class="dashboard-header enhanced-header">
        <div class="header-content">
            <div class="header-info">
                <h1 class="page-title">
                    <i class="fas fa-crown"></i>
                    إدارة البلوجرز المؤثرين
                </h1>
                <p class="header-subtitle">إدارة وتنظيم البلوجرز والمؤثرين في وسائل التواصل الاجتماعي</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary btn-add-influencer" onclick="toggleForm()">
                    <i class="fas fa-plus-circle"></i>
                    <span>إضافة بلوجر جديد</span>
                </button>
                <button class="btn btn-secondary" onclick="exportInfluencers()">
                    <i class="fas fa-download"></i>
                    <span>تصدير البيانات</span>
                </button>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo count($influencers); ?></h3>
                    <p>إجمالي البلوجرز</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo count(array_filter($influencers, function($i) { return $i['featured']; })); ?></h3>
                    <p>البلوجرز المميزين</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-info">
                    <h3><?php
                        $total_followers = array_sum(array_column($influencers, 'followers_count'));
                        echo $total_followers > 1000000 ? number_format($total_followers/1000000, 1) . 'M' :
                             ($total_followers > 1000 ? number_format($total_followers/1000, 1) . 'K' : number_format($total_followers));
                    ?></h3>
                    <p>إجمالي المتابعين</p>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-info">
                    <h3><?php
                        $avg_score = !empty($influencers) ? array_sum(array_column($influencers, 'influence_score')) / count($influencers) : 0;
                        echo number_format($avg_score, 1);
                    ?></h3>
                    <p>متوسط درجة التأثير</p>
                </div>
            </div>
        </div>
    </div>

    <?php if (isset($success_message)): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-circle"></i>
        <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <?php if (!$table_exists): ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i>
        <strong>تنبيه:</strong> جدول البلوجرز المؤثرين غير موجود في قاعدة البيانات.
        <br><br>
        <a href="../create_influencers_table.php" class="btn btn-primary" target="_blank">
            <i class="fas fa-database"></i>
            إنشاء الجدول الآن
        </a>
        <br><br>
        <small>بعد إنشاء الجدول، قم بتحديث هذه الصفحة.</small>
    </div>
    <?php endif; ?>

    <!-- Enhanced Add/Edit Form -->
    <div class="form-container enhanced-form" id="influencerForm" style="display: <?php echo $edit_influencer ? 'block' : 'none'; ?>;">
        <div class="form-card">
            <div class="form-header">
                <div class="form-title">
                    <i class="fas fa-<?php echo $edit_influencer ? 'edit' : 'plus-circle'; ?>"></i>
                    <h3><?php echo $edit_influencer ? 'تعديل البلوجر المؤثر' : 'إضافة بلوجر مؤثر جديد'; ?></h3>
                </div>
                <button class="btn-close" onclick="toggleForm()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="form-progress">
                <div class="progress-step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-label">المعلومات الأساسية</div>
                </div>
                <div class="progress-step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-label">وسائل التواصل</div>
                </div>
                <div class="progress-step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-label">الإعدادات</div>
                </div>
            </div>

            <form method="POST" enctype="multipart/form-data" class="influencer-form multi-step-form">
                <input type="hidden" name="action" value="<?php echo $edit_influencer ? 'edit' : 'add'; ?>">
                <?php if ($edit_influencer): ?>
                <input type="hidden" name="id" value="<?php echo $edit_influencer['id']; ?>">
                <?php endif; ?>

                <!-- Step 1: Basic Information -->
                <div class="form-step active" data-step="1">
                    <div class="step-header">
                        <h4><i class="fas fa-user"></i> المعلومات الأساسية</h4>
                        <p>أدخل المعلومات الأساسية للبلوجر المؤثر</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="name">
                                <i class="fas fa-user"></i>
                                الاسم (English)
                                <span class="required">*</span>
                            </label>
                            <input type="text" id="name" name="name" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['name']) : ''; ?>" required placeholder="Enter full name in English">
                        </div>

                        <div class="form-group">
                            <label for="name_ar">
                                <i class="fas fa-user"></i>
                                الاسم (العربية)
                                <span class="required">*</span>
                            </label>
                            <input type="text" id="name_ar" name="name_ar" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['name_ar']) : ''; ?>" required placeholder="أدخل الاسم الكامل بالعربية">
                        </div>

                        <div class="form-group">
                            <label for="title">
                                <i class="fas fa-briefcase"></i>
                                المسمى الوظيفي (English)
                            </label>
                            <input type="text" id="title" name="title" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['title']) : ''; ?>" placeholder="e.g. Content Creator, Blogger">
                        </div>

                        <div class="form-group">
                            <label for="title_ar">
                                <i class="fas fa-briefcase"></i>
                                المسمى الوظيفي (العربية)
                            </label>
                            <input type="text" id="title_ar" name="title_ar" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['title_ar']) : ''; ?>" placeholder="مثال: منشئ محتوى، بلوجر">
                        </div>

                        <div class="form-group">
                            <label for="followers_count">
                                <i class="fas fa-users"></i>
                                عدد المتابعين
                            </label>
                            <input type="number" id="followers_count" name="followers_count" value="<?php echo $edit_influencer ? $edit_influencer['followers_count'] : '0'; ?>" min="0" placeholder="1000000">
                            <small class="form-hint">أدخل العدد الإجمالي للمتابعين</small>
                        </div>

                        <div class="form-group">
                            <label for="influence_score">
                                <i class="fas fa-star"></i>
                                درجة التأثير (من 1 إلى 10)
                            </label>
                            <input type="number" id="influence_score" name="influence_score" value="<?php echo $edit_influencer ? $edit_influencer['influence_score'] : '0.0'; ?>" min="0" max="10" step="0.1" placeholder="8.5">
                            <div class="score-preview">
                                <div class="score-bar">
                                    <div class="score-fill" style="width: <?php echo $edit_influencer ? ($edit_influencer['influence_score'] / 10) * 100 : 0; ?>%"></div>
                                </div>
                                <span class="score-text"><?php echo $edit_influencer ? $edit_influencer['influence_score'] : '0.0'; ?>/10</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="bio">
                            <i class="fas fa-align-left"></i>
                            النبذة التعريفية (English)
                        </label>
                        <textarea id="bio" name="bio" rows="3" placeholder="Brief description about the influencer..."><?php echo $edit_influencer ? htmlspecialchars($edit_influencer['bio']) : ''; ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="bio_ar">
                            <i class="fas fa-align-right"></i>
                            النبذة التعريفية (العربية)
                        </label>
                        <textarea id="bio_ar" name="bio_ar" rows="3" placeholder="نبذة مختصرة عن البلوجر المؤثر..."><?php echo $edit_influencer ? htmlspecialchars($edit_influencer['bio_ar']) : ''; ?></textarea>
                    </div>

                    <div class="form-group image-upload-group">
                        <label for="image">
                            <i class="fas fa-camera"></i>
                            صورة البلوجر
                        </label>
                        <div class="image-upload-container">
                            <input type="file" id="image" name="image" accept="image/*" onchange="previewImage(this)">
                            <div class="upload-preview">
                                <?php if ($edit_influencer && $edit_influencer['image']): ?>
                                <img src="<?php echo asset_url('img/influencers/' . $edit_influencer['image']); ?>" alt="Current Image" class="current-image-preview">
                                <span class="image-label">الصورة الحالية</span>
                                <?php else: ?>
                                <div class="upload-placeholder">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>اختر صورة أو اسحبها هنا</span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2: Social Media Links -->
                <div class="form-step" data-step="2">
                    <div class="step-header">
                        <h4><i class="fas fa-share-alt"></i> وسائل التواصل الاجتماعي</h4>
                        <p>أضف روابط حسابات البلوجر على منصات التواصل الاجتماعي</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="platform">
                                <i class="fas fa-star"></i>
                                المنصة الرئيسية
                            </label>
                            <select id="platform" name="platform" onchange="updatePlatformPreview(this)">
                                <option value="">اختر المنصة الرئيسية</option>
                                <option value="Instagram" <?php echo ($edit_influencer && $edit_influencer['platform'] === 'Instagram') ? 'selected' : ''; ?>>
                                    <i class="fab fa-instagram"></i> Instagram
                                </option>
                                <option value="YouTube" <?php echo ($edit_influencer && $edit_influencer['platform'] === 'YouTube') ? 'selected' : ''; ?>>
                                    <i class="fab fa-youtube"></i> YouTube
                                </option>
                                <option value="TikTok" <?php echo ($edit_influencer && $edit_influencer['platform'] === 'TikTok') ? 'selected' : ''; ?>>
                                    <i class="fab fa-tiktok"></i> TikTok
                                </option>
                                <option value="Twitter" <?php echo ($edit_influencer && $edit_influencer['platform'] === 'Twitter') ? 'selected' : ''; ?>>
                                    <i class="fab fa-twitter"></i> Twitter
                                </option>
                                <option value="Facebook" <?php echo ($edit_influencer && $edit_influencer['platform'] === 'Facebook') ? 'selected' : ''; ?>>
                                    <i class="fab fa-facebook"></i> Facebook
                                </option>
                                <option value="LinkedIn" <?php echo ($edit_influencer && $edit_influencer['platform'] === 'LinkedIn') ? 'selected' : ''; ?>>
                                    <i class="fab fa-linkedin"></i> LinkedIn
                                </option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="platform_url">
                                <i class="fas fa-link"></i>
                                رابط المنصة الرئيسية
                            </label>
                            <input type="url" id="platform_url" name="platform_url" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['platform_url']) : ''; ?>" placeholder="https://...">
                        </div>
                    </div>

                    <div class="social-platforms-grid">
                        <div class="form-group social-platform">
                            <label for="facebook">
                                <i class="fab fa-facebook-f" style="color: #1877f2;"></i>
                                Facebook
                            </label>
                            <input type="url" id="facebook" name="facebook" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['facebook']) : ''; ?>" placeholder="https://facebook.com/username">
                        </div>

                        <div class="form-group social-platform">
                            <label for="twitter">
                                <i class="fab fa-twitter" style="color: #1da1f2;"></i>
                                Twitter
                            </label>
                            <input type="url" id="twitter" name="twitter" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['twitter']) : ''; ?>" placeholder="https://twitter.com/username">
                        </div>

                        <div class="form-group social-platform">
                            <label for="instagram">
                                <i class="fab fa-instagram" style="color: #e4405f;"></i>
                                Instagram
                            </label>
                            <input type="url" id="instagram" name="instagram" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['instagram']) : ''; ?>" placeholder="https://instagram.com/username">
                        </div>

                        <div class="form-group social-platform">
                            <label for="youtube">
                                <i class="fab fa-youtube" style="color: #ff0000;"></i>
                                YouTube
                            </label>
                            <input type="url" id="youtube" name="youtube" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['youtube']) : ''; ?>" placeholder="https://youtube.com/c/channel">
                        </div>

                        <div class="form-group social-platform">
                            <label for="tiktok">
                                <i class="fab fa-tiktok" style="color: #000000;"></i>
                                TikTok
                            </label>
                            <input type="url" id="tiktok" name="tiktok" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['tiktok']) : ''; ?>" placeholder="https://tiktok.com/@username">
                        </div>

                        <div class="form-group social-platform">
                            <label for="linkedin">
                                <i class="fab fa-linkedin-in" style="color: #0077b5;"></i>
                                LinkedIn
                            </label>
                            <input type="url" id="linkedin" name="linkedin" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['linkedin']) : ''; ?>" placeholder="https://linkedin.com/in/username">
                        </div>

                        <div class="form-group social-platform">
                            <label for="website">
                                <i class="fas fa-globe" style="color: #6c757d;"></i>
                                الموقع الشخصي
                            </label>
                            <input type="url" id="website" name="website" value="<?php echo $edit_influencer ? htmlspecialchars($edit_influencer['website']) : ''; ?>" placeholder="https://website.com">
                        </div>
                    </div>
                </div>

                <!-- Step 3: Settings -->
                <div class="form-step" data-step="3">
                    <div class="step-header">
                        <h4><i class="fas fa-cogs"></i> الإعدادات والتفضيلات</h4>
                        <p>اضبط إعدادات العرض والحالة للبلوجر المؤثر</p>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="display_order">
                                <i class="fas fa-sort-numeric-down"></i>
                                ترتيب العرض
                            </label>
                            <input type="number" id="display_order" name="display_order" value="<?php echo $edit_influencer ? $edit_influencer['display_order'] : '0'; ?>" min="0" placeholder="1">
                            <small class="form-hint">الرقم الأصغر يظهر أولاً</small>
                        </div>

                        <div class="form-group">
                            <label for="status">
                                <i class="fas fa-toggle-on"></i>
                                حالة البلوجر
                            </label>
                            <select id="status" name="status" required>
                                <option value="active" <?php echo ($edit_influencer && $edit_influencer['status'] === 'active') ? 'selected' : ''; ?>>
                                    <i class="fas fa-check-circle"></i> نشط
                                </option>
                                <option value="inactive" <?php echo ($edit_influencer && $edit_influencer['status'] === 'inactive') ? 'selected' : ''; ?>>
                                    <i class="fas fa-times-circle"></i> غير نشط
                                </option>
                            </select>
                        </div>
                    </div>

                    <div class="settings-cards">
                        <div class="setting-card">
                            <div class="setting-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="setting-content">
                                <h5>عرض مميز</h5>
                                <p>عرض البلوجر في الصفحة الرئيسية كبلوجر مميز</p>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="featured" value="1" <?php echo ($edit_influencer && $edit_influencer['featured']) ? 'checked' : ''; ?>>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="setting-card">
                            <div class="setting-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="setting-content">
                                <h5>إشعارات التحديث</h5>
                                <p>إرسال إشعار عند تحديث معلومات البلوجر</p>
                                <label class="toggle-switch">
                                    <input type="checkbox" name="notifications" value="1">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="preview-card">
                        <h5><i class="fas fa-eye"></i> معاينة البطاقة</h5>
                        <div class="influencer-preview">
                            <div class="preview-image">
                                <img id="previewImg" src="<?php echo $edit_influencer && $edit_influencer['image'] ? asset_url('img/influencers/' . $edit_influencer['image']) : 'assets/img/default-avatar.png'; ?>" alt="Preview">
                                <div class="preview-badge">
                                    <span id="previewScore"><?php echo $edit_influencer ? $edit_influencer['influence_score'] : '0.0'; ?></span>
                                </div>
                            </div>
                            <div class="preview-info">
                                <h6 id="previewName"><?php echo $edit_influencer ? htmlspecialchars($edit_influencer['name_ar']) : 'اسم البلوجر'; ?></h6>
                                <p id="previewTitle"><?php echo $edit_influencer ? htmlspecialchars($edit_influencer['title_ar']) : 'المسمى الوظيفي'; ?></p>
                                <div class="preview-followers">
                                    <i class="fas fa-users"></i>
                                    <span id="previewFollowers"><?php echo $edit_influencer ? number_format($edit_influencer['followers_count']) : '0'; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Navigation -->
                <div class="form-navigation">
                    <button type="button" class="btn btn-secondary" id="prevBtn" onclick="changeStep(-1)" style="display: none;">
                        <i class="fas fa-arrow-right"></i>
                        السابق
                    </button>
                    <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeStep(1)">
                        التالي
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button type="submit" class="btn btn-success" id="submitBtn" style="display: none;">
                        <i class="fas fa-save"></i>
                        <?php echo $edit_influencer ? 'تحديث البلوجر' : 'إضافة البلوجر'; ?>
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="toggleForm()">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Influencers List -->
    <div class="table-container">
        <div class="table-header">
            <h3>قائمة البلوجرز المؤثرين</h3>
            <div class="table-actions">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="البحث في البلوجرز...">
                    <i class="fas fa-search"></i>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>الاسم</th>
                        <th>المسمى الوظيفي</th>
                        <th>المنصة الرئيسية</th>
                        <th>عدد المتابعين</th>
                        <th>درجة التأثير</th>
                        <th>مميز</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($influencers)): ?>
                    <tr>
                        <td colspan="9" class="no-data">
                            <i class="fas fa-users"></i>
                            <p>لا توجد بلوجرز مؤثرين مضافين بعد</p>
                        </td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($influencers as $influencer): ?>
                    <tr>
                        <td>
                            <?php if ($influencer['image']): ?>
                            <img src="<?php echo asset_url('img/influencers/' . $influencer['image']); ?>" alt="<?php echo htmlspecialchars($influencer['name']); ?>" class="influencer-avatar">
                            <?php else: ?>
                            <div class="avatar-placeholder">
                                <i class="fas fa-user"></i>
                            </div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="influencer-info">
                                <strong><?php echo htmlspecialchars($influencer['name_ar']); ?></strong>
                                <small><?php echo htmlspecialchars($influencer['name']); ?></small>
                            </div>
                        </td>
                        <td>
                            <div class="title-info">
                                <span><?php echo htmlspecialchars($influencer['title_ar']); ?></span>
                                <small><?php echo htmlspecialchars($influencer['title']); ?></small>
                            </div>
                        </td>
                        <td>
                            <?php if ($influencer['platform']): ?>
                            <div class="platform-info">
                                <i class="fab fa-<?php echo strtolower($influencer['platform']); ?>"></i>
                                <span><?php echo $influencer['platform']; ?></span>
                            </div>
                            <?php else: ?>
                            <span class="text-muted">غير محدد</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="followers-count">
                                <?php echo number_format($influencer['followers_count']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="influence-score">
                                <span class="score"><?php echo $influencer['influence_score']; ?></span>
                                <div class="score-bar">
                                    <div class="score-fill" style="width: <?php echo ($influencer['influence_score'] / 10) * 100; ?>%"></div>
                                </div>
                            </div>
                        </td>
                        <td>
                            <?php if ($influencer['featured']): ?>
                            <span class="badge badge-success">
                                <i class="fas fa-star"></i>
                                مميز
                            </span>
                            <?php else: ?>
                            <span class="badge badge-secondary">عادي</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <?php if ($influencer['status'] === 'active'): ?>
                            <span class="badge badge-success">نشط</span>
                            <?php else: ?>
                            <span class="badge badge-danger">غير نشط</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="?page=influencers&edit=<?php echo $influencer['id']; ?>" class="btn btn-sm btn-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteInfluencer(<?php echo $influencer['id']; ?>)" class="btn btn-sm btn-danger" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php if ($influencer['platform_url']): ?>
                                <a href="<?php echo htmlspecialchars($influencer['platform_url']); ?>" target="_blank" class="btn btn-sm btn-info" title="زيارة المنصة">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>تأكيد الحذف</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من حذف هذا البلوجر المؤثر؟ لا يمكن التراجع عن هذا الإجراء.</p>
        </div>
        <div class="modal-footer">
            <form method="POST" id="deleteForm">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" id="deleteId">
                <button type="submit" class="btn btn-danger">حذف</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
            </form>
        </div>
    </div>
</div>

<script>
let currentStep = 1;
const totalSteps = 3;

function toggleForm() {
    const form = document.getElementById('influencerForm');
    if (form.style.display === 'none' || form.style.display === '') {
        form.style.display = 'block';
        resetForm();
    } else {
        form.style.display = 'none';
    }
}

function resetForm() {
    currentStep = 1;
    showStep(currentStep);
    updateProgressBar();
}

function changeStep(direction) {
    console.log('Changing step. Current:', currentStep, 'Direction:', direction);

    if (direction === 1 && currentStep < totalSteps) {
        console.log('Moving to next step...');
        if (validateCurrentStep()) {
            currentStep++;
            console.log('New step:', currentStep);
            showStep(currentStep);
            updateProgressBar();
        } else {
            console.log('Validation failed, staying on current step');
        }
    } else if (direction === -1 && currentStep > 1) {
        console.log('Moving to previous step...');
        currentStep--;
        console.log('New step:', currentStep);
        showStep(currentStep);
        updateProgressBar();
    } else {
        console.log('Cannot change step. Current:', currentStep, 'Total:', totalSteps, 'Direction:', direction);
    }
}

function showStep(step) {
    console.log('Showing step:', step); // للتشخيص

    // Hide all steps
    document.querySelectorAll('.form-step').forEach(stepEl => {
        stepEl.style.display = 'none';
        stepEl.classList.remove('active');
    });

    // Show current step
    const currentStepEl = document.querySelector(`.form-step[data-step="${step}"]`);
    console.log('Current step element:', currentStepEl); // للتشخيص

    if (currentStepEl) {
        currentStepEl.style.display = 'block';
        currentStepEl.classList.add('active');
    }

    // Update navigation buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');

    if (prevBtn) prevBtn.style.display = step === 1 ? 'none' : 'inline-flex';
    if (nextBtn) nextBtn.style.display = step === totalSteps ? 'none' : 'inline-flex';
    if (submitBtn) submitBtn.style.display = step === totalSteps ? 'inline-flex' : 'none';
}

function updateProgressBar() {
    document.querySelectorAll('.progress-step').forEach((step, index) => {
        if (index + 1 <= currentStep) {
            step.classList.add('active');
        } else {
            step.classList.remove('active');
        }
    });
}

function validateCurrentStep() {
    const currentStepEl = document.querySelector(`.form-step[data-step="${currentStep}"]`);
    if (!currentStepEl) {
        console.log('Current step element not found for step:', currentStep);
        return false;
    }

    const requiredFields = currentStepEl.querySelectorAll('input[required], select[required], textarea[required]');
    console.log('Required fields in step', currentStep, ':', requiredFields.length);

    // For step 1, only validate name fields as required
    if (currentStep === 1) {
        const nameField = currentStepEl.querySelector('#name');
        const nameArField = currentStepEl.querySelector('#name_ar');

        let isValid = true;
        let emptyFields = [];

        if (nameField && !nameField.value.trim()) {
            nameField.classList.add('error');
            emptyFields.push('الاسم (English)');
            isValid = false;
        } else if (nameField) {
            nameField.classList.remove('error');
        }

        if (nameArField && !nameArField.value.trim()) {
            nameArField.classList.add('error');
            emptyFields.push('الاسم (العربية)');
            isValid = false;
        } else if (nameArField) {
            nameArField.classList.remove('error');
        }

        if (!isValid) {
            showNotification('يرجى ملء الحقول المطلوبة: ' + emptyFields.join(', '), 'error');
        }

        return isValid;
    }

    // For other steps, allow progression without strict validation
    return true;
}

function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.querySelector('.upload-preview');
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Preview" class="image-preview">
                <span class="image-label">معاينة الصورة الجديدة</span>
            `;

            // Update preview in step 3
            const previewImg = document.getElementById('previewImg');
            if (previewImg) {
                previewImg.src = e.target.result;
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function updatePlatformPreview(select) {
    const platformUrl = document.getElementById('platform_url');
    const platform = select.value;

    if (platform) {
        const placeholders = {
            'Instagram': 'https://instagram.com/username',
            'YouTube': 'https://youtube.com/c/channel',
            'TikTok': 'https://tiktok.com/@username',
            'Twitter': 'https://twitter.com/username',
            'Facebook': 'https://facebook.com/username',
            'LinkedIn': 'https://linkedin.com/in/username'
        };

        platformUrl.placeholder = placeholders[platform] || 'https://...';
    }
}

// Real-time preview updates
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing form...');

    // Initialize the form
    currentStep = 1;
    showStep(currentStep);
    updateProgressBar();

    // Update preview when typing
    const nameAr = document.getElementById('name_ar');
    const titleAr = document.getElementById('title_ar');
    const followersCount = document.getElementById('followers_count');
    const influenceScore = document.getElementById('influence_score');

    console.log('Form elements found:', {
        nameAr: !!nameAr,
        titleAr: !!titleAr,
        followersCount: !!followersCount,
        influenceScore: !!influenceScore
    });

    if (nameAr) {
        nameAr.addEventListener('input', function() {
            const previewName = document.getElementById('previewName');
            if (previewName) {
                previewName.textContent = this.value || 'اسم البلوجر';
            }
        });
    }

    if (titleAr) {
        titleAr.addEventListener('input', function() {
            const previewTitle = document.getElementById('previewTitle');
            if (previewTitle) {
                previewTitle.textContent = this.value || 'المسمى الوظيفي';
            }
        });
    }

    if (followersCount) {
        followersCount.addEventListener('input', function() {
            const previewFollowers = document.getElementById('previewFollowers');
            if (previewFollowers) {
                const count = parseInt(this.value) || 0;
                previewFollowers.textContent = count.toLocaleString();
            }
        });
    }

    if (influenceScore) {
        influenceScore.addEventListener('input', function() {
            const score = parseFloat(this.value) || 0;
            const previewScore = document.getElementById('previewScore');
            const scoreBar = document.querySelector('.score-preview .score-fill');
            const scoreText = document.querySelector('.score-preview .score-text');

            if (previewScore) previewScore.textContent = score.toFixed(1);
            if (scoreBar) scoreBar.style.width = (score / 10 * 100) + '%';
            if (scoreText) scoreText.textContent = score.toFixed(1) + '/10';
        });
    }
});

function exportInfluencers() {
    window.open('export_influencers.php', '_blank');
}

function deleteInfluencer(id) {
    document.getElementById('deleteId').value = id;
    document.getElementById('deleteModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 10px;
        font-weight: 500;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('deleteModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// Search functionality
document.getElementById('searchInput').addEventListener('keyup', function() {
    const searchTerm = this.value.toLowerCase();
    const tableRows = document.querySelectorAll('.data-table tbody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
});
</script>

<style>
.alert-warning {
    background-color: rgba(255, 193, 7, 0.2);
    color: #856404;
    border: 1px solid rgba(255, 193, 7, 0.3);
    padding: 20px;
    border-radius: var(--border-radius-md);
    margin-bottom: 30px;
    text-align: center;
}

.alert-warning .btn {
    margin: 10px 5px;
}

.alert-warning strong {
    color: #856404;
}

.alert-warning small {
    color: #6c757d;
    font-style: italic;
}

/* Enhanced Admin Styles */
.enhanced-header {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
}

.header-info h1 {
    color: var(--color-gold);
    font-size: 2.2rem;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-subtitle {
    color: #ccc;
    font-size: 1.1rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 15px;
    align-items: center;
}

.btn-add-influencer {
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    border: none;
    padding: 15px 25px;
    border-radius: 12px;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
}

.btn-add-influencer:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
}

/* Stats Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    border-color: var(--color-gold);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.2);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    font-size: 1.5rem;
}

.stat-info h3 {
    color: var(--color-gold);
    font-size: 1.8rem;
    margin: 0 0 5px 0;
    font-weight: 800;
}

.stat-info p {
    color: #ccc;
    margin: 0;
    font-size: 0.9rem;
}

/* Enhanced Form */
.enhanced-form .form-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.form-header {
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    color: var(--color-black);
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.form-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.form-title h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

/* Progress Bar */
.form-progress {
    display: flex;
    justify-content: center;
    padding: 30px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;
    flex: 1;
    max-width: 150px;
}

.progress-step:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 60%;
    width: 80%;
    height: 2px;
    background: rgba(212, 175, 55, 0.3);
    z-index: 1;
}

.progress-step.active:not(:last-child)::after {
    background: var(--color-gold);
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(212, 175, 55, 0.2);
    color: #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.progress-step.active .step-number {
    background: var(--color-gold);
    color: var(--color-black);
    transform: scale(1.1);
}

.step-label {
    color: #ccc;
    font-size: 0.85rem;
    text-align: center;
    transition: color 0.3s ease;
}

.progress-step.active .step-label {
    color: var(--color-gold);
    font-weight: 600;
}

/* Form Steps */
.multi-step-form {
    padding: 30px;
}

.form-step {
    display: none !important;
}

.form-step.active {
    display: block !important;
    animation: fadeInUp 0.5s ease;
}

.form-step[data-step="1"] {
    display: block !important;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.step-header h4 {
    color: var(--color-gold);
    font-size: 1.4rem;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.step-header p {
    color: #ccc;
    margin: 0;
}

/* Enhanced Form Groups */
.form-group label {
    color: var(--color-gold);
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.required {
    color: #ff4757;
    font-size: 0.8rem;
}

.form-hint {
    color: #999;
    font-size: 0.8rem;
    margin-top: 5px;
    display: block;
}

/* Score Preview */
.score-preview {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.score-bar {
    flex: 1;
    height: 8px;
    background: rgba(212, 175, 55, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-gold) 0%, #ffd700 100%);
    transition: width 0.3s ease;
}

.score-text {
    color: var(--color-gold);
    font-weight: 700;
    font-size: 0.9rem;
}

/* Image Upload */
.image-upload-container {
    margin-top: 10px;
}

.upload-preview {
    width: 150px;
    height: 150px;
    border: 2px dashed rgba(212, 175, 55, 0.5);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.upload-preview:hover {
    border-color: var(--color-gold);
    background: rgba(212, 175, 55, 0.1);
}

.upload-placeholder {
    text-align: center;
    color: #ccc;
}

.upload-placeholder i {
    font-size: 2rem;
    color: var(--color-gold);
    margin-bottom: 10px;
}

.current-image-preview,
.image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
}

.image-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px;
    font-size: 0.8rem;
    text-align: center;
}

/* Social Platforms Grid */
.social-platforms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.social-platform {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 10px;
    padding: 15px;
    transition: all 0.3s ease;
}

.social-platform:hover {
    border-color: var(--color-gold);
    background: rgba(255, 255, 255, 0.05);
}

/* Settings Cards */
.settings-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.setting-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    gap: 15px;
    transition: all 0.3s ease;
}

.setting-card:hover {
    border-color: var(--color-gold);
    background: rgba(255, 255, 255, 0.05);
}

.setting-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--color-gold) 0%, #ffd700 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-black);
    font-size: 1.2rem;
}

.setting-content h5 {
    color: var(--color-gold);
    margin: 0 0 5px 0;
    font-size: 1.1rem;
}

.setting-content p {
    color: #ccc;
    margin: 0 0 15px 0;
    font-size: 0.9rem;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--color-gold);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Preview Card */
.preview-card {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 12px;
    padding: 20px;
    margin-top: 20px;
}

.preview-card h5 {
    color: var(--color-gold);
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.influencer-preview {
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-radius: 10px;
}

.preview-image {
    position: relative;
    width: 80px;
    height: 80px;
}

.preview-image img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--color-gold);
}

.preview-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--color-gold);
    color: var(--color-black);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
}

.preview-info h6 {
    color: var(--color-gold);
    margin: 0 0 5px 0;
    font-size: 1.1rem;
}

.preview-info p {
    color: #ccc;
    margin: 0 0 8px 0;
    font-size: 0.9rem;
}

.preview-followers {
    color: #999;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Form Navigation */
.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(212, 175, 55, 0.2);
}

.form-navigation .btn {
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.form-navigation .btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.form-navigation .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

/* Error States */
.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #ff4757;
    box-shadow: 0 0 0 2px rgba(255, 71, 87, 0.2);
}

/* Responsive */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
    }

    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .form-progress {
        padding: 20px 10px;
    }

    .progress-step {
        max-width: 100px;
    }

    .step-label {
        font-size: 0.75rem;
    }

    .social-platforms-grid {
        grid-template-columns: 1fr;
    }

    .settings-cards {
        grid-template-columns: 1fr;
    }

    .influencer-preview {
        flex-direction: column;
        text-align: center;
    }

    .form-navigation {
        flex-wrap: wrap;
        gap: 10px;
    }
}
</style>
