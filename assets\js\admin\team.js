/**
 * Team Page JavaScript
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add social media preview
    initSocialMediaPreview();
    
    // Add drag and drop reordering
    initDragAndDrop();
    
    // Add search functionality
    initSearch();
});

/**
 * Add social media preview
 */
function initSocialMediaPreview() {
    const socialInputs = document.querySelectorAll('input[type="url"]');
    
    socialInputs.forEach(input => {
        // Create preview icon if it doesn't exist
        let previewIcon = input.parentElement.querySelector('.social-preview');
        if (!previewIcon) {
            previewIcon = document.createElement('a');
            previewIcon.className = 'social-preview';
            previewIcon.target = '_blank';
            previewIcon.rel = 'noopener noreferrer';
            previewIcon.style.marginLeft = '10px';
            previewIcon.style.fontSize = '1.2rem';
            previewIcon.style.color = '#aaa';
            previewIcon.style.textDecoration = 'none';
            previewIcon.style.display = 'none';
            
            // Get the icon class from the label
            const label = input.parentElement.querySelector('label');
            if (label) {
                const icon = label.querySelector('i');
                if (icon) {
                    const iconClass = icon.className;
                    previewIcon.innerHTML = `<i class="${iconClass}"></i>`;
                }
            }
            
            input.parentElement.appendChild(previewIcon);
        }
        
        // Update preview on input change
        input.addEventListener('input', function() {
            const url = this.value.trim();
            
            if (url) {
                previewIcon.href = url;
                previewIcon.style.display = 'inline-block';
            } else {
                previewIcon.style.display = 'none';
            }
        });
        
        // Trigger the event on load
        const event = new Event('input');
        input.dispatchEvent(event);
    });
}

/**
 * Add drag and drop reordering
 */
function initDragAndDrop() {
    const table = document.querySelector('.table');
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    if (!tbody) return;
    
    const rows = tbody.querySelectorAll('tr');
    if (rows.length <= 1) return;
    
    // Add draggable attribute to rows
    rows.forEach(row => {
        row.setAttribute('draggable', 'true');
        row.style.cursor = 'move';
        
        // Add drag start event
        row.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', Array.from(rows).indexOf(this));
            this.classList.add('dragging');
        });
        
        // Add drag end event
        row.addEventListener('dragend', function() {
            this.classList.remove('dragging');
        });
        
        // Add drag over event
        row.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        });
        
        // Add drag leave event
        row.addEventListener('dragleave', function() {
            this.classList.remove('drag-over');
        });
        
        // Add drop event
        row.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');
            
            const fromIndex = parseInt(e.dataTransfer.getData('text/plain'));
            const toIndex = Array.from(rows).indexOf(this);
            
            if (fromIndex === toIndex) return;
            
            // Reorder rows
            if (fromIndex < toIndex) {
                tbody.insertBefore(rows[fromIndex], rows[toIndex].nextSibling);
            } else {
                tbody.insertBefore(rows[fromIndex], rows[toIndex]);
            }
            
            // Update display order
            updateDisplayOrder();
        });
    });
    
    // Add helper function to update display order
    function updateDisplayOrder() {
        const updatedRows = tbody.querySelectorAll('tr');
        
        updatedRows.forEach((row, index) => {
            const orderCell = row.querySelector('td:nth-child(4)');
            if (orderCell) {
                orderCell.textContent = index + 1;
                
                // Update hidden input if exists
                const form = row.querySelector('form');
                if (form) {
                    const orderInput = form.querySelector('input[name="display_order"]');
                    if (orderInput) {
                        orderInput.value = index + 1;
                    }
                }
            }
        });
    }
}

/**
 * Add search functionality
 */
function initSearch() {
    const table = document.querySelector('.table');
    if (!table) return;
    
    // Create search input
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.style.marginBottom = '20px';
    
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'البحث عن عضو...';
    searchInput.className = 'form-control';
    searchInput.style.width = '100%';
    searchInput.style.maxWidth = '300px';
    
    searchContainer.appendChild(searchInput);
    
    // Insert search input before table
    table.parentElement.insertBefore(searchContainer, table);
    
    // Add search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const name = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const position = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
            
            if (name.includes(searchTerm) || position.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
}

// Add CSS for drag and drop
const style = document.createElement('style');
style.textContent = `
    .dragging {
        opacity: 0.5;
        background-color: var(--color-black) !important;
    }
    
    .drag-over {
        border-top: 2px solid var(--color-gold);
    }
    
    .social-preview {
        transition: all 0.3s ease;
    }
    
    .social-preview:hover {
        color: var(--color-gold) !important;
        transform: scale(1.2);
    }
`;
document.head.appendChild(style);
