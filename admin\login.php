<?php
/**
 * Admin Login for Social Media Festival
 */

// Load session configuration
require_once '../config/session.php';

// Start session
session_start();

// Load configuration
$conn = require_once '../config/database.php';
require_once '../config/config.php';
require_once '../config/i18n.php';

// Set language variables
$lang = isset($_SESSION['lang']) ? $_SESSION['lang'] : DEFAULT_LANG;
$dir = $lang === 'ar' ? 'rtl' : 'ltr';

// Load common functions
require_once '../includes/functions.php';

// Check if user is already logged in and is admin
if (is_logged_in() && is_admin()) {
    // Redirect to admin dashboard
    header('Location: index.php');
    exit;
}

// Initialize variables
$error = '';
$username = '';

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Get form data
        $username = trim($_POST['username']);
        $password = $_POST['password'];

        // Validate form data
        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } else {
            // Check if user exists
            $username = $conn->real_escape_string($username);
            $sql = "SELECT id, username, password, full_name, is_admin FROM users WHERE username = '$username' OR email = '$username' LIMIT 1";
            $result = $conn->query($sql);

            if ($result && $result->num_rows > 0) {
                $user = $result->fetch_assoc();

                // Verify password
                if (password_verify($password, $user['password'])) {
                    // Check if user is admin
                    if ($user['is_admin']) {
                        // Set session variables
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['user_name'] = $user['username'];
                        $_SESSION['full_name'] = $user['full_name'];
                        $_SESSION['is_admin'] = true;

                        // Redirect to admin dashboard
                        header('Location: index.php');
                        exit;
                    } else {
                        $error = 'You do not have permission to access the admin area.';
                    }
                } else {
                    $error = 'Invalid username or password.';
                }
            } else {
                $error = 'Invalid username or password.';
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo $lang; ?>" dir="<?php echo $dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?php echo SITE_NAME; ?></title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo asset_url('img/favicon.ico'); ?>">

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="<?php echo asset_url('css/normalize.css'); ?>">
    <link rel="stylesheet" href="<?php echo asset_url('css/admin.css'); ?>">
    <?php if (is_rtl()): ?>
    <link rel="stylesheet" href="<?php echo asset_url('css/admin-rtl.css'); ?>">
    <?php endif; ?>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&family=Poppins:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body class="admin-login-page">
    <div class="login-container">
        <div class="login-logo">
            <img src="<?php echo asset_url('img/logo.png'); ?>" alt="<?php echo SITE_NAME; ?>">
        </div>

        <div class="login-form-container">
            <h1>تسجيل دخول المدير</h1>

            <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <?php echo $error; ?>
            </div>
            <?php endif; ?>

            <form class="login-form" action="login.php" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                <div class="form-group">
                    <label for="username">اسم المستخدم أو البريد الإلكتروني</label>
                    <div class="input-icon">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($username); ?>" required autofocus>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-icon">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">تسجيل الدخول</button>
                    <a href="../index.php" class="btn btn-link">العودة إلى الموقع</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Admin JavaScript -->
    <script src="<?php echo asset_url('js/admin.js'); ?>"></script>
</body>
</html>
