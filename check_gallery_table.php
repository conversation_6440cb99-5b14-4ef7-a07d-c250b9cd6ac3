<?php
require_once 'config/config.php';
require_once 'config/database.php';

echo "<h2>🔍 فحص جدول المعرض</h2>";

// التحقق من وجود الجدول
$table_check = $conn->query("SHOW TABLES LIKE 'gallery'");
if ($table_check->num_rows == 0) {
    echo "❌ جدول المعرض غير موجود. سأقوم بإنشائه الآن...<br>";
    
    // إنشاء الجدول
    $sql_create = "CREATE TABLE `gallery` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `title_ar` varchar(255) NOT NULL,
        `description` text,
        `description_ar` text,
        `image` varchar(255) NOT NULL,
        `category` varchar(100) DEFAULT 'general',
        `category_ar` varchar(100) DEFAULT 'عام',
        `featured` tinyint(1) DEFAULT 0,
        `sort_order` int(11) DEFAULT 0,
        `status` enum('active','inactive') DEFAULT 'active',
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_category` (`category`),
        KEY `idx_featured` (`featured`),
        KEY `idx_status` (`status`),
        KEY `idx_sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($conn->query($sql_create)) {
        echo "✅ تم إنشاء جدول المعرض بنجاح<br>";
    } else {
        echo "❌ خطأ في إنشاء الجدول: " . $conn->error . "<br>";
        exit;
    }
} else {
    echo "✅ جدول المعرض موجود<br>";
}

// فحص البيانات الموجودة
$data_check = $conn->query("SELECT COUNT(*) as count FROM gallery");
$count = $data_check->fetch_assoc()['count'];
echo "📊 عدد الصور في المعرض: $count<br>";

if ($count > 0) {
    echo "<br>📋 قائمة الصور الموجودة:<br>";
    $images = $conn->query("SELECT id, title, title_ar, image, status FROM gallery ORDER BY id DESC LIMIT 10");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>العنوان</th><th>العنوان العربي</th><th>الصورة</th><th>الحالة</th></tr>";
    while ($row = $images->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['title']) . "</td>";
        echo "<td>" . htmlspecialchars($row['title_ar']) . "</td>";
        echo "<td>" . htmlspecialchars($row['image']) . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<br>⚠️ لا توجد صور في المعرض. يمكنك إضافة صور من <a href='admin/index.php?page=gallery'>لوحة الإدارة</a><br>";
}

// فحص دوال المعرض
echo "<br>🔧 فحص الدوال:<br>";
if (function_exists('get_gallery_images')) {
    echo "✅ دالة get_gallery_images موجودة<br>";
    
    // اختبار الدالة
    $test_images = get_gallery_images(null, false, 5);
    echo "📊 عدد الصور المسترجعة من الدالة: " . count($test_images) . "<br>";
    
    if (count($test_images) > 0) {
        echo "✅ الدالة تعمل بشكل صحيح<br>";
        echo "🖼️ أول صورة: " . htmlspecialchars($test_images[0]['title']) . "<br>";
    } else {
        echo "⚠️ الدالة لا تسترجع أي صور<br>";
    }
} else {
    echo "❌ دالة get_gallery_images غير موجودة<br>";
}

if (function_exists('get_gallery_title')) {
    echo "✅ دالة get_gallery_title موجودة<br>";
} else {
    echo "❌ دالة get_gallery_title غير موجودة<br>";
}

// فحص مجلد الرفع
$upload_dir = 'assets/uploads/';
if (is_dir($upload_dir)) {
    echo "<br>📁 مجلد الرفع موجود: $upload_dir<br>";
    if (is_writable($upload_dir)) {
        echo "✅ مجلد الرفع قابل للكتابة<br>";
    } else {
        echo "❌ مجلد الرفع غير قابل للكتابة<br>";
    }
    
    // عرض الصور الموجودة
    $files = glob($upload_dir . 'gallery_*');
    echo "📊 عدد صور المعرض في المجلد: " . count($files) . "<br>";
    
    if (count($files) > 0) {
        echo "🖼️ أول 5 صور:<br>";
        for ($i = 0; $i < min(5, count($files)); $i++) {
            $filename = basename($files[$i]);
            echo "- $filename<br>";
        }
    }
} else {
    echo "<br>❌ مجلد الرفع غير موجود: $upload_dir<br>";
}

// إضافة محتوى الترجمة إذا لم يكن موجوداً
$content_check = $conn->query("SELECT COUNT(*) as count FROM content WHERE content_key = 'gallery_title'");
if ($content_check && $content_check->fetch_assoc()['count'] == 0) {
    echo "<br>📝 إضافة محتوى الترجمة...<br>";
    
    $content_items = [
        ['gallery_title', 'Photo Gallery', 'معرض الصور'],
        ['gallery_subtitle', 'Capturing the best moments of our festival', 'نلتقط أفضل لحظات مهرجاننا'],
        ['gallery_view_all', 'View All Photos', 'عرض جميع الصور']
    ];
    
    $content_sql = "INSERT INTO content (content_key, content_value, content_value_ar) VALUES (?, ?, ?)";
    $content_stmt = $conn->prepare($content_sql);
    
    foreach ($content_items as $item) {
        $content_stmt->bind_param('sss', $item[0], $item[1], $item[2]);
        $content_stmt->execute();
    }
    
    echo "✅ تم إضافة محتوى الترجمة<br>";
} else {
    echo "<br>✅ محتوى الترجمة موجود<br>";
}

echo "<br><div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>📋 ملخص الفحص:</h3>";
echo "<ul>";
echo "<li>جدول المعرض: " . ($table_check->num_rows > 0 ? "✅ موجود" : "❌ غير موجود") . "</li>";
echo "<li>عدد الصور: $count</li>";
echo "<li>الدوال: " . (function_exists('get_gallery_images') ? "✅ تعمل" : "❌ لا تعمل") . "</li>";
echo "<li>مجلد الرفع: " . (is_dir($upload_dir) ? "✅ موجود" : "❌ غير موجود") . "</li>";
echo "</ul>";
echo "</div>";

echo "<br><a href='index.php'>العودة للصفحة الرئيسية</a> | ";
echo "<a href='admin/index.php?page=gallery'>إدارة المعرض</a>";

$conn->close();
?>
