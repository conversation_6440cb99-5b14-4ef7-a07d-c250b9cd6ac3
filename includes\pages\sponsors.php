<?php
/**
 * Sponsors Page
 */

// Get sponsors from database
function get_sponsors_by_category($category) {
    global $conn;

    // Check if sponsors table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'sponsors'");
    if ($check_table->num_rows == 0) {
        // Table doesn't exist, return empty array
        return [];
    }

    // Check if type column exists (instead of category)
    $check_column = $conn->query("SHOW COLUMNS FROM sponsors LIKE 'type'");
    if ($check_column->num_rows > 0) {
        // Use type column
        $sql = "SELECT * FROM sponsors WHERE type = ? ORDER BY id ASC, name ASC";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $category);
        $stmt->execute();
        $result = $stmt->get_result();
        $sponsors = [];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $sponsors[] = $row;
            }
        }

        return $sponsors;
    }

    // Fallback to category column if type doesn't exist
    $check_column = $conn->query("SHOW COLUMNS FROM sponsors LIKE 'category'");
    if ($check_column->num_rows > 0) {
        $sql = "SELECT * FROM sponsors WHERE category = ? ORDER BY id ASC, name ASC";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $category);
        $stmt->execute();
        $result = $stmt->get_result();
        $sponsors = [];

        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $sponsors[] = $row;
            }
        }

        return $sponsors;
    }

    // Neither column exists
    return [];
}

// Check if sponsors table exists
$check_table = $conn->query("SHOW TABLES LIKE 'sponsors'");
$table_exists = $check_table->num_rows > 0;

// Create sponsors table if it doesn't exist
if (!$table_exists) {
    $sql = "CREATE TABLE sponsors (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        name_ar VARCHAR(100) NOT NULL,
        type ENUM('platinum', 'gold', 'silver') NOT NULL,
        logo VARCHAR(255) NOT NULL,
        website VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    if ($conn->query($sql) === TRUE) {
        // Insert sample sponsors
        $sample_sponsors = [
            // Platinum sponsors
            ['Platinum Sponsor 1', 'الراعي البلاتيني 1', 'platinum', 'sponsor1.png', 'https://example.com'],
            ['Platinum Sponsor 2', 'الراعي البلاتيني 2', 'platinum', 'sponsor2.png', 'https://example.com'],

            // Gold sponsors
            ['Gold Sponsor 1', 'الراعي الذهبي 1', 'gold', 'sponsor3.png', 'https://example.com'],
            ['Gold Sponsor 2', 'الراعي الذهبي 2', 'gold', 'sponsor4.png', 'https://example.com'],
            ['Gold Sponsor 3', 'الراعي الذهبي 3', 'gold', 'sponsor5.png', 'https://example.com'],

            // Silver sponsors
            ['Silver Sponsor 1', 'الراعي الفضي 1', 'silver', 'sponsor6.png', 'https://example.com'],
            ['Silver Sponsor 2', 'الراعي الفضي 2', 'silver', 'sponsor7.png', 'https://example.com']
        ];

        $insert_sql = "INSERT INTO sponsors (name, name_ar, type, logo, website) VALUES (?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_sql);

        foreach ($sample_sponsors as $sponsor) {
            $insert_stmt->bind_param("sssss", $sponsor[0], $sponsor[1], $sponsor[2], $sponsor[3], $sponsor[4]);
            $insert_stmt->execute();
        }
    }
}

// Get sponsors by category
$platinum_sponsors = get_sponsors_by_category('platinum');
$gold_sponsors = get_sponsors_by_category('gold');
$silver_sponsors = get_sponsors_by_category('silver');
$bronze_sponsors = get_sponsors_by_category('bronze');
$media_partners = get_sponsors_by_category('media');
$technology_partners = get_sponsors_by_category('technology');

// Page title
$page_title = get_content('sponsors', 'title', 'Our Sponsors');
?>

<!-- Sponsors Page -->
<section class="sponsors-section">
    <div class="container">
        <div class="sponsors-header">
            <h1 class="sponsors-title"><?php echo $page_title; ?></h1>
            <p class="sponsors-description"><?php echo get_content('sponsors', 'description', 'We are grateful to our sponsors for their support in making the Social Media Festival a success.'); ?></p>
        </div>

        <!-- Platinum Sponsors -->
        <?php if (!empty($platinum_sponsors)): ?>
        <div class="platinum-sponsors">
            <div class="platinum-title">
                <h2 class="category-title"><?php echo get_content('sponsors', 'platinum', 'Platinum Sponsors'); ?></h2>
            </div>
            <div class="platinum-grid">
                <?php foreach ($platinum_sponsors as $sponsor): ?>
                <div class="platinum-card">
                    <div class="platinum-logo">
                        <img src="<?php echo asset_url('uploads/' . (isset($sponsor['logo']) ? $sponsor['logo'] : 'default-sponsor.png')); ?>" alt="<?php echo htmlspecialchars($sponsor['name']); ?>">
                    </div>
                    <pre style="display:none;"><?php print_r($sponsor); ?></pre>
                    <div class="platinum-info">
                        <h3 class="platinum-name"><?php echo $current_lang === 'ar' ? $sponsor['name_ar'] : $sponsor['name']; ?></h3>
                        <div class="platinum-description">
                            <?php
                            $description = $current_lang === 'ar' ?
                                (isset($sponsor['description_ar']) ? $sponsor['description_ar'] : '') :
                                (isset($sponsor['description']) ? $sponsor['description'] : '');
                            echo $description;
                            ?>
                        </div>
                        <?php if (!empty($sponsor['website'])): ?>
                        <div class="platinum-actions">
                            <a href="<?php echo $sponsor['website']; ?>" target="_blank" class="btn-website">
                                <i class="fas fa-globe"></i> <?php echo get_content('sponsors', 'visit_website', 'Visit Website'); ?>
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Gold Sponsors -->
        <?php if (!empty($gold_sponsors)): ?>
        <div class="category-container">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-medal"></i>
                </div>
                <h2 class="category-title"><?php echo get_content('sponsors', 'gold', 'Gold Sponsors'); ?></h2>
            </div>
            <div class="sponsors-grid">
                <?php foreach ($gold_sponsors as $sponsor): ?>
                <div class="sponsor-card">
                    <div class="sponsor-logo">
                        <img src="<?php echo asset_url('uploads/' . (isset($sponsor['logo']) ? $sponsor['logo'] : 'default-sponsor.png')); ?>" alt="<?php echo htmlspecialchars($sponsor['name']); ?>">
                    </div>
                    <div class="sponsor-info">
                        <h3 class="sponsor-name"><?php echo $current_lang === 'ar' ? $sponsor['name_ar'] : $sponsor['name']; ?></h3>
                        <div class="sponsor-description">
                            <?php
                            $description = $current_lang === 'ar' ?
                                (isset($sponsor['description_ar']) ? $sponsor['description_ar'] : '') :
                                (isset($sponsor['description']) ? $sponsor['description'] : '');
                            echo mb_substr($description, 0, 100) . (mb_strlen($description) > 100 ? '...' : '');
                            ?>
                        </div>
                        <div class="sponsor-meta">
                            <div class="sponsor-category">
                                <?php echo get_content('sponsors', 'gold', 'Gold Sponsors'); ?>
                            </div>
                            <pre style="display:none;"><?php print_r($sponsor); ?></pre>
                            <?php if (!empty($sponsor['website'])): ?>
                            <div class="sponsor-actions">
                                <a href="<?php echo $sponsor['website']; ?>" target="_blank" class="btn-website">
                                    <i class="fas fa-globe"></i> <?php echo get_content('sponsors', 'visit_website', 'Visit Website'); ?>
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Silver Sponsors -->
        <?php if (!empty($silver_sponsors)): ?>
        <div class="category-container">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-award"></i>
                </div>
                <h2 class="category-title"><?php echo get_content('sponsors', 'silver', 'Silver Sponsors'); ?></h2>
            </div>
            <div class="sponsors-grid">
                <?php foreach ($silver_sponsors as $sponsor): ?>
                <div class="sponsor-card">
                    <div class="sponsor-logo">
                        <img src="<?php echo asset_url('uploads/' . (isset($sponsor['logo']) ? $sponsor['logo'] : 'default-sponsor.png')); ?>" alt="<?php echo htmlspecialchars($sponsor['name']); ?>">
                    </div>
                    <div class="sponsor-info">
                        <h3 class="sponsor-name"><?php echo $current_lang === 'ar' ? $sponsor['name_ar'] : $sponsor['name']; ?></h3>
                        <div class="sponsor-description">
                            <?php
                            $description = $current_lang === 'ar' ?
                                (isset($sponsor['description_ar']) ? $sponsor['description_ar'] : '') :
                                (isset($sponsor['description']) ? $sponsor['description'] : '');
                            echo mb_substr($description, 0, 100) . (mb_strlen($description) > 100 ? '...' : '');
                            ?>
                        </div>
                        <div class="sponsor-meta">
                            <div class="sponsor-category">
                                <?php echo get_content('sponsors', 'silver', 'Silver Sponsors'); ?>
                            </div>
                            <?php if (!empty($sponsor['website'])): ?>
                            <div class="sponsor-actions">
                                <a href="<?php echo $sponsor['website']; ?>" target="_blank" class="btn-website">
                                    <i class="fas fa-globe"></i> <?php echo get_content('sponsors', 'visit_website', 'Visit Website'); ?>
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Media Partners -->
        <?php if (!empty($media_partners)): ?>
        <div class="category-container">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-newspaper"></i>
                </div>
                <h2 class="category-title"><?php echo get_content('sponsors', 'media', 'Media Partners'); ?></h2>
            </div>
            <div class="sponsors-grid">
                <?php foreach ($media_partners as $sponsor): ?>
                <div class="sponsor-card">
                    <div class="sponsor-logo">
                        <img src="<?php echo asset_url('uploads/' . (isset($sponsor['logo']) ? $sponsor['logo'] : 'default-sponsor.png')); ?>" alt="<?php echo htmlspecialchars($sponsor['name']); ?>">
                    </div>
                    <div class="sponsor-info">
                        <h3 class="sponsor-name"><?php echo $current_lang === 'ar' ? $sponsor['name_ar'] : $sponsor['name']; ?></h3>
                        <div class="sponsor-description">
                            <?php
                            $description = $current_lang === 'ar' ?
                                (isset($sponsor['description_ar']) ? $sponsor['description_ar'] : '') :
                                (isset($sponsor['description']) ? $sponsor['description'] : '');
                            echo mb_substr($description, 0, 100) . (mb_strlen($description) > 100 ? '...' : '');
                            ?>
                        </div>
                        <div class="sponsor-meta">
                            <div class="sponsor-category">
                                <?php echo get_content('sponsors', 'media', 'Media Partners'); ?>
                            </div>
                            <?php if (!empty($sponsor['website'])): ?>
                            <div class="sponsor-actions">
                                <a href="<?php echo $sponsor['website']; ?>" target="_blank" class="btn-website">
                                    <i class="fas fa-globe"></i> <?php echo get_content('sponsors', 'visit_website', 'Visit Website'); ?>
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Become a Sponsor -->
        <div class="become-sponsor">
            <h2 class="become-sponsor-title"><?php echo get_content('sponsors', 'become_sponsor', 'Become a Sponsor'); ?></h2>
            <p class="become-sponsor-description"><?php echo get_content('sponsors', 'become_sponsor_desc', 'Interested in sponsoring the Social Media Festival? Contact us to learn about our sponsorship packages and benefits.'); ?></p>
            <a href="<?php echo base_url('?page=contact'); ?>" class="btn-become-sponsor">
                <i class="fas fa-handshake"></i> <?php echo get_content('sponsors', 'contact_us', 'Contact Us'); ?>
            </a>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animations to sponsor cards
    const sponsorCards = document.querySelectorAll('.sponsor-card, .platinum-card');

    sponsorCards.forEach((card, index) => {
        // Stagger animation delay
        const delay = index * 0.1;

        // Apply animation with GSAP
        gsap.from(card, {
            y: 50,
            duration: 0.8,
            delay: delay,
            ease: 'power2.out'
        });
    });

    // Add animation to become sponsor section
    gsap.from('.become-sponsor', {
        y: 30,
        duration: 1,
        delay: 0.5,
        scrollTrigger: {
            trigger: '.become-sponsor',
            start: 'top 80%'
        }
    });
});
</script>
