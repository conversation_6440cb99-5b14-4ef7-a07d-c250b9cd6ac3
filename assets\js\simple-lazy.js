/**
 * Simple Lazy Loading System
 * نظام تحميل بطيء بسيط وفعال
 */

(function() {
    'use strict';

    // Simple lazy loading for gallery images only
    function initSimpleLazy() {
        const lazyImages = document.querySelectorAll('.gallery-lazy');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        loadImage(img);
                        imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px'
            });

            lazyImages.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            lazyImages.forEach(img => loadImage(img));
        }
    }

    function loadImage(img) {
        const src = img.dataset.src;
        if (!src) return;

        // Create new image to preload
        const imageLoader = new Image();
        
        imageLoader.onload = function() {
            img.src = src;
            img.classList.add('loaded');
            img.removeAttribute('data-src');
        };

        imageLoader.onerror = function() {
            img.classList.add('error');
        };

        imageLoader.src = src;
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSimpleLazy);
    } else {
        initSimpleLazy();
    }

    // Add CSS for smooth transitions
    const style = document.createElement('style');
    style.textContent = `
        .gallery-lazy {
            transition: opacity 0.3s ease;
            background: #f5f5f5;
        }
        
        .gallery-lazy.loaded {
            opacity: 1;
        }
        
        .gallery-lazy.error {
            opacity: 0.5;
            filter: grayscale(100%);
        }
    `;
    document.head.appendChild(style);

})();
