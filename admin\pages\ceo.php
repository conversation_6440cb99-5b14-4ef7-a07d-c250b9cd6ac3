<?php
/**
 * CEO Management Page
 */

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = $_POST['name'] ?? '';
    $position = $_POST['position'] ?? '';
    $position_ar = $_POST['position_ar'] ?? '';
    $bio = $_POST['bio'] ?? '';
    $bio_ar = $_POST['bio_ar'] ?? '';
    $facebook = $_POST['facebook'] ?? '';
    $twitter = $_POST['twitter'] ?? '';
    $instagram = $_POST['instagram'] ?? '';
    $linkedin = $_POST['linkedin'] ?? '';
    
    // Handle image upload
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/uploads/';
        $temp_name = $_FILES['image']['tmp_name'];
        $original_name = $_FILES['image']['name'];
        $extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $new_name = 'ceo_' . time() . '.' . $extension;
        
        if (move_uploaded_file($temp_name, $upload_dir . $new_name)) {
            $image = $new_name;
        }
    }
    
    // Check if CEO record exists
    $check_sql = "SELECT * FROM ceo LIMIT 1";
    $check_result = $conn->query($check_sql);
    
    if ($check_result && $check_result->num_rows > 0) {
        // Update existing record
        $update_sql = "UPDATE ceo SET 
                      name = ?, 
                      position = ?, 
                      position_ar = ?, 
                      bio = ?, 
                      bio_ar = ?, 
                      facebook = ?, 
                      twitter = ?, 
                      instagram = ?, 
                      linkedin = ?";
        
        // Only update image if a new one was uploaded
        if (!empty($image)) {
            $update_sql .= ", image = ?";
        }
        
        $update_sql .= " LIMIT 1";
        
        $stmt = $conn->prepare($update_sql);
        
        if (!empty($image)) {
            $stmt->bind_param("sssssssss", $name, $position, $position_ar, $bio, $bio_ar, $facebook, $twitter, $instagram, $linkedin, $image);
        } else {
            $stmt->bind_param("sssssssss", $name, $position, $position_ar, $bio, $bio_ar, $facebook, $twitter, $instagram, $linkedin);
        }
        
        if ($stmt->execute()) {
            $success_message = "تم تحديث بيانات الرئيس التنفيذي بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء تحديث البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    } else {
        // Insert new record
        $insert_sql = "INSERT INTO ceo (name, position, position_ar, bio, bio_ar, facebook, twitter, instagram, linkedin, image) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("ssssssssss", $name, $position, $position_ar, $bio, $bio_ar, $facebook, $twitter, $instagram, $linkedin, $image);
        
        if ($stmt->execute()) {
            $success_message = "تم إضافة بيانات الرئيس التنفيذي بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء إضافة البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    }
}

// Get CEO data
$ceo = get_ceo_info();
?>

<div class="page-header">
    <h1><i class="fas fa-user-tie"></i> إدارة الرئيس التنفيذي</h1>
    <p>إدارة معلومات الرئيس التنفيذي للمهرجان</p>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success">
    <?php echo $success_message; ?>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger">
    <?php echo $error_message; ?>
</div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h2>بيانات الرئيس التنفيذي</h2>
    </div>
    <div class="card-body">
        <form action="" method="post" enctype="multipart/form-data">
            <div class="form-group">
                <label for="name">الاسم</label>
                <input type="text" id="name" name="name" class="form-control" value="<?php echo $ceo ? htmlspecialchars($ceo['name']) : ''; ?>" required>
            </div>
            
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="position">المنصب (الإنجليزية)</label>
                    <input type="text" id="position" name="position" class="form-control" value="<?php echo $ceo ? htmlspecialchars($ceo['position']) : ''; ?>" required>
                </div>
                <div class="form-group col-md-6">
                    <label for="position_ar">المنصب (العربية)</label>
                    <input type="text" id="position_ar" name="position_ar" class="form-control" value="<?php echo $ceo ? htmlspecialchars($ceo['position_ar']) : ''; ?>" required>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="bio">نبذة تعريفية (الإنجليزية)</label>
                    <textarea id="bio" name="bio" class="form-control" rows="5"><?php echo $ceo ? htmlspecialchars($ceo['bio']) : ''; ?></textarea>
                </div>
                <div class="form-group col-md-6">
                    <label for="bio_ar">نبذة تعريفية (العربية)</label>
                    <textarea id="bio_ar" name="bio_ar" class="form-control" rows="5"><?php echo $ceo ? htmlspecialchars($ceo['bio_ar']) : ''; ?></textarea>
                </div>
            </div>
            
            <div class="form-group">
                <label for="image">الصورة الشخصية</label>
                <?php if ($ceo && !empty($ceo['image'])): ?>
                <div class="current-image">
                    <img src="<?php echo asset_url('uploads/' . $ceo['image']); ?>" alt="<?php echo htmlspecialchars($ceo['name']); ?>" style="max-width: 200px; margin-bottom: 10px;">
                    <p>الصورة الحالية</p>
                </div>
                <?php endif; ?>
                <input type="file" id="image" name="image" class="form-control-file" <?php echo !$ceo ? 'required' : ''; ?>>
                <small class="form-text text-muted">يفضل صورة بأبعاد 600×800 بكسل</small>
            </div>
            
            <h3 class="mt-4">روابط التواصل الاجتماعي</h3>
            
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="facebook"><i class="fab fa-facebook"></i> فيسبوك</label>
                    <input type="url" id="facebook" name="facebook" class="form-control" value="<?php echo $ceo ? htmlspecialchars($ceo['facebook']) : ''; ?>">
                </div>
                <div class="form-group col-md-6">
                    <label for="twitter"><i class="fab fa-twitter"></i> تويتر</label>
                    <input type="url" id="twitter" name="twitter" class="form-control" value="<?php echo $ceo ? htmlspecialchars($ceo['twitter']) : ''; ?>">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group col-md-6">
                    <label for="instagram"><i class="fab fa-instagram"></i> انستغرام</label>
                    <input type="url" id="instagram" name="instagram" class="form-control" value="<?php echo $ceo ? htmlspecialchars($ceo['instagram']) : ''; ?>">
                </div>
                <div class="form-group col-md-6">
                    <label for="linkedin"><i class="fab fa-linkedin"></i> لينكد إن</label>
                    <input type="url" id="linkedin" name="linkedin" class="form-control" value="<?php echo $ceo ? htmlspecialchars($ceo['linkedin']) : ''; ?>">
                </div>
            </div>
            
            <div class="form-group text-center">
                <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ البيانات</button>
            </div>
        </form>
    </div>
</div>
