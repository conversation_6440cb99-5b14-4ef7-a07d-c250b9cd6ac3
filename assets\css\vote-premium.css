/* Premium Vote Page Styles */
:root {
    --premium-gold: #ffd700;
    --premium-gold-light: #ffe44d;
    --premium-gold-dark: #ccac00;
    --premium-black: #000000;
    --premium-dark: #0a0a0a;
    --premium-gray: #1a1a1a;
    --premium-light: #ffffff;
    --premium-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    --premium-gold-shadow: 0 5px 20px rgba(255, 215, 0, 0.3);
    --premium-border-radius: 12px;
    --premium-transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.vote-section {
    padding: 0;
    background: #000000;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.vote-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(255, 215, 0, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(255, 215, 0, 0.05), transparent 70%);
    pointer-events: none;
}

.container {
    max-width: 100%;
    padding: 0;
}

.alert {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: var(--premium-shadow);
    width: auto;
    max-width: 90%;
    text-align: center;
    animation: fadeOut 0.5s ease 3s forwards;
    border: none;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; visibility: hidden; }
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.9);
    color: white;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.9);
    color: white;
}

/* Main Categories Section */
.main-categories-section {
    position: sticky;
    top: 60px;
    z-index: 100;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.85));
    padding: 0;
    box-shadow: var(--premium-shadow);
}

.main-categories-slider {
    overflow: hidden;
    position: relative;
}

.categories-scroll {
    display: flex;
    overflow-x: auto;
    padding: 15px 10px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}

.categories-scroll::-webkit-scrollbar {
    display: none;
}

.main-category-item {
    flex: 0 0 auto;
    width: 100px;
    margin-right: 15px;
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    border-radius: 50%;
    padding: 0;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: var(--premium-transition);
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    box-shadow: var(--premium-shadow);
}

.main-category-item:hover {
    transform: translateY(-5px);
    border-color: var(--premium-gold);
    box-shadow: var(--premium-gold-shadow);
}

.main-category-item.active {
    border: 2px solid var(--premium-gold);
    box-shadow: var(--premium-gold-shadow);
}

.category-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background-color: #222;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.category-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.main-category-item:hover .category-icon img {
    transform: scale(1.1);
}

.category-icon i {
    font-size: 2rem;
    color: var(--premium-gold);
}

/* Voting Content */
.voting-content {
    padding: 20px 15px;
    position: relative;
}

.voting-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../img/pattern.png');
    opacity: 0.02;
    pointer-events: none;
}

/* Subcategories Section */
.subcategories-section {
    margin-bottom: 20px;
    background: linear-gradient(145deg, rgba(20, 20, 20, 0.7), rgba(10, 10, 10, 0.9));
    padding: 15px;
    border-radius: var(--premium-border-radius);
    border: 1px solid rgba(255, 215, 0, 0.1);
    box-shadow: var(--premium-shadow);
    position: relative;
    overflow: hidden;
}

.subcategories-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(to right, transparent, rgba(10, 10, 10, 0.9));
    pointer-events: none;
    z-index: 1;
}

.subcategories-slider {
    overflow: hidden;
    position: relative;
}

.subcategories-scroll {
    display: flex;
    overflow-x: auto;
    padding: 10px 0;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}

.subcategories-scroll::-webkit-scrollbar {
    display: none;
}

.subcategory-item {
    flex: 0 0 auto;
    width: 90px;
    height: 90px;
    margin-right: 15px;
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    border-radius: 50%;
    padding: 0;
    text-align: center;
    text-decoration: none;
    color: white;
    transition: var(--premium-transition);
    border: 2px solid transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
}

.subcategory-item:hover {
    transform: translateY(-5px) scale(1.05);
    border-color: var(--premium-gold);
    box-shadow: var(--premium-gold-shadow);
}

.subcategory-item.active {
    border: 2px solid var(--premium-gold);
    box-shadow: var(--premium-gold-shadow);
}

.subcategory-icon {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    overflow: hidden;
}

.subcategory-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.subcategory-item:hover .subcategory-icon img {
    transform: scale(1.1);
}

.subcategory-icon i {
    font-size: 1.5rem;
    color: var(--premium-gold);
}

/* Nominees Section */
.nominees-section {
    margin-bottom: 20px;
    background: linear-gradient(145deg, rgba(20, 20, 20, 0.7), rgba(10, 10, 10, 0.9));
    padding: 20px;
    border-radius: var(--premium-border-radius);
    border: 1px solid rgba(255, 215, 0, 0.1);
    box-shadow: var(--premium-shadow);
    position: relative;
    overflow: hidden;
}

.nominees-section::before {
    content: '';
    position: absolute;
    top: -50px;
    right: -50px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.1), transparent 70%);
    border-radius: 50%;
    pointer-events: none;
}

.nominees-container {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;
    scrollbar-width: thin;
    scrollbar-color: var(--premium-gold) #333;
}

.nominees-container::-webkit-scrollbar {
    width: 5px;
}

.nominees-container::-webkit-scrollbar-track {
    background: #333;
    border-radius: 5px;
}

.nominees-container::-webkit-scrollbar-thumb {
    background-color: var(--premium-gold);
    border-radius: 5px;
}

.nominees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 20px;
    margin-top: 10px;
}
