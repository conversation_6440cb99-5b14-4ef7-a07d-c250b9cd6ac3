/**
 * Enhanced Dashboard JavaScript
 * Adds interactive features to the admin dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips for action buttons
    const tooltipButtons = document.querySelectorAll('[title]');
    tooltipButtons.forEach(button => {
        // We're using the title attribute as a simple tooltip
        // In a production environment, you might want to use a proper tooltip library
        const title = button.getAttribute('title');
        button.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = title;
            document.body.appendChild(tooltip);
            
            const rect = button.getBoundingClientRect();
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';
            tooltip.style.left = (rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)) + 'px';
            
            setTimeout(() => {
                tooltip.classList.add('show');
            }, 10);
        });
        
        button.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.tooltip');
            if (tooltip) {
                tooltip.classList.remove('show');
                setTimeout(() => {
                    tooltip.remove();
                }, 200);
            }
        });
    });
    
    // Add refresh functionality to chart refresh buttons
    const refreshButtons = document.querySelectorAll('.chart-actions .fa-sync-alt');
    refreshButtons.forEach(button => {
        button.addEventListener('click', function() {
            const chartContainer = this.closest('.chart-container');
            const chartCanvas = chartContainer.querySelector('canvas');
            
            // Add spinning animation to the refresh button
            this.classList.add('fa-spin');
            
            // Simulate a refresh delay
            setTimeout(() => {
                // In a real application, you would fetch new data here
                // and update the chart
                
                // For demonstration, we'll just stop the spinning
                this.classList.remove('fa-spin');
                
                // Show a success message
                const successMessage = document.createElement('div');
                successMessage.className = 'refresh-success';
                successMessage.innerHTML = '<i class="fas fa-check"></i> تم التحديث';
                chartContainer.appendChild(successMessage);
                
                setTimeout(() => {
                    successMessage.classList.add('show');
                }, 10);
                
                setTimeout(() => {
                    successMessage.classList.remove('show');
                    setTimeout(() => {
                        successMessage.remove();
                    }, 300);
                }, 2000);
            }, 1000);
        });
    });
    
    // Add export functionality to chart export buttons
    const exportButtons = document.querySelectorAll('.chart-actions .fa-download');
    exportButtons.forEach(button => {
        button.addEventListener('click', function() {
            const chartContainer = this.closest('.chart-container');
            const chartCanvas = chartContainer.querySelector('canvas');
            const chartTitle = chartContainer.querySelector('h2').textContent;
            
            // In a real application, you would implement proper chart export
            // For demonstration, we'll just show an alert
            alert('تصدير الرسم البياني: ' + chartTitle);
            
            // You could use the following code to actually export the chart as an image
            /*
            const link = document.createElement('a');
            link.download = chartTitle + '.png';
            link.href = chartCanvas.toDataURL('image/png');
            link.click();
            */
        });
    });
    
    // Add animation to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animated');
        }, index * 100);
    });
    
    // Add animation to progress bars
    const progressBars = document.querySelectorAll('.progress-bar');
    setTimeout(() => {
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0';
            setTimeout(() => {
                bar.style.width = width;
            }, 100);
        });
    }, 500);
    
    // Add print functionality
    const printButton = document.querySelector('.dashboard-actions button');
    if (printButton) {
        printButton.addEventListener('click', function() {
            // You could customize the print view here
            window.print();
        });
    }
    
    // Add table row hover effect
    const tableRows = document.querySelectorAll('.data-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.classList.add('highlight');
        });
        
        row.addEventListener('mouseleave', function() {
            this.classList.remove('highlight');
        });
    });
    
    // Add animation to welcome banner
    const welcomeBanner = document.querySelector('.welcome-banner');
    if (welcomeBanner) {
        setTimeout(() => {
            welcomeBanner.classList.add('animated');
        }, 300);
    }
});

// Add CSS for the tooltip and animations
const style = document.createElement('style');
style.textContent = `
    .tooltip {
        position: fixed;
        background-color: var(--color-black);
        color: var(--color-white);
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1000;
        opacity: 0;
        transform: translateY(10px);
        transition: opacity 0.2s, transform 0.2s;
        pointer-events: none;
        border: 1px solid var(--color-gray);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    }
    
    .tooltip.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .refresh-success {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.8);
        background-color: rgba(40, 167, 69, 0.9);
        color: white;
        padding: 10px 15px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        opacity: 0;
        transition: all 0.3s;
    }
    
    .refresh-success.show {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    
    .refresh-success i {
        margin-right: 5px;
    }
    
    .stat-card {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.5s, transform 0.5s;
    }
    
    .stat-card.animated {
        opacity: 1;
        transform: translateY(0);
    }
    
    .welcome-banner {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.5s, transform 0.5s;
    }
    
    .welcome-banner.animated {
        opacity: 1;
        transform: translateY(0);
    }
    
    .data-table tbody tr {
        transition: background-color 0.3s;
    }
    
    .data-table tbody tr.highlight {
        background-color: rgba(212, 175, 55, 0.05);
    }
    
    /* Print styles */
    @media print {
        .admin-sidebar,
        .admin-header,
        .admin-footer,
        .quick-actions,
        .table-actions,
        .chart-actions,
        .table-footer,
        .btn {
            display: none !important;
        }
        
        .dashboard {
            padding: 0 !important;
        }
        
        .dashboard-header {
            margin-bottom: 20px !important;
        }
        
        .welcome-banner {
            background: none !important;
            border: none !important;
            box-shadow: none !important;
            padding: 0 !important;
        }
        
        .welcome-content h2 {
            color: #000 !important;
        }
        
        .welcome-content p {
            color: #333 !important;
        }
        
        .welcome-image {
            display: none !important;
        }
        
        .section-title {
            color: #000 !important;
            border-bottom: 1px solid #ccc !important;
        }
        
        .stat-card,
        .chart-container,
        .table-container,
        .status-card {
            background: none !important;
            border: 1px solid #ccc !important;
            box-shadow: none !important;
            break-inside: avoid !important;
        }
        
        .stat-content h3,
        .chart-container h2,
        .table-container h2,
        .status-content h3 {
            color: #000 !important;
        }
        
        .stat-number {
            color: #333 !important;
        }
        
        .data-table th {
            background-color: #f0f0f0 !important;
            color: #000 !important;
        }
        
        .data-table td {
            color: #333 !important;
        }
        
        .category-badge {
            background: none !important;
            border: 1px solid #ccc !important;
            color: #333 !important;
        }
    }
`;
document.head.appendChild(style);
