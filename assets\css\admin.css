/**
 * Admin CSS for Social Media Festival
 */

/* ===== Base Styles ===== */
:root {
    /* Colors */
    --color-black: #000000;
    --color-dark: #121212;
    --color-dark-gray: #1e1e1e;
    --color-gray: #333333;
    --color-light-gray: #444444;
    --color-gold: #d4af37;
    --color-gold-light: #f1e5ac;
    --color-gold-dark: #aa8c2c;
    --color-white: #ffffff;
    --color-red: #e74c3c;
    --color-green: #2ecc71;
    --color-blue: #3498db;

    /* Typography */
    --font-primary: 'Poppins', sans-serif;
    --font-arabic: 'Cairo', sans-serif;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;

    /* Border Radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 16px;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Admin Specific */
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
    --header-height: 60px;

    /* Shadows */
    --shadow-sm: 0 2px 5px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 5px 15px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 30px rgba(0, 0, 0, 0.5);
    --shadow-gold: 0 5px 15px rgba(212, 175, 55, 0.3);
}

/* Page Transition Effect */
body {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

body.page-loaded {
    opacity: 1;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    color: var(--color-white);
    background-color: var(--color-dark);
    line-height: 1.6;
}

[lang="ar"] body {
    font-family: var(--font-arabic);
}

a {
    color: var(--color-gold);
    text-decoration: none;
    transition: color var(--transition-normal);
}

a:hover {
    color: var(--color-gold-light);
}

img {
    max-width: 100%;
    height: auto;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 {
    font-size: 2rem;
}

h2 {
    font-size: 1.5rem;
}

h3 {
    font-size: 1.25rem;
}

h4 {
    font-size: 1rem;
}

h5 {
    font-size: 0.875rem;
}

h6 {
    font-size: 0.75rem;
}

p {
    margin-bottom: var(--spacing-md);
}

/* ===== Buttons ===== */
.btn {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    border: none;
}

.btn-primary {
    background-color: var(--color-gold);
    color: var(--color-black);
}

.btn-primary:hover {
    background-color: var(--color-gold-light);
    color: var(--color-black);
}

.btn-secondary {
    background-color: var(--color-gray);
    color: var(--color-white);
}

.btn-secondary:hover {
    background-color: var(--color-light-gray);
}

.btn-danger {
    background-color: var(--color-red);
    color: var(--color-white);
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-success {
    background-color: var(--color-green);
    color: var(--color-white);
}

.btn-success:hover {
    background-color: #27ae60;
}

.btn-outline {
    background-color: transparent;
    color: var(--color-gold);
    border: 1px solid var(--color-gold);
}

.btn-outline:hover {
    background-color: var(--color-gold);
    color: var(--color-black);
}

.btn-link {
    background-color: transparent;
    color: var(--color-gold);
    padding: 0;
}

.btn-link:hover {
    text-decoration: underline;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.125rem;
}

/* ===== Forms ===== */
.form-group {
    margin-bottom: var(--spacing-md);
}

label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="date"],
input[type="time"],
input[type="search"],
select,
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--color-light-gray);
    border-radius: var(--border-radius-md);
    background-color: var(--color-dark-gray);
    color: var(--color-white);
    font-family: inherit;
    font-size: 1rem;
    transition: border-color var(--transition-normal);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="date"]:focus,
input[type="time"]:focus,
input[type="search"]:focus,
select:focus,
textarea:focus {
    border-color: var(--color-gold);
    outline: none;
}

.input-icon {
    position: relative;
}

.input-icon i {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    color: var(--color-light-gray);
}

.input-icon input {
    padding-left: 2.5rem;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-lg);
}

/* ===== Alerts ===== */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.alert-success {
    background-color: rgba(46, 204, 113, 0.2);
    border: 1px solid var(--color-green);
    color: var(--color-green);
}

.alert-danger {
    background-color: rgba(231, 76, 60, 0.2);
    border: 1px solid var(--color-red);
    color: var(--color-red);
}

.alert-info {
    background-color: rgba(52, 152, 219, 0.2);
    border: 1px solid var(--color-blue);
    color: var(--color-blue);
}

/* ===== Admin Layout ===== */
.admin-wrapper {
    display: flex;
    min-height: 100vh;
}

/* ===== Admin Sidebar ===== */
.admin-sidebar {
    width: var(--sidebar-width);
    background-color: var(--color-black);
    border-right: 1px solid var(--color-gray);
    transition: width var(--transition-normal);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 100;
    overflow-y: auto;
}

.admin-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-gray);
    background-color: rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.sidebar-header .logo img {
    height: 40px;
    transition: transform var(--transition-normal);
}

.sidebar-header .logo:hover img {
    transform: scale(1.05);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--color-white);
    font-size: 1.25rem;
    cursor: pointer;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 2px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    color: var(--color-white);
    transition: all var(--transition-normal);
    border-radius: var(--border-radius-sm);
    margin: 0 var(--spacing-xs);
}

.sidebar-nav a:hover {
    background-color: var(--color-gray);
    color: var(--color-gold);
    transform: translateX(5px);
}

.sidebar-nav li.active a {
    background-color: var(--color-gold);
    color: var(--color-black);
    box-shadow: 0 2px 5px rgba(212, 175, 55, 0.3);
}

.sidebar-nav i {
    margin-right: var(--spacing-md);
    font-size: 1.25rem;
    width: 20px;
    text-align: center;
    transition: transform var(--transition-normal);
}

.sidebar-nav a:hover i {
    transform: scale(1.2);
}

.admin-sidebar.collapsed .sidebar-nav span {
    display: none;
}

/* Dropdown menu in sidebar */
.sidebar-nav .dropdown-toggle {
    position: relative;
    cursor: pointer;
}

.sidebar-nav .dropdown-toggle::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    transition: transform var(--transition-normal);
}

.sidebar-nav .dropdown-toggle.active::after {
    transform: translateY(-50%) rotate(180deg);
}

.sidebar-nav .dropdown-menu {
    display: none;
    overflow: hidden;
    max-height: 0;
    transition: max-height var(--transition-normal);
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-sm);
    margin: 0 var(--spacing-xs);
    position: static;
    box-shadow: none;
    border: none;
    padding: 0;
    transform: none;
    opacity: 1;
}

.sidebar-nav .dropdown-menu li {
    margin-bottom: 0;
}

.sidebar-nav .dropdown-menu a {
    padding-left: calc(var(--spacing-md) + 20px);
    font-size: 0.95rem;
}

.sidebar-nav .dropdown-menu a i {
    font-size: 1rem;
}

/* RTL support for dropdown */
html[dir="rtl"] .sidebar-nav .dropdown-toggle::after {
    right: auto;
    left: 15px;
}

html[dir="rtl"] .sidebar-nav .dropdown-menu a {
    padding-left: var(--spacing-md);
    padding-right: calc(var(--spacing-md) + 20px);
}

/* ===== Admin Main Content ===== */
.admin-main {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-normal);
}

.admin-wrapper.collapsed .admin-main {
    margin-left: var(--sidebar-collapsed-width);
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    background-color: var(--color-black);
    border-bottom: 1px solid var(--color-gray);
    height: var(--header-height);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    position: sticky;
    top: 0;
    z-index: 99;
}

.header-search form {
    display: flex;
}

.header-search input {
    width: 300px;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
    border: 1px solid var(--color-gray);
    background-color: var(--color-dark-gray);
    color: var(--color-white);
    transition: all var(--transition-normal);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.header-search input:focus {
    width: 350px;
    border-color: var(--color-gold);
    box-shadow: inset 0 1px 3px rgba(212, 175, 55, 0.3);
}

.header-search button {
    padding: 0.5rem 1rem;
    background-color: var(--color-gold);
    color: var(--color-black);
    border: none;
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.header-search button:hover {
    background-color: var(--color-gold-light);
    transform: translateX(2px);
}

.header-actions {
    display: flex;
    align-items: center;
}

.language-switcher {
    margin-right: var(--spacing-md);
    display: flex;
    align-items: center;
}

.lang-item {
    display: flex;
    align-items: center;
    margin-left: var(--spacing-sm);
    color: var(--color-white);
    padding: 5px 8px;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-normal);
}

.lang-item:hover {
    background-color: var(--color-gray);
    transform: translateY(-2px);
}

.lang-item.active {
    color: var(--color-gold);
    background-color: rgba(212, 175, 55, 0.1);
    border: 1px solid var(--color-gold);
}

.lang-item img {
    width: 16px;
    height: 12px;
    margin-right: var(--spacing-xs);
    border-radius: 2px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.user-menu {
    position: relative;
}

.user-toggle {
    display: flex;
    align-items: center;
    color: var(--color-white);
    padding: 5px 10px;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
}

.user-toggle:hover {
    background-color: var(--color-gray);
}

.user-toggle i {
    margin-right: var(--spacing-xs);
    font-size: 1.5rem;
    color: var(--color-gold);
}

.user-menu .dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background-color: var(--color-dark);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    display: none;
    z-index: 100;
    border: 1px solid var(--color-gray);
    transform: translateY(10px);
    opacity: 0;
    transition: all var(--transition-normal);
}

.user-menu .dropdown-menu:before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 10px;
    height: 10px;
    background-color: var(--color-dark);
    transform: rotate(45deg);
    border-top: 1px solid var(--color-gray);
    border-left: 1px solid var(--color-gray);
}

.user-menu:hover .dropdown-menu {
    display: block;
    transform: translateY(0);
    opacity: 1;
}

.user-menu .dropdown-menu li {
    list-style: none;
    margin-bottom: var(--spacing-sm);
}

.user-menu .dropdown-menu a {
    color: var(--color-white);
    display: block;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-normal);
}

.user-menu .dropdown-menu a:hover {
    color: var(--color-gold);
    background-color: var(--color-black);
    padding-left: var(--spacing-md);
}

.user-menu .dropdown-menu i {
    margin-right: var(--spacing-sm);
    width: 20px;
    text-align: center;
    color: var(--color-gold);
}

.admin-content {
    padding: var(--spacing-lg);
    min-height: calc(100vh - var(--header-height) - 60px); /* Ensure content fills the page */
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.admin-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    text-align: center;
    color: var(--color-light-gray);
    border-top: 1px solid var(--color-gray);
    background-color: var(--color-black);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.admin-footer p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.admin-footer a {
    color: var(--color-gold);
    transition: all var(--transition-normal);
}

.admin-footer a:hover {
    color: var(--color-gold-light);
    text-decoration: underline;
}

/* ===== Admin Login Page ===== */
.admin-login-page {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background-color: var(--color-dark);
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: var(--spacing-lg);
    background-color: var(--color-black);
    border-radius: var(--border-radius-lg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.login-logo {
    text-align: center;
    margin-bottom: var(--spacing-lg);
}

.login-logo img {
    height: 60px;
}

.login-form-container h1 {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--color-gold);
}

/* ===== Dashboard ===== */
.page-title {
    margin-bottom: var(--spacing-lg);
    color: var(--color-gold);
    font-size: 2.2rem;
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
}

.page-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--color-gold);
    border-radius: 3px;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.stat-card {
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    align-items: center;
    transition: all var(--transition-normal);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--color-gray);
    overflow: hidden;
    position: relative;
}

.stat-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 5px;
    height: 100%;
    background-color: var(--color-gold);
    opacity: 0.7;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: var(--color-gold);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background-color: var(--color-gold);
    color: var(--color-black);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: var(--spacing-md);
    box-shadow: 0 3px 10px rgba(212, 175, 55, 0.3);
    transition: all var(--transition-normal);
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(10deg);
}

.stat-content h3 {
    margin-bottom: 0;
    font-size: 1rem;
    color: var(--color-white);
    transition: all var(--transition-normal);
}

.stat-card:hover .stat-content h3 {
    color: var(--color-gold);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--color-gold);
    margin-bottom: 0;
    text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.chart-container {
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    height: 300px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--color-gray);
    transition: all var(--transition-normal);
}

.chart-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: var(--color-gold);
}

.chart-container h2 {
    margin-bottom: var(--spacing-md);
    font-size: 1.25rem;
    color: var(--color-gold);
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
}

.chart-container h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--color-gold);
    border-radius: 2px;
}

.dashboard-tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

.table-container {
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--color-gray);
    transition: all var(--transition-normal);
}

.table-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: var(--color-gold);
}

.table-container h2 {
    margin-bottom: var(--spacing-md);
    font-size: 1.25rem;
    color: var(--color-gold);
    position: relative;
    display: inline-block;
    padding-bottom: 5px;
}

.table-container h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 30px;
    height: 2px;
    background-color: var(--color-gold);
    border-radius: 2px;
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.data-table th,
.data-table td {
    padding: var(--spacing-sm);
    text-align: left;
    border-bottom: 1px solid var(--color-gray);
}

.data-table th {
    font-weight: 700;
    color: var(--color-gold);
    background-color: rgba(0, 0, 0, 0.3);
    position: sticky;
    top: 0;
}

.data-table th:first-child {
    border-top-left-radius: var(--border-radius-sm);
}

.data-table th:last-child {
    border-top-right-radius: var(--border-radius-sm);
}

.data-table tr:hover {
    background-color: var(--color-dark-gray);
}

.data-table tr:last-child td:first-child {
    border-bottom-left-radius: var(--border-radius-sm);
}

.data-table tr:last-child td:last-child {
    border-bottom-right-radius: var(--border-radius-sm);
}

.table-footer {
    margin-top: var(--spacing-md);
    text-align: center;
}

.table-footer .btn {
    transition: all var(--transition-normal);
}

.table-footer .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.no-data {
    text-align: center;
    color: var(--color-light-gray);
    padding: var(--spacing-lg) 0;
    font-style: italic;
}

/* ===== Content Management ===== */
.content-tabs {
    display: flex;
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--color-gray);
}

.content-tab {
    padding: var(--spacing-sm) var(--spacing-md);
    cursor: pointer;
    border-bottom: 2px solid transparent;
    margin-right: var(--spacing-md);
}

.content-tab.active {
    border-bottom-color: var(--color-gold);
    color: var(--color-gold);
}

.content-panel {
    display: none;
}

.content-panel.active {
    display: block;
}

.content-item {
    background-color: var(--color-black);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.content-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.content-item-title {
    font-weight: 700;
    margin-bottom: 0;
}

.content-item-actions {
    display: flex;
}

.content-item-actions .btn {
    margin-left: var(--spacing-xs);
}

.content-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.content-form .form-group:last-child {
    grid-column: span 2;
}

/* ===== Media Queries ===== */
@media (max-width: 992px) {
    .admin-sidebar {
        width: var(--sidebar-collapsed-width);
    }

    .admin-sidebar .sidebar-nav span {
        display: none;
    }

    .admin-main {
        margin-left: var(--sidebar-collapsed-width);
    }

    .admin-sidebar.expanded {
        width: var(--sidebar-width);
        z-index: 1000;
    }

    .admin-sidebar.expanded .sidebar-nav span {
        display: inline;
    }

    .dashboard-charts,
    .dashboard-tables {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .header-search input {
        width: 200px;
    }

    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }

    .content-form {
        grid-template-columns: 1fr;
    }

    .content-form .form-group:last-child {
        grid-column: span 1;
    }
}

@media (max-width: 576px) {
    .admin-header {
        flex-direction: column;
        height: auto;
        padding: var(--spacing-sm);
    }

    .header-search {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }

    .header-search form {
        width: 100%;
    }

    .header-search input {
        width: 100%;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .stats-cards {
        grid-template-columns: 1fr;
    }

    .admin-content {
        padding: var(--spacing-md);
    }
}

/* ===== Modals ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    overflow-y: auto;
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: var(--color-dark);
    margin: 50px auto;
    padding: 0;
    width: 90%;
    max-width: 600px;
    border-radius: var(--border-radius-md);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    position: relative;
    border: 1px solid var(--color-gray);
    animation: slideIn 0.3s ease-out;
    overflow: hidden;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--color-gray);
    background-color: rgba(0, 0, 0, 0.3);
}

.modal-header h2 {
    margin-bottom: 0;
    font-size: 1.5rem;
    color: var(--color-gold);
}

.modal-close {
    background: none;
    border: none;
    color: var(--color-white);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--color-gold);
    transform: rotate(90deg);
}

.modal-body {
    padding: var(--spacing-md);
}

.modal-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--color-gray);
    display: flex;
    justify-content: flex-end;
    background-color: rgba(0, 0, 0, 0.2);
}

/* Page Header Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--color-dark) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-gray);
}

.page-title h1 {
    color: var(--color-gold);
    font-size: 2rem;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-title p {
    color: var(--color-light-gray);
    margin: 0;
    font-size: 1rem;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

/* Content Wrapper */
.content-wrapper {
    padding: 0 1.5rem;
}

.podcasts-container {
    max-width: 100%;
}

/* Podcasts Grid */
.podcasts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
    margin-top: 1rem;
}

/* Podcast Card */
.podcast-card {
    background: var(--color-dark);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    border: 1px solid var(--color-gray);
    position: relative;
}

.podcast-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(212, 175, 55, 0.2);
    border-color: var(--color-gold);
}

/* Card Header */
.card-header {
    position: relative;
    overflow: hidden;
}

.podcast-thumbnail {
    position: relative;
    height: 220px;
    overflow: hidden;
    background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
}

.podcast-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.podcast-card:hover .podcast-thumbnail img {
    transform: scale(1.05);
}

.default-thumbnail {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--color-gray), #2a2a2a);
    color: var(--color-gold);
    font-size: 3.5rem;
    opacity: 0.7;
}

.featured-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: linear-gradient(135deg, var(--color-gold), #e6ac00);
    color: var(--color-black);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.episode-badge {
    position: absolute;
    bottom: 12px;
    left: 12px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--color-gold);
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

/* Card Body */
.card-body {
    padding: 1.5rem;
}

.podcast-titles {
    margin-bottom: 1.2rem;
}

.title-en {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--color-white);
    line-height: 1.4;
}

.title-ar {
    margin: 0 0 0.8rem 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--color-light-gray);
    direction: rtl;
    line-height: 1.4;
}

.podcast-meta {
    margin-bottom: 1rem;
}

.meta-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.8rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--color-light-gray);
    flex: 1;
}

.meta-item i {
    width: 16px;
    color: var(--color-gold);
    font-size: 0.9rem;
}

.podcast-description {
    color: var(--color-light-gray);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    text-align: justify;
}

.podcast-status {
    margin-bottom: 1rem;
}

.status-badge {
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.status-inactive {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

/* Card Footer */
.card-footer {
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid var(--color-gray);
}

.action-buttons {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

.action-buttons .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.no-data {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    color: var(--text-muted);
}

.no-data i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-data h3 {
    margin: 1rem 0 0.5rem 0;
    color: var(--text-secondary);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: var(--color-dark);
    border-radius: var(--border-radius-md);
    border: 2px dashed var(--color-gray);
    margin-top: 2rem;
}

.empty-icon {
    margin-bottom: 1.5rem;
}

.empty-icon i {
    font-size: 4rem;
    color: var(--color-gold);
    opacity: 0.6;
}

.empty-state h3 {
    margin: 0 0 1rem 0;
    color: var(--color-white);
    font-size: 1.5rem;
}

.empty-state p {
    margin: 0 0 2rem 0;
    color: var(--color-light-gray);
    font-size: 1rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    overflow-y: auto;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: block;
}

.modal-dialog {
    margin: 2rem auto;
    max-width: 90%;
    width: 800px;
}

.modal-xl {
    max-width: 95%;
    width: 1000px;
}

.modal-content {
    background: var(--color-dark);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-gray);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--color-gray);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
}

.modal-title {
    color: var(--color-gold);
    font-size: 1.5rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-close {
    background: none;
    border: none;
    color: var(--color-white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--color-gold);
}

.modal-body {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--color-gray);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    background: rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .podcasts-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .meta-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }

    .modal-body {
        padding: 1.5rem;
    }
}
