/**
 * RTL CSS for Admin Pages
 * Includes RTL styles for celebrities, stories, users, votes, and settings pages
 */

/* ===== Common Page Styles ===== */
.page-title::after {
    left: auto;
    right: 0;
    background: linear-gradient(-90deg, var(--color-gold), transparent);
}

.action-buttons {
    justify-content: flex-end;
}

.action-buttons .btn i {
    margin-right: 0;
    margin-left: var(--spacing-sm);
}

/* ===== Card Items ===== */
.celebrity-actions .btn i,
.story-actions .btn i,
.user-actions .btn i {
    margin-right: 0;
    margin-left: var(--spacing-xs);
}

/* ===== Form Styles ===== */
.form-actions {
    justify-content: flex-start;
}

/* ===== Settings Page ===== */
.settings-tabs .tab {
    border-bottom: 3px solid transparent;
}

.info-item {
    flex-direction: row-reverse;
}

/* ===== Votes Page ===== */
.stat-chart {
    direction: ltr; /* Charts should remain LTR */
}

/* ===== Tables ===== */
.data-table th,
.data-table td {
    text-align: right;
}

/* ===== Modals ===== */
.modal-header {
    flex-direction: row-reverse;
}

.modal-close {
    margin-right: auto;
    margin-left: 0;
}

/* ===== File Inputs ===== */
.file-preview {
    text-align: right;
}
