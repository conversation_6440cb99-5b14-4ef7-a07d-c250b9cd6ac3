<?php
/**
 * Admin Votes Management Page
 */

// Process form submission
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $message = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
        $message_type = 'danger';
    } else {
        // Get form data
        $vote_id = isset($_POST['vote_id']) ? (int)$_POST['vote_id'] : 0;
        $action = isset($_POST['action']) ? $_POST['action'] : '';
        
        // Process based on action
        if ($action === 'delete') {
            // Delete vote
            if ($vote_id > 0) {
                // Get vote details to update nominee vote count
                $get_vote_sql = "SELECT nominee_id FROM votes WHERE id = ?";
                $get_vote_stmt = $conn->prepare($get_vote_sql);
                $get_vote_stmt->bind_param('i', $vote_id);
                $get_vote_stmt->execute();
                $get_vote_result = $get_vote_stmt->get_result();
                
                if ($get_vote_result && $get_vote_result->num_rows > 0) {
                    $nominee_id = $get_vote_result->fetch_assoc()['nominee_id'];
                    
                    // Start transaction
                    $conn->begin_transaction();
                    
                    try {
                        // Delete vote
                        $delete_sql = "DELETE FROM votes WHERE id = ?";
                        $delete_stmt = $conn->prepare($delete_sql);
                        $delete_stmt->bind_param('i', $vote_id);
                        $delete_stmt->execute();
                        
                        // Update nominee vote count
                        $update_nominee_sql = "UPDATE nominees SET votes = votes - 1 WHERE id = ? AND votes > 0";
                        $update_nominee_stmt = $conn->prepare($update_nominee_sql);
                        $update_nominee_stmt->bind_param('i', $nominee_id);
                        $update_nominee_stmt->execute();
                        
                        // Commit transaction
                        $conn->commit();
                        
                        $message = 'تم حذف التصويت بنجاح.';
                        $message_type = 'success';
                    } catch (Exception $e) {
                        // Rollback transaction on error
                        $conn->rollback();
                        
                        $message = 'حدث خطأ أثناء حذف التصويت: ' . $e->getMessage();
                        $message_type = 'danger';
                    }
                } else {
                    $message = 'التصويت غير موجود.';
                    $message_type = 'danger';
                }
            }
        } elseif ($action === 'delete_all') {
            // Delete all votes
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Delete all votes
                $delete_all_sql = "DELETE FROM votes";
                $conn->query($delete_all_sql);
                
                // Reset all nominee vote counts
                $reset_nominees_sql = "UPDATE nominees SET votes = 0";
                $conn->query($reset_nominees_sql);
                
                // Commit transaction
                $conn->commit();
                
                $message = 'تم حذف جميع التصويتات بنجاح.';
                $message_type = 'success';
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                
                $message = 'حدث خطأ أثناء حذف التصويتات: ' . $e->getMessage();
                $message_type = 'danger';
            }
        }
    }
}

// Get votes with user and nominee details
$votes_sql = "
    SELECT v.id, v.created_at, 
           u.id AS user_id, u.username, u.full_name, 
           n.id AS nominee_id, n.name, n.name_ar, n.category
    FROM votes v
    JOIN users u ON v.user_id = u.id
    JOIN nominees n ON v.nominee_id = n.id
    ORDER BY v.created_at DESC
";
$votes_result = $conn->query($votes_sql);
$votes = [];

if ($votes_result && $votes_result->num_rows > 0) {
    while ($row = $votes_result->fetch_assoc()) {
        $votes[] = $row;
    }
}

// Get vote statistics
$total_votes = count($votes);

// Votes by category
$votes_by_category_sql = "
    SELECT n.category, COUNT(*) as vote_count
    FROM votes v
    JOIN nominees n ON v.nominee_id = n.id
    GROUP BY n.category
    ORDER BY vote_count DESC
";
$votes_by_category_result = $conn->query($votes_by_category_sql);
$votes_by_category = [];

if ($votes_by_category_result && $votes_by_category_result->num_rows > 0) {
    while ($row = $votes_by_category_result->fetch_assoc()) {
        $votes_by_category[] = $row;
    }
}

// Votes by date
$votes_by_date_sql = "
    SELECT DATE(v.created_at) as vote_date, COUNT(*) as vote_count
    FROM votes v
    GROUP BY DATE(v.created_at)
    ORDER BY vote_date DESC
    LIMIT 10
";
$votes_by_date_result = $conn->query($votes_by_date_sql);
$votes_by_date = [];

if ($votes_by_date_result && $votes_by_date_result->num_rows > 0) {
    while ($row = $votes_by_date_result->fetch_assoc()) {
        $votes_by_date[] = $row;
    }
}
?>

<div class="votes-management">
    <h1 class="page-title">إدارة التصويتات</h1>
    
    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?>">
        <?php echo $message; ?>
    </div>
    <?php endif; ?>
    
    <!-- Vote Statistics -->
    <div class="stats-row">
        <div class="card">
            <div class="card-header">
                <h2>إحصائيات التصويت</h2>
            </div>
            <div class="card-body">
                <div class="stat-item">
                    <div class="stat-label">إجمالي الأصوات</div>
                    <div class="stat-value"><?php echo $total_votes; ?></div>
                </div>
                
                <?php if (!empty($votes_by_category)): ?>
                <div class="stat-item">
                    <div class="stat-label">الأصوات حسب الفئة</div>
                    <div class="stat-chart">
                        <canvas id="categoryChart" height="200"></canvas>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($votes_by_date)): ?>
                <div class="stat-item">
                    <div class="stat-label">الأصوات حسب التاريخ</div>
                    <div class="stat-chart">
                        <canvas id="dateChart" height="200"></canvas>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Votes List -->
    <div class="card">
        <div class="card-header">
            <h2>قائمة التصويتات</h2>
            <?php if (!empty($votes)): ?>
            <div class="card-actions">
                <button class="btn btn-danger" id="deleteAllVotesBtn">
                    <i class="fas fa-trash"></i> حذف جميع التصويتات
                </button>
            </div>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <?php if (empty($votes)): ?>
            <p class="no-data">لا توجد تصويتات حتى الآن.</p>
            <?php else: ?>
            <div class="table-responsive">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>المرشح</th>
                            <th>الفئة</th>
                            <th>تاريخ التصويت</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($votes as $vote): ?>
                        <tr>
                            <td>
                                <a href="index.php?page=users" title="عرض المستخدمين">
                                    <?php echo htmlspecialchars($vote['full_name']); ?>
                                    (<?php echo htmlspecialchars($vote['username']); ?>)
                                </a>
                            </td>
                            <td>
                                <a href="index.php?page=nominees" title="عرض المرشحين">
                                    <?php echo htmlspecialchars($current_lang === 'ar' ? $vote['name_ar'] : $vote['name']); ?>
                                </a>
                            </td>
                            <td><?php echo htmlspecialchars($vote['category']); ?></td>
                            <td><?php echo format_date($vote['created_at'], 'Y-m-d H:i'); ?></td>
                            <td>
                                <button class="btn btn-sm btn-danger delete-vote" 
                                    data-id="<?php echo $vote['id']; ?>"
                                    data-user="<?php echo htmlspecialchars($vote['username']); ?>"
                                    data-nominee="<?php echo htmlspecialchars($current_lang === 'ar' ? $vote['name_ar'] : $vote['name']); ?>">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Vote Modal -->
<div class="modal" id="deleteVoteModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>حذف التصويت</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من أنك تريد حذف تصويت المستخدم "<span id="delete_vote_user"></span>" للمرشح "<span id="delete_vote_nominee"></span>"؟</p>
            <p class="text-danger">سيتم تقليل عدد أصوات المرشح بمقدار 1. هذا الإجراء لا يمكن التراجع عنه.</p>
            
            <form action="index.php?page=votes" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="vote_id" id="delete_vote_id">
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-danger">حذف</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete All Votes Modal -->
<div class="modal" id="deleteAllVotesModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>حذف جميع التصويتات</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من أنك تريد حذف جميع التصويتات؟</p>
            <p class="text-danger">سيتم إعادة تعيين عدد أصوات جميع المرشحين إلى 0. هذا الإجراء لا يمكن التراجع عنه.</p>
            
            <form action="index.php?page=votes" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="delete_all">
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-danger">حذف الكل</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Delete vote modal
    const deleteButtons = document.querySelectorAll('.delete-vote');
    const deleteVoteModal = document.getElementById('deleteVoteModal');
    const deleteVoteId = document.getElementById('delete_vote_id');
    const deleteVoteUser = document.getElementById('delete_vote_user');
    const deleteVoteNominee = document.getElementById('delete_vote_nominee');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const user = this.getAttribute('data-user');
            const nominee = this.getAttribute('data-nominee');
            
            deleteVoteId.value = id;
            deleteVoteUser.textContent = user;
            deleteVoteNominee.textContent = nominee;
            
            deleteVoteModal.style.display = 'block';
        });
    });
    
    // Delete all votes modal
    const deleteAllVotesBtn = document.getElementById('deleteAllVotesBtn');
    const deleteAllVotesModal = document.getElementById('deleteAllVotesModal');
    
    if (deleteAllVotesBtn) {
        deleteAllVotesBtn.addEventListener('click', function() {
            deleteAllVotesModal.style.display = 'block';
        });
    }
    
    // Close modals
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });
    
    // Initialize charts
    if (typeof Chart !== 'undefined') {
        // Category chart
        const categoryChartElement = document.getElementById('categoryChart');
        if (categoryChartElement) {
            const categoryChart = new Chart(categoryChartElement, {
                type: 'pie',
                data: {
                    labels: [
                        <?php foreach ($votes_by_category as $category): ?>
                        '<?php echo addslashes($category['category']); ?>',
                        <?php endforeach; ?>
                    ],
                    datasets: [{
                        data: [
                            <?php foreach ($votes_by_category as $category): ?>
                            <?php echo $category['vote_count']; ?>,
                            <?php endforeach; ?>
                        ],
                        backgroundColor: [
                            '#d4af37',
                            '#aa8c2c',
                            '#f1e5ac',
                            '#8a7424',
                            '#c9c9c9'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    }
                }
            });
        }
        
        // Date chart
        const dateChartElement = document.getElementById('dateChart');
        if (dateChartElement) {
            const dateChart = new Chart(dateChartElement, {
                type: 'bar',
                data: {
                    labels: [
                        <?php foreach ($votes_by_date as $date): ?>
                        '<?php echo format_date($date['vote_date'], 'Y-m-d'); ?>',
                        <?php endforeach; ?>
                    ],
                    datasets: [{
                        label: 'عدد الأصوات',
                        data: [
                            <?php foreach ($votes_by_date as $date): ?>
                            <?php echo $date['vote_count']; ?>,
                            <?php endforeach; ?>
                        ],
                        backgroundColor: 'rgba(212, 175, 55, 0.7)',
                        borderColor: '#d4af37',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#ffffff'
                            },
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    }
                }
            });
        }
    }
});
</script>
