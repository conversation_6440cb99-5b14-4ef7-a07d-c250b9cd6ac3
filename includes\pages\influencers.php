<?php
/**
 * Influencers Page - Display all influencers
 */

// Get all influencers
$all_influencers = get_all_influencers();

// Get search and filter parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$platform_filter = isset($_GET['platform']) ? $_GET['platform'] : '';
$sort_by = isset($_GET['sort']) ? $_GET['sort'] : 'influence_score';

// Filter influencers based on search and filters
$filtered_influencers = $all_influencers;

if (!empty($search)) {
    $filtered_influencers = array_filter($filtered_influencers, function($influencer) use ($search) {
        return stripos($influencer['name'], $search) !== false ||
               stripos($influencer['name_ar'], $search) !== false ||
               stripos($influencer['title'], $search) !== false ||
               stripos($influencer['title_ar'], $search) !== false;
    });
}

if (!empty($platform_filter)) {
    $filtered_influencers = array_filter($filtered_influencers, function($influencer) use ($platform_filter) {
        return $influencer['platform'] === $platform_filter;
    });
}

// Sort influencers
switch ($sort_by) {
    case 'name':
        usort($filtered_influencers, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        break;
    case 'followers':
        usort($filtered_influencers, function($a, $b) {
            return $b['followers_count'] - $a['followers_count'];
        });
        break;
    case 'influence_score':
    default:
        usort($filtered_influencers, function($a, $b) {
            return $b['influence_score'] <=> $a['influence_score'];
        });
        break;
}

// Get unique platforms for filter
$platforms = array_unique(array_filter(array_column($all_influencers, 'platform')));
?>

<!-- Page Header -->
<section class="page-header influencers-page-header">
    <div class="container">
        <div class="header-content">
            <div class="breadcrumb">
                <a href="<?php echo base_url(); ?>"><?php echo get_content('influencers', 'breadcrumb_home', 'Home'); ?></a>
                <span class="separator">/</span>
                <span class="current"><?php echo get_content('influencers', 'breadcrumb_current', 'Influencers'); ?></span>
            </div>

            <h1 class="page-title">
                <i class="fas fa-crown"></i>
                <?php echo get_content('influencers', 'page_title', 'Most Influential Bloggers'); ?>
            </h1>
            <p class="page-subtitle"><?php echo get_content('influencers', 'page_subtitle', 'Discover the most influential content creators on social media'); ?></p>

            <div class="stats-overview">
                <div class="stat-item">
                    <span class="stat-number"><?php echo count($all_influencers); ?></span>
                    <span class="stat-label"><?php echo get_content('influencers', 'stat_total_influencers', 'Total Bloggers'); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php echo count($filtered_influencers); ?></span>
                    <span class="stat-label"><?php echo get_content('influencers', 'stat_results_shown', 'Results Shown'); ?></span>
                </div>
                <div class="stat-item">
                    <span class="stat-number"><?php
                        $total_followers = array_sum(array_column($all_influencers, 'followers_count'));
                        echo format_followers_count($total_followers);
                    ?></span>
                    <span class="stat-label"><?php echo get_content('influencers', 'stat_total_followers', 'Total Followers'); ?></span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters and Search -->
<section class="filters-section">
    <div class="container">
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <input type="hidden" name="page" value="influencers">

                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" name="search" placeholder="<?php echo get_content('influencers', 'search_placeholder', 'Search for blogger...'); ?>" value="<?php echo htmlspecialchars($search); ?>">
                </div>

                <div class="filter-group">
                    <select name="platform" onchange="this.form.submit()">
                        <option value=""><?php echo get_content('influencers', 'all_platforms', 'All Platforms'); ?></option>
                        <?php foreach ($platforms as $platform): ?>
                        <option value="<?php echo $platform; ?>" <?php echo $platform_filter === $platform ? 'selected' : ''; ?>>
                            <?php echo get_content('platforms', strtolower($platform), $platform); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="filter-group">
                    <select name="sort" onchange="this.form.submit()">
                        <option value="influence_score" <?php echo $sort_by === 'influence_score' ? 'selected' : ''; ?>><?php echo get_content('influencers', 'sort_by_influence', 'Most Influential'); ?></option>
                        <option value="followers" <?php echo $sort_by === 'followers' ? 'selected' : ''; ?>><?php echo get_content('influencers', 'sort_by_followers', 'Most Followed'); ?></option>
                        <option value="name" <?php echo $sort_by === 'name' ? 'selected' : ''; ?>><?php echo get_content('influencers', 'sort_by_name', 'Alphabetical'); ?></option>
                    </select>
                </div>

                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                    <?php echo get_content('influencers', 'search_button', 'Search'); ?>
                </button>

                <?php if (!empty($search) || !empty($platform_filter)): ?>
                <a href="?page=influencers" class="clear-filters">
                    <i class="fas fa-times"></i>
                    <?php echo get_content('influencers', 'clear_filters', 'Clear Filters'); ?>
                </a>
                <?php endif; ?>
            </form>
        </div>
    </div>
</section>

<!-- Influencers Grid -->
<section class="influencers-grid-section">
    <div class="container">
        <?php if (empty($filtered_influencers)): ?>
        <div class="no-results">
            <div class="no-results-icon">
                <i class="fas fa-search"></i>
            </div>
            <h3><?php echo get_content('influencers', 'no_results', 'No Results Found'); ?></h3>
            <p><?php echo get_content('influencers', 'no_results_desc', 'No bloggers found matching your search criteria'); ?></p>
            <a href="?page=influencers" class="btn btn-primary"><?php echo get_content('influencers', 'view_all_bloggers', 'View All Bloggers'); ?></a>
        </div>
        <?php else: ?>

        <div class="results-header">
            <h2><?php echo get_content('influencers', 'results_title', 'Results'); ?> (<?php echo count($filtered_influencers); ?>)</h2>
            <div class="view-toggle">
                <button class="view-btn active" data-view="grid">
                    <i class="fas fa-th"></i>
                </button>
                <button class="view-btn" data-view="list">
                    <i class="fas fa-list"></i>
                </button>
            </div>
        </div>

        <div class="influencers-grid" id="influencersGrid">
            <?php foreach ($filtered_influencers as $index => $influencer): ?>
            <div class="influencer-card" data-aos="fade-up" data-aos-delay="<?php echo ($index % 12) * 100; ?>">
                <!-- Rank Badge -->
                <div class="rank-badge">
                    <span class="rank-number">#<?php echo $index + 1; ?></span>
                </div>

                <!-- Featured Badge -->
                <?php if ($influencer['featured']): ?>
                <div class="featured-badge">
                    <i class="fas fa-star"></i>
                    <span><?php echo get_content('influencers', 'featured_badge', 'Featured'); ?></span>
                </div>
                <?php endif; ?>

                <div class="card-header">
                    <div class="influencer-image">
                        <?php if (!empty($influencer['image'])): ?>
                            <img src="<?php echo asset_url('img/influencers/' . $influencer['image']); ?>"
                                 alt="<?php echo get_influencer_name($influencer); ?>"
                                 onerror="this.src='assets/img/default-avatar.png'">
                        <?php else: ?>
                            <div class="default-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                        <?php endif; ?>

                        <!-- Influence Score Badge -->
                        <div class="score-badge">
                            <span class="score"><?php echo $influencer['influence_score']; ?></span>
                            <span class="max">/10</span>
                        </div>

                        <!-- Platform Badge -->
                        <?php if (!empty($influencer['platform'])): ?>
                        <div class="platform-badge">
                            <?php
                            $platform_icons = [
                                'Instagram' => 'fab fa-instagram',
                                'YouTube' => 'fab fa-youtube',
                                'TikTok' => 'fab fa-tiktok',
                                'LinkedIn' => 'fab fa-linkedin',
                                'Facebook' => 'fab fa-facebook',
                                'Twitter' => 'fab fa-twitter'
                            ];
                            $icon = $platform_icons[$influencer['platform']] ?? 'fas fa-globe';
                            ?>
                            <i class="<?php echo $icon; ?>"></i>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="card-body">
                    <h3 class="influencer-name"><?php echo get_influencer_name($influencer); ?></h3>
                    <p class="influencer-title"><?php echo get_influencer_title($influencer); ?></p>

                    <div class="metrics">
                        <div class="metric">
                            <i class="fas fa-users"></i>
                            <span><?php echo format_followers_count($influencer['followers_count']); ?></span>
                            <small><?php echo get_content('influencers', 'followers_label', 'Followers'); ?></small>
                        </div>
                        <div class="metric">
                            <i class="fas fa-star"></i>
                            <span><?php echo $influencer['influence_score']; ?></span>
                            <small><?php echo get_content('influencers', 'influence_score_label', 'Influence Score'); ?></small>
                        </div>
                    </div>

                    <?php if (!empty($influencer['bio']) || !empty($influencer['bio_ar'])): ?>
                    <div class="bio-preview">
                        <p><?php echo substr(htmlspecialchars(get_influencer_bio($influencer)), 0, 100) . '...'; ?></p>
                    </div>
                    <?php endif; ?>
                </div>

                <div class="card-footer">
                    <div class="social-links">
                        <?php if (!empty($influencer['facebook'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['facebook']); ?>" target="_blank" class="social-link facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        <?php endif; ?>

                        <?php if (!empty($influencer['twitter'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['twitter']); ?>" target="_blank" class="social-link twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                        <?php endif; ?>

                        <?php if (!empty($influencer['instagram'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['instagram']); ?>" target="_blank" class="social-link instagram">
                                <i class="fab fa-instagram"></i>
                            </a>
                        <?php endif; ?>

                        <?php if (!empty($influencer['youtube'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['youtube']); ?>" target="_blank" class="social-link youtube">
                                <i class="fab fa-youtube"></i>
                            </a>
                        <?php endif; ?>

                        <?php if (!empty($influencer['tiktok'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['tiktok']); ?>" target="_blank" class="social-link tiktok">
                                <i class="fab fa-tiktok"></i>
                            </a>
                        <?php endif; ?>

                        <?php if (!empty($influencer['linkedin'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['linkedin']); ?>" target="_blank" class="social-link linkedin">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        <?php endif; ?>

                        <?php if (!empty($influencer['website'])): ?>
                            <a href="<?php echo htmlspecialchars($influencer['website']); ?>" target="_blank" class="social-link website">
                                <i class="fas fa-globe"></i>
                            </a>
                        <?php endif; ?>
                    </div>

                    <button class="follow-btn" onclick="followInfluencer(<?php echo $influencer['id']; ?>)">
                        <i class="fas fa-plus"></i>
                        <span><?php echo get_content('influencers', 'follow_button', 'Follow'); ?></span>
                    </button>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <?php endif; ?>
    </div>
</section>

<!-- Back to Top -->
<button class="back-to-top" onclick="scrollToTop()">
    <i class="fas fa-arrow-up"></i>
</button>
