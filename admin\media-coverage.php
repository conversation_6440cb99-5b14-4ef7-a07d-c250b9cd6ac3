<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit();
}

$page_title = 'إدارة التغطية الإعلامية';
$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $success_message = add_media_coverage();
                break;
            case 'edit':
                $success_message = edit_media_coverage();
                break;
            case 'delete':
                $success_message = delete_media_coverage();
                break;
        }
    }
}

// Get all media coverage
$media_coverage = get_all_media_coverage();

function add_media_coverage() {
    global $conn;
    
    $title = $_POST['title'];
    $title_ar = $_POST['title_ar'];
    $description = $_POST['description'] ?? '';
    $description_ar = $_POST['description_ar'] ?? '';
    $type = $_POST['type'];
    $link = $_POST['link'] ?? '';
    $featured = isset($_POST['featured']) ? 1 : 0;
    $sort_order = $_POST['sort_order'] ?? 0;
    
    // Handle image upload
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/uploads/';
        $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
        $image = 'media_' . time() . '.' . $file_extension;
        
        if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image)) {
            // Image uploaded successfully
        } else {
            return 'خطأ في رفع الصورة';
        }
    }
    
    $sql = "INSERT INTO media_coverage (title, title_ar, description, description_ar, image, type, link, featured, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sssssssii', $title, $title_ar, $description, $description_ar, $image, $type, $link, $featured, $sort_order);
    
    if ($stmt->execute()) {
        return 'تم إضافة التغطية الإعلامية بنجاح';
    } else {
        return 'خطأ في إضافة التغطية الإعلامية';
    }
}

function edit_media_coverage() {
    global $conn;
    
    $id = $_POST['id'];
    $title = $_POST['title'];
    $title_ar = $_POST['title_ar'];
    $description = $_POST['description'] ?? '';
    $description_ar = $_POST['description_ar'] ?? '';
    $type = $_POST['type'];
    $link = $_POST['link'] ?? '';
    $featured = isset($_POST['featured']) ? 1 : 0;
    $sort_order = $_POST['sort_order'] ?? 0;
    
    // Handle image upload
    $image_sql = '';
    $image_param = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/uploads/';
        $file_extension = pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION);
        $image = 'media_' . time() . '.' . $file_extension;
        
        if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image)) {
            $image_sql = ', image = ?';
            $image_param = $image;
        }
    }
    
    $sql = "UPDATE media_coverage SET title = ?, title_ar = ?, description = ?, description_ar = ?, type = ?, link = ?, featured = ?, sort_order = ?" . $image_sql . " WHERE id = ?";
    $stmt = $conn->prepare($sql);
    
    if ($image_param) {
        $stmt->bind_param('ssssssiisi', $title, $title_ar, $description, $description_ar, $type, $link, $featured, $sort_order, $image_param, $id);
    } else {
        $stmt->bind_param('ssssssiii', $title, $title_ar, $description, $description_ar, $type, $link, $featured, $sort_order, $id);
    }
    
    if ($stmt->execute()) {
        return 'تم تحديث التغطية الإعلامية بنجاح';
    } else {
        return 'خطأ في تحديث التغطية الإعلامية';
    }
}

function delete_media_coverage() {
    global $conn;
    
    $id = $_POST['id'];
    
    // Get image name to delete file
    $result = $conn->query("SELECT image FROM media_coverage WHERE id = $id");
    if ($result && $row = $result->fetch_assoc()) {
        $image_file = '../assets/uploads/' . $row['image'];
        if (file_exists($image_file)) {
            unlink($image_file);
        }
    }
    
    $sql = "DELETE FROM media_coverage WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $id);
    
    if ($stmt->execute()) {
        return 'تم حذف التغطية الإعلامية بنجاح';
    } else {
        return 'خطأ في حذف التغطية الإعلامية';
    }
}

function get_all_media_coverage() {
    global $conn;
    
    $sql = "SELECT * FROM media_coverage ORDER BY type ASC, sort_order ASC, id DESC";
    $result = $conn->query($sql);
    
    $coverage = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $coverage[] = $row;
        }
    }
    
    return $coverage;
}

include 'includes/header.php';
?>

<div class="admin-container">
    <div class="admin-header">
        <h1><i class="fas fa-tv"></i> <?php echo $page_title; ?></h1>
        <button class="btn btn-primary" onclick="openAddModal()">
            <i class="fas fa-plus"></i> إضافة تغطية جديدة
        </button>
    </div>

    <?php if ($success_message): ?>
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
    </div>
    <?php endif; ?>

    <?php if ($error_message): ?>
    <div class="alert alert-error">
        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
    </div>
    <?php endif; ?>

    <!-- Media Coverage Table -->
    <div class="admin-card">
        <div class="card-header">
            <h3><i class="fas fa-list"></i> قائمة التغطية الإعلامية</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>الصورة</th>
                            <th>العنوان</th>
                            <th>النوع</th>
                            <th>مميز</th>
                            <th>الترتيب</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($media_coverage as $coverage): ?>
                        <tr>
                            <td>
                                <div class="coverage-image-preview">
                                    <?php if (!empty($coverage['image'])): ?>
                                    <img src="../assets/uploads/<?php echo $coverage['image']; ?>" 
                                         alt="<?php echo htmlspecialchars($coverage['title']); ?>">
                                    <?php else: ?>
                                    <div class="no-image">
                                        <i class="fas fa-image"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <div class="coverage-title">
                                    <strong><?php echo htmlspecialchars($coverage['title']); ?></strong>
                                    <br>
                                    <small><?php echo htmlspecialchars($coverage['title_ar']); ?></small>
                                </div>
                            </td>
                            <td>
                                <span class="type-badge <?php echo $coverage['type']; ?>">
                                    <?php echo $coverage['type'] === 'advertising' ? 'إعلانية' : 'تلفزيونية'; ?>
                                </span>
                            </td>
                            <td>
                                <?php if ($coverage['featured']): ?>
                                <span class="status-badge featured">مميز</span>
                                <?php else: ?>
                                <span class="status-badge">عادي</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo $coverage['sort_order']; ?></td>
                            <td>
                                <span class="status-badge <?php echo $coverage['status']; ?>">
                                    <?php echo $coverage['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                                </span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-sm btn-primary" onclick="editCoverage(<?php echo $coverage['id']; ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" onclick="deleteCoverage(<?php echo $coverage['id']; ?>)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
<div id="coverageModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">إضافة تغطية إعلامية</h3>
            <span class="close" onclick="closeModal()">&times;</span>
        </div>
        <form id="coverageForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" id="formAction" value="add">
            <input type="hidden" name="id" id="coverageId">
            
            <div class="form-row">
                <div class="form-group">
                    <label for="title">العنوان (English)</label>
                    <input type="text" name="title" id="title" required>
                </div>
                <div class="form-group">
                    <label for="title_ar">العنوان (العربية)</label>
                    <input type="text" name="title_ar" id="title_ar" required>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="description">الوصف (English)</label>
                    <textarea name="description" id="description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="description_ar">الوصف (العربية)</label>
                    <textarea name="description_ar" id="description_ar" rows="3"></textarea>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="type">نوع التغطية</label>
                    <select name="type" id="type" required>
                        <option value="advertising">إعلانية</option>
                        <option value="tv">تلفزيونية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="sort_order">ترتيب العرض</label>
                    <input type="number" name="sort_order" id="sort_order" value="0">
                </div>
            </div>
            
            <div class="form-group">
                <label for="link">الرابط (اختياري)</label>
                <input type="url" name="link" id="link">
            </div>
            
            <div class="form-group">
                <label for="image">الصورة</label>
                <input type="file" name="image" id="image" accept="image/*">
                <small>الحد الأقصى: 5MB. الأنواع المدعومة: JPG, PNG, GIF</small>
            </div>
            
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="featured" id="featured">
                    <span class="checkmark"></span>
                    تغطية مميزة
                </label>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.coverage-image-preview {
    width: 60px;
    height: 40px;
    border-radius: 5px;
    overflow: hidden;
}

.coverage-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
}

.coverage-title strong {
    color: #333;
}

.coverage-title small {
    color: #666;
}

.type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.type-badge.advertising {
    background: #e3f2fd;
    color: #1976d2;
}

.type-badge.tv {
    background: #f3e5f5;
    color: #7b1fa2;
}

.status-badge.featured {
    background: #fff3e0;
    color: #f57c00;
}
</style>

<script>
function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة تغطية إعلامية';
    document.getElementById('formAction').value = 'add';
    document.getElementById('coverageForm').reset();
    document.getElementById('coverageModal').style.display = 'block';
}

function editCoverage(id) {
    // Get coverage data via AJAX or from page data
    document.getElementById('modalTitle').textContent = 'تعديل التغطية الإعلامية';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('coverageId').value = id;
    document.getElementById('coverageModal').style.display = 'block';
}

function deleteCoverage(id) {
    if (confirm('هل أنت متأكد من حذف هذه التغطية؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function closeModal() {
    document.getElementById('coverageModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('coverageModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}
</script>

<?php include 'includes/footer.php'; ?>
