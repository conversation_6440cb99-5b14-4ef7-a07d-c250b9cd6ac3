/**
 * Enhanced RTL CSS for Admin Dashboard
 */

/* ===== Base Styles ===== */
body {
    text-align: right;
    direction: rtl;
}

input, textarea, select {
    text-align: right;
}

/* ===== Admin Wrapper ===== */
.admin-wrapper {
    flex-direction: row-reverse;
}

/* ===== Sidebar ===== */
.admin-sidebar {
    left: auto;
    right: 0;
    border-right: none;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header {
    flex-direction: row-reverse;
}

.sidebar-nav ul {
    padding-right: 0;
}

.sidebar-nav a {
    text-align: right;
}

.sidebar-nav a i {
    margin-right: 0;
    margin-left: 10px;
}

.dropdown-toggle::after {
    margin-left: 0;
    margin-right: auto;
    transform: rotate(180deg);
}

.dropdown-menu {
    padding-left: 0;
    padding-right: 20px;
}

/* ===== Main Content ===== */
.admin-main {
    margin-left: 0;
    margin-right: 250px;
}

.admin-header {
    flex-direction: row-reverse;
}

.header-search {
    margin-right: 0;
    margin-left: auto;
}

.header-search input {
    padding: 8px 15px 8px 40px;
}

.header-search button {
    right: auto;
    left: 5px;
}

.header-actions {
    flex-direction: row-reverse;
}

.language-switcher {
    margin-right: 0;
    margin-left: 15px;
}

.user-toggle i {
    margin-right: 0;
    margin-left: 5px;
}

.dropdown-menu {
    right: auto;
    left: 0;
    text-align: right;
}

.dropdown-menu i {
    margin-right: 0;
    margin-left: 5px;
}

/* ===== Dashboard ===== */
.stat-card {
    text-align: right;
}

.stat-icon {
    margin-right: 0;
    margin-left: 15px;
}

/* ===== Forms ===== */
.form-group label {
    text-align: right;
}

.form-actions {
    text-align: right;
}

/* ===== Tables ===== */
.table th, .table td {
    text-align: right;
}

.table-actions {
    justify-content: flex-end;
}

/* ===== Content Management ===== */
.content-tabs {
    flex-direction: row-reverse;
}

.content-item-header {
    flex-direction: row-reverse;
}

.content-item-actions {
    margin-left: 0;
    margin-right: auto;
}

.content-label {
    text-align: right;
}

.content-text {
    text-align: right;
}

/* ===== Header Content Management ===== */
.header-content-management .card-header h2 {
    text-align: right;
}

/* ===== Modals ===== */
.modal-header {
    flex-direction: row-reverse;
}

.modal-close {
    margin-left: 0;
    margin-right: auto;
}

/* ===== Alerts ===== */
.alert {
    text-align: right;
}

/* ===== Media Queries ===== */
@media (max-width: 992px) {
    .admin-sidebar {
        transform: translateX(100%);
    }
    
    .admin-sidebar.active {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-right: 0;
    }
}
