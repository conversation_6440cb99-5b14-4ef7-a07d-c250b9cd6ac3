<?php
/**
 * Admin Header Content Management Page
 */

// Process form submission
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $message = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
        $message_type = 'danger';
    } else {
        // Get form data
        $key_name = isset($_POST['key_name']) ? trim($_POST['key_name']) : '';
        $value_en = isset($_POST['value_en']) ? trim($_POST['value_en']) : '';
        $value_ar = isset($_POST['value_ar']) ? trim($_POST['value_ar']) : '';
        $content_id = isset($_POST['content_id']) ? (int)$_POST['content_id'] : 0;
        $action = isset($_POST['action']) ? $_POST['action'] : '';

        // Validate form data
        if ($action === 'edit') {
            // Update content
            if ($content_id > 0 && !empty($value_en) && !empty($value_ar)) {
                $sql = "UPDATE content SET value_en = ?, value_ar = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('ssi', $value_en, $value_ar, $content_id);

                if ($stmt->execute()) {
                    $message = 'تم تحديث محتوى الهيدر بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تحديث المحتوى: ' . $stmt->error;
                    $message_type = 'danger';
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        } elseif ($action === 'add') {
            // Add new content
            if (!empty($key_name) && !empty($value_en) && !empty($value_ar)) {
                // Check if content already exists
                $check_sql = "SELECT id FROM content WHERE section = 'nav' AND key_name = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param('s', $key_name);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();

                if ($check_result->num_rows > 0) {
                    $message = 'عنصر القائمة موجود بالفعل. يرجى استخدام زر التحرير بدلاً من ذلك.';
                    $message_type = 'danger';
                } else {
                    // Insert new content
                    $section = 'nav'; // Fixed section for header content
                    $sql = "INSERT INTO content (section, key_name, value_en, value_ar) VALUES (?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('ssss', $section, $key_name, $value_en, $value_ar);

                    if ($stmt->execute()) {
                        $message = 'تمت إضافة عنصر القائمة بنجاح.';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء إضافة المحتوى: ' . $stmt->error;
                        $message_type = 'danger';
                    }
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        }
    }
}

// Get header content (nav section)
$content_sql = "SELECT id, key_name, value_en, value_ar FROM content WHERE section = 'nav' ORDER BY key_name";
$content_result = $conn->query($content_sql);
$header_content = [];

if ($content_result && $content_result->num_rows > 0) {
    while ($row = $content_result->fetch_assoc()) {
        $header_content[] = $row;
    }
}

// Get site title
$site_title_sql = "SELECT id, value_en, value_ar FROM content WHERE section = 'site' AND key_name = 'title' LIMIT 1";
$site_title_result = $conn->query($site_title_sql);
$site_title = null;

if ($site_title_result && $site_title_result->num_rows > 0) {
    $site_title = $site_title_result->fetch_assoc();
}
?>

<div class="header-content-management content-management">
    <div class="dashboard-header">
        <h1 class="page-title">إدارة محتوى الهيدر</h1>
        <div class="dashboard-actions">
            <div class="date-display">
                <i class="fas fa-calendar-alt"></i>
                <span><?php echo date('l, d F Y'); ?></span>
            </div>
        </div>
    </div>

    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?> animated">
        <i class="fas fa-<?php echo $message_type === 'success' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
        <?php echo $message; ?>
    </div>
    <?php endif; ?>

    <!-- Welcome Banner -->
    <div class="welcome-banner">
        <div class="welcome-content">
            <h2>إدارة محتوى الهيدر</h2>
            <p>من هنا يمكنك إدارة محتوى الهيدر بما في ذلك عنوان الموقع وعناصر القائمة. هذه العناصر ستظهر في الهيدر في جميع صفحات الموقع.</p>
        </div>
        <div class="welcome-image">
            <i class="fas fa-heading"></i>
        </div>
    </div>

    <div class="content-tabs-container">
        <div class="content-tabs">
            <div class="content-tab active" data-tab="site-title">عنوان الموقع</div>
            <div class="content-tab" data-tab="menu-items">عناصر القائمة</div>
            <div class="content-tab" data-tab="add-item">إضافة عنصر جديد</div>
        </div>

        <!-- Site Title Panel -->
        <div id="site-title" class="content-panel active">
            <h2><i class="fas fa-heading"></i> عنوان الموقع</h2>

            <?php if ($site_title): ?>
            <div class="content-item site-title-item">
                <div class="content-item-header">
                    <h3 class="content-item-title">عنوان الموقع</h3>
                </div>
                <div class="content-item-body">
                    <form action="index.php?page=header_content" method="post" class="site-title-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="content_id" value="<?php echo $site_title['id']; ?>">

                        <div class="form-row">
                            <div class="form-group">
                                <label for="site_title_en">
                                    <i class="fas fa-language"></i> عنوان الموقع (الإنجليزية)
                                </label>
                                <input type="text" id="site_title_en" name="value_en" value="<?php echo htmlspecialchars($site_title['value_en']); ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="site_title_ar">
                                    <i class="fas fa-language"></i> عنوان الموقع (العربية)
                                </label>
                                <input type="text" id="site_title_ar" name="value_ar" value="<?php echo htmlspecialchars($site_title['value_ar']); ?>" required dir="rtl">
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> تحديث عنوان الموقع
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <?php else: ?>
            <div class="no-data-container">
                <i class="fas fa-exclamation-triangle no-data-icon"></i>
                <p class="no-data">عنوان الموقع غير موجود. يرجى إضافته في صفحة إدارة المحتوى.</p>
                <a href="index.php?page=content" class="btn btn-primary">
                    <i class="fas fa-plus-circle"></i> إضافة عنوان الموقع
                </a>
            </div>
            <?php endif; ?>
        </div>

        <!-- Menu Items Panel -->
        <div id="menu-items" class="content-panel">
            <h2><i class="fas fa-list"></i> عناصر قائمة الهيدر</h2>

            <?php if (empty($header_content)): ?>
            <div class="no-data-container">
                <i class="fas fa-list no-data-icon"></i>
                <p class="no-data">لا توجد عناصر قائمة. أضف عناصر جديدة أولاً.</p>
                <button class="btn btn-primary" onclick="document.querySelector('[data-tab=\'add-item\']').click()">
                    <i class="fas fa-plus-circle"></i> إضافة عنصر قائمة
                </button>
            </div>
            <?php else: ?>
            <div class="content-items menu-items">
                <?php foreach ($header_content as $item): ?>
                <div class="content-item menu-item">
                    <div class="content-item-header">
                        <h3 class="content-item-title"><?php echo htmlspecialchars($item['key_name']); ?></h3>
                        <div class="content-item-actions">
                            <button class="btn btn-sm btn-primary edit-content"
                                    data-id="<?php echo $item['id']; ?>"
                                    data-key="<?php echo htmlspecialchars($item['key_name']); ?>"
                                    data-en="<?php echo htmlspecialchars($item['value_en']); ?>"
                                    data-ar="<?php echo htmlspecialchars($item['value_ar']); ?>">
                                <i class="fas fa-edit"></i> تحرير
                            </button>
                        </div>
                    </div>
                    <div class="content-item-body">
                        <div class="content-value">
                            <div class="content-label">النص (الإنجليزية)</div>
                            <div class="content-text"><?php echo htmlspecialchars($item['value_en']); ?></div>
                        </div>
                        <div class="content-value">
                            <div class="content-label">النص (العربية)</div>
                            <div class="content-text" dir="rtl"><?php echo htmlspecialchars($item['value_ar']); ?></div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Add New Menu Item Panel -->
        <div id="add-item" class="content-panel">
            <h2><i class="fas fa-plus-circle"></i> إضافة عنصر قائمة جديد</h2>

            <div class="content-item add-menu-item">
                <div class="content-item-body">
                    <form action="index.php?page=header_content" method="post" class="add-menu-form">
                        <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                        <input type="hidden" name="action" value="add">

                        <div class="form-group">
                            <label for="key_name">
                                <i class="fas fa-key"></i> اسم المفتاح (مثال: home, about, contact)
                            </label>
                            <input type="text" id="key_name" name="key_name" required>
                            <small class="form-text">استخدم أحرف إنجليزية صغيرة بدون مسافات أو أحرف خاصة.</small>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="value_en">
                                    <i class="fas fa-language"></i> النص (الإنجليزية)
                                </label>
                                <input type="text" id="value_en" name="value_en" required>
                            </div>

                            <div class="form-group">
                                <label for="value_ar">
                                    <i class="fas fa-language"></i> النص (العربية)
                                </label>
                                <input type="text" id="value_ar" name="value_ar" required dir="rtl">
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus-circle"></i> إضافة عنصر القائمة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Content Modal -->
<div class="modal" id="editContentModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-edit"></i> تحرير عنصر القائمة</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=header_content" method="post" id="editContentForm">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="content_id" id="edit_content_id">

                <div class="form-group">
                    <label for="edit_key_name">
                        <i class="fas fa-key"></i> اسم المفتاح
                    </label>
                    <input type="text" id="edit_key_name" readonly>
                    <small class="form-text">لا يمكن تعديل اسم المفتاح.</small>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_value_en">
                            <i class="fas fa-language"></i> النص (الإنجليزية)
                        </label>
                        <input type="text" id="edit_value_en" name="value_en" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_value_ar">
                            <i class="fas fa-language"></i> النص (العربية)
                        </label>
                        <input type="text" id="edit_value_ar" name="value_ar" required dir="rtl">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary modal-close">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Content tabs functionality
    initContentTabs();

    // Edit modal functionality
    initEditModal();

    // Add animation to content items
    animateContentItems();

    // Add ripple effect to buttons
    initRippleEffect();
});

/**
 * Initialize content tabs functionality
 */
function initContentTabs() {
    const contentTabs = document.querySelectorAll('.content-tab');
    const contentPanels = document.querySelectorAll('.content-panel');

    if (contentTabs.length === 0 || contentPanels.length === 0) return;

    // Store the active tab in session storage
    const storedActiveTab = sessionStorage.getItem('activeHeaderContentTab');

    // If there's a stored active tab, activate it
    if (storedActiveTab) {
        const tabToActivate = document.querySelector(`[data-tab="${storedActiveTab}"]`);
        const panelToActivate = document.getElementById(storedActiveTab);

        if (tabToActivate && panelToActivate) {
            // Deactivate all tabs and panels
            contentTabs.forEach(tab => tab.classList.remove('active'));
            contentPanels.forEach(panel => panel.classList.remove('active'));

            // Activate the stored tab and panel
            tabToActivate.classList.add('active');
            panelToActivate.classList.add('active');
        }
    }

    // Add click event listeners to tabs
    contentTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabId = this.getAttribute('data-tab');
            if (!tabId) return;

            // Store the active tab in session storage
            sessionStorage.setItem('activeHeaderContentTab', tabId);

            // Deactivate all tabs and panels
            contentTabs.forEach(t => t.classList.remove('active'));
            contentPanels.forEach(p => {
                p.style.opacity = '0';
                setTimeout(() => {
                    p.classList.remove('active');
                }, 300);
            });

            // Activate the clicked tab
            this.classList.add('active');

            // Activate the corresponding panel with animation
            const panel = document.getElementById(tabId);
            if (panel) {
                setTimeout(() => {
                    panel.classList.add('active');
                    setTimeout(() => {
                        panel.style.opacity = '1';
                    }, 10);
                }, 300);
            }
        });
    });
}

/**
 * Initialize edit modal functionality
 */
function initEditModal() {
    const editButtons = document.querySelectorAll('.edit-content');
    const editModal = document.getElementById('editContentModal');

    if (!editModal || editButtons.length === 0) return;

    const editContentId = document.getElementById('edit_content_id');
    const editKeyName = document.getElementById('edit_key_name');
    const editValueEn = document.getElementById('edit_value_en');
    const editValueAr = document.getElementById('edit_value_ar');

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const key = this.getAttribute('data-key');
            const valueEn = this.getAttribute('data-en');
            const valueAr = this.getAttribute('data-ar');

            editContentId.value = id;
            editKeyName.value = key;
            editValueEn.value = valueEn;
            editValueAr.value = valueAr;

            // Show modal with animation
            editModal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Focus on the first input
            setTimeout(() => {
                editValueEn.focus();
            }, 300);
        });
    });

    // Close modals
    const modalCloseButtons = document.querySelectorAll('.modal-close');

    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.opacity = '0';
            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.opacity = '0';
            setTimeout(() => {
                event.target.style.display = 'none';
                document.body.style.overflow = '';
            }, 300);
        }
    });
}

/**
 * Add animation to content items
 */
function animateContentItems() {
    const contentItems = document.querySelectorAll('.content-item');
    contentItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';

        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, 100 + (index * 50));
    });
}

/**
 * Add ripple effect to buttons
 */
function initRippleEffect() {
    const buttons = document.querySelectorAll('.btn');

    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}
</script>
