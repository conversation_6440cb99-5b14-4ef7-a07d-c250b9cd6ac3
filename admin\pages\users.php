<?php
/**
 * Admin Users Management Page
 */

// Process form submission
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $message = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
        $message_type = 'danger';
    } else {
        // Get form data
        $user_id = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
        $username = isset($_POST['username']) ? trim($_POST['username']) : '';
        $email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $full_name = isset($_POST['full_name']) ? trim($_POST['full_name']) : '';
        $phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
        $password = isset($_POST['password']) ? $_POST['password'] : '';
        $is_admin = isset($_POST['is_admin']) ? (int)$_POST['is_admin'] : 0;
        $action = isset($_POST['action']) ? $_POST['action'] : '';
        
        // Process based on action
        if ($action === 'delete') {
            // Delete user
            if ($user_id > 0) {
                // Check if user is the current logged in admin
                if ($user_id === $_SESSION['user_id']) {
                    $message = 'لا يمكنك حذف حسابك الحالي.';
                    $message_type = 'danger';
                } else {
                    // Delete user from database
                    $sql = "DELETE FROM users WHERE id = ?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('i', $user_id);
                    
                    if ($stmt->execute()) {
                        // Delete votes for this user
                        $delete_votes_sql = "DELETE FROM votes WHERE user_id = ?";
                        $delete_votes_stmt = $conn->prepare($delete_votes_sql);
                        $delete_votes_stmt->bind_param('i', $user_id);
                        $delete_votes_stmt->execute();
                        
                        $message = 'تم حذف المستخدم بنجاح.';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء حذف المستخدم: ' . $stmt->error;
                        $message_type = 'danger';
                    }
                }
            }
        } elseif ($action === 'edit') {
            // Update user
            if ($user_id > 0 && !empty($username) && !empty($email) && !empty($full_name)) {
                // Check if username or email already exists
                $check_sql = "SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param('ssi', $username, $email, $user_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows > 0) {
                    $message = 'اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل.';
                    $message_type = 'danger';
                } else {
                    // Prepare SQL based on whether password is being updated
                    if (!empty($password)) {
                        // Hash password
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        
                        $sql = "UPDATE users SET username = ?, email = ?, password = ?, full_name = ?, phone = ?, is_admin = ? WHERE id = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param('sssssii', $username, $email, $hashed_password, $full_name, $phone, $is_admin, $user_id);
                    } else {
                        $sql = "UPDATE users SET username = ?, email = ?, full_name = ?, phone = ?, is_admin = ? WHERE id = ?";
                        $stmt = $conn->prepare($sql);
                        $stmt->bind_param('ssssii', $username, $email, $full_name, $phone, $is_admin, $user_id);
                    }
                    
                    if ($stmt->execute()) {
                        $message = 'تم تحديث المستخدم بنجاح.';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء تحديث المستخدم: ' . $stmt->error;
                        $message_type = 'danger';
                    }
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        } elseif ($action === 'add') {
            // Add new user
            if (!empty($username) && !empty($email) && !empty($password) && !empty($full_name)) {
                // Check if username or email already exists
                $check_sql = "SELECT id FROM users WHERE username = ? OR email = ?";
                $check_stmt = $conn->prepare($check_sql);
                $check_stmt->bind_param('ss', $username, $email);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows > 0) {
                    $message = 'اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل.';
                    $message_type = 'danger';
                } else {
                    // Hash password
                    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                    
                    // Insert new user
                    $sql = "INSERT INTO users (username, email, password, full_name, phone, is_admin) VALUES (?, ?, ?, ?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param('sssssi', $username, $email, $hashed_password, $full_name, $phone, $is_admin);
                    
                    if ($stmt->execute()) {
                        $message = 'تمت إضافة المستخدم بنجاح.';
                        $message_type = 'success';
                    } else {
                        $message = 'حدث خطأ أثناء إضافة المستخدم: ' . $stmt->error;
                        $message_type = 'danger';
                    }
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        }
    }
}

// Get all users
$users_sql = "SELECT id, username, email, full_name, phone, is_admin, created_at FROM users ORDER BY created_at DESC";
$users_result = $conn->query($users_sql);
$users = [];

if ($users_result && $users_result->num_rows > 0) {
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
}
?>

<div class="users-management">
    <h1 class="page-title">إدارة المستخدمين</h1>
    
    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?>">
        <?php echo $message; ?>
    </div>
    <?php endif; ?>
    
    <!-- Add New User Button -->
    <div class="action-buttons">
        <button class="btn btn-primary" id="addUserBtn">
            <i class="fas fa-plus"></i> إضافة مستخدم جديد
        </button>
    </div>
    
    <!-- Users List -->
    <div class="card">
        <div class="card-header">
            <h2>قائمة المستخدمين</h2>
        </div>
        <div class="card-body">
            <?php if (empty($users)): ?>
            <p class="no-data">لا يوجد مستخدمين حتى الآن.</p>
            <?php else: ?>
            <div class="table-responsive">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>اسم المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الاسم الكامل</th>
                            <th>الهاتف</th>
                            <th>نوع الحساب</th>
                            <th>تاريخ التسجيل</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                            <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                            <td><?php echo !empty($user['phone']) ? htmlspecialchars($user['phone']) : '<span class="text-muted">-</span>'; ?></td>
                            <td>
                                <span class="badge badge-<?php echo $user['is_admin'] ? 'primary' : 'secondary'; ?>">
                                    <?php echo $user['is_admin'] ? 'مدير' : 'مستخدم'; ?>
                                </span>
                            </td>
                            <td><?php echo format_date($user['created_at'], 'Y-m-d H:i'); ?></td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-user" 
                                    data-id="<?php echo $user['id']; ?>"
                                    data-username="<?php echo htmlspecialchars($user['username']); ?>"
                                    data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                    data-fullname="<?php echo htmlspecialchars($user['full_name']); ?>"
                                    data-phone="<?php echo htmlspecialchars($user['phone']); ?>"
                                    data-admin="<?php echo $user['is_admin']; ?>">
                                    <i class="fas fa-edit"></i> تحرير
                                </button>
                                <?php if ($user['id'] !== $_SESSION['user_id']): ?>
                                <button class="btn btn-sm btn-danger delete-user" 
                                    data-id="<?php echo $user['id']; ?>"
                                    data-username="<?php echo htmlspecialchars($user['username']); ?>">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal" id="addUserModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>إضافة مستخدم جديد</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=users" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="add">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">اسم المستخدم</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">كلمة المرور</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="full_name">الاسم الكامل</label>
                        <input type="text" id="full_name" name="full_name" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">الهاتف</label>
                        <input type="text" id="phone" name="phone">
                    </div>
                    
                    <div class="form-group">
                        <label for="is_admin">نوع الحساب</label>
                        <select id="is_admin" name="is_admin">
                            <option value="0">مستخدم عادي</option>
                            <option value="1">مدير</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">إضافة مستخدم</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal" id="editUserModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>تحرير المستخدم</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=users" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="user_id" id="edit_user_id">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_username">اسم المستخدم</label>
                        <input type="text" id="edit_username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_email">البريد الإلكتروني</label>
                        <input type="email" id="edit_email" name="email" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_password">كلمة المرور</label>
                        <input type="password" id="edit_password" name="password">
                        <small class="form-text">اترك هذا الحقل فارغًا للاحتفاظ بكلمة المرور الحالية.</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_full_name">الاسم الكامل</label>
                        <input type="text" id="edit_full_name" name="full_name" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_phone">الهاتف</label>
                        <input type="text" id="edit_phone" name="phone">
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_is_admin">نوع الحساب</label>
                        <select id="edit_is_admin" name="is_admin">
                            <option value="0">مستخدم عادي</option>
                            <option value="1">مدير</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal" id="deleteUserModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>حذف المستخدم</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من أنك تريد حذف المستخدم "<span id="delete_user_username"></span>"؟</p>
            <p class="text-danger">سيتم حذف جميع الأصوات المرتبطة بهذا المستخدم أيضًا. هذا الإجراء لا يمكن التراجع عنه.</p>
            
            <form action="index.php?page=users" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="user_id" id="delete_user_id">
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-danger">حذف</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add user modal
    const addUserBtn = document.getElementById('addUserBtn');
    const addUserModal = document.getElementById('addUserModal');
    
    if (addUserBtn) {
        addUserBtn.addEventListener('click', function() {
            addUserModal.style.display = 'block';
        });
    }
    
    // Edit user modal
    const editButtons = document.querySelectorAll('.edit-user');
    const editUserModal = document.getElementById('editUserModal');
    const editUserId = document.getElementById('edit_user_id');
    const editUsername = document.getElementById('edit_username');
    const editEmail = document.getElementById('edit_email');
    const editFullName = document.getElementById('edit_full_name');
    const editPhone = document.getElementById('edit_phone');
    const editIsAdmin = document.getElementById('edit_is_admin');
    
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            const email = this.getAttribute('data-email');
            const fullName = this.getAttribute('data-fullname');
            const phone = this.getAttribute('data-phone');
            const isAdmin = this.getAttribute('data-admin');
            
            editUserId.value = id;
            editUsername.value = username;
            editEmail.value = email;
            editFullName.value = fullName;
            editPhone.value = phone;
            editIsAdmin.value = isAdmin;
            
            editUserModal.style.display = 'block';
        });
    });
    
    // Delete user modal
    const deleteButtons = document.querySelectorAll('.delete-user');
    const deleteUserModal = document.getElementById('deleteUserModal');
    const deleteUserId = document.getElementById('delete_user_id');
    const deleteUserUsername = document.getElementById('delete_user_username');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            
            deleteUserId.value = id;
            deleteUserUsername.textContent = username;
            
            deleteUserModal.style.display = 'block';
        });
    });
    
    // Close modals
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });
});
</script>
