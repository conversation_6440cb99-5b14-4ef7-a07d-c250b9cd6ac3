<?php
/**
 * Admin Celebrities Management Page
 */

// Make sure uploads directory exists
$uploads_dir = __DIR__ . '/../../assets/uploads/';
if (!file_exists($uploads_dir)) {
    mkdir($uploads_dir, 0777, true);
}

// Define web path for uploads
define('UPLOADS_WEB_PATH', 'uploads/');

// Process form submission
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $message = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
        $message_type = 'danger';
    } else {
        // Get form data
        $celebrity_id = isset($_POST['celebrity_id']) ? (int)$_POST['celebrity_id'] : 0;
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $name_ar = isset($_POST['name_ar']) ? trim($_POST['name_ar']) : '';
        $role = isset($_POST['role']) ? trim($_POST['role']) : '';
        $role_ar = isset($_POST['role_ar']) ? trim($_POST['role_ar']) : '';
        $featured = isset($_POST['featured']) ? 1 : 0;
        $facebook = isset($_POST['facebook']) ? trim($_POST['facebook']) : '';
        $twitter = isset($_POST['twitter']) ? trim($_POST['twitter']) : '';
        $instagram = isset($_POST['instagram']) ? trim($_POST['instagram']) : '';
        $youtube = isset($_POST['youtube']) ? trim($_POST['youtube']) : '';
        $action = isset($_POST['action']) ? $_POST['action'] : '';

        // Handle file upload
        $image = '';

        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $image_tmp = $_FILES['image']['tmp_name'];
            $image_name = $_FILES['image']['name'];
            $image_ext = pathinfo($image_name, PATHINFO_EXTENSION);
            $image = 'celebrity_' . time() . '.' . $image_ext;

            // Move uploaded file
            if (move_uploaded_file($image_tmp, $uploads_dir . $image)) {
                // File uploaded successfully
            } else {
                $message = 'حدث خطأ أثناء تحميل الصورة.';
                $message_type = 'danger';
                $image = '';
            }
        }

        // Process based on action
        if ($action === 'delete') {
            // Delete celebrity
            if ($celebrity_id > 0) {
                // Get celebrity image to delete
                $get_image_sql = "SELECT image FROM celebrities WHERE id = ?";
                $get_image_stmt = $conn->prepare($get_image_sql);
                $get_image_stmt->bind_param('i', $celebrity_id);
                $get_image_stmt->execute();
                $get_image_result = $get_image_stmt->get_result();

                if ($get_image_result && $get_image_result->num_rows > 0) {
                    $image_file = $get_image_result->fetch_assoc()['image'];

                    // Delete image file
                    if (!empty($image_file)) {
                        @unlink($uploads_dir . $image_file);
                    }
                }

                // Delete celebrity from database
                $sql = "DELETE FROM celebrities WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('i', $celebrity_id);

                if ($stmt->execute()) {
                    $message = 'تم حذف النجم بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء حذف النجم: ' . $stmt->error;
                    $message_type = 'danger';
                }
            }
        } elseif ($action === 'edit') {
            // Update celebrity
            if ($celebrity_id > 0 && !empty($name) && !empty($name_ar)) {
                // Get current celebrity data
                $get_celebrity_sql = "SELECT image FROM celebrities WHERE id = ?";
                $get_celebrity_stmt = $conn->prepare($get_celebrity_sql);
                $get_celebrity_stmt->bind_param('i', $celebrity_id);
                $get_celebrity_stmt->execute();
                $get_celebrity_result = $get_celebrity_stmt->get_result();
                $current_celebrity = $get_celebrity_result->fetch_assoc();

                // Use current image if no new one uploaded
                if (empty($image)) {
                    $image = $current_celebrity['image'];
                } else {
                    // Delete old image file
                    if (!empty($current_celebrity['image'])) {
                        @unlink($uploads_dir . $current_celebrity['image']);
                    }
                }

                // Update celebrity in database
                $sql = "UPDATE celebrities SET name = ?, name_ar = ?, role = ?, role_ar = ?, image = ?, featured = ?,
                        facebook = ?, twitter = ?, instagram = ?, youtube = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('sssssissssi', $name, $name_ar, $role, $role_ar, $image, $featured,
                                 $facebook, $twitter, $instagram, $youtube, $celebrity_id);

                if ($stmt->execute()) {
                    $message = 'تم تحديث النجم بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تحديث النجم: ' . $stmt->error;
                    $message_type = 'danger';
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        } elseif ($action === 'add') {
            // Add new celebrity
            if (!empty($name) && !empty($name_ar) && !empty($image)) {
                $sql = "INSERT INTO celebrities (name, name_ar, role, role_ar, image, featured, facebook, twitter, instagram, youtube)
                       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('sssssissss', $name, $name_ar, $role, $role_ar, $image, $featured,
                                 $facebook, $twitter, $instagram, $youtube);

                if ($stmt->execute()) {
                    $message = 'تمت إضافة النجم بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء إضافة النجم: ' . $stmt->error;
                    $message_type = 'danger';
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        }
    }
}

// Get all celebrities
$celebrities_sql = "SELECT * FROM celebrities ORDER BY featured DESC, name";
$celebrities_result = $conn->query($celebrities_sql);
$celebrities = [];

if ($celebrities_result && $celebrities_result->num_rows > 0) {
    while ($row = $celebrities_result->fetch_assoc()) {
        $celebrities[] = $row;
    }
}
?>

<div class="celebrities-management">
    <h1 class="page-title">إدارة النجوم</h1>

    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?>">
        <?php echo $message; ?>
    </div>
    <?php endif; ?>

    <!-- Add New Celebrity Button -->
    <div class="action-buttons">
        <button class="btn btn-primary" id="addCelebrityBtn">
            <i class="fas fa-plus"></i> إضافة نجم جديد
        </button>
    </div>

    <!-- Celebrities List -->
    <div class="card">
        <div class="card-header">
            <h2>قائمة النجوم</h2>
        </div>
        <div class="card-body">
            <?php if (empty($celebrities)): ?>
            <p class="no-data">لا يوجد نجوم حتى الآن.</p>
            <?php else: ?>
            <div class="celebrities-grid">
                <?php foreach ($celebrities as $celebrity): ?>
                <div class="celebrity-card <?php echo $celebrity['featured'] ? 'featured' : ''; ?>">
                    <div class="celebrity-image">
                        <?php if (!empty($celebrity['image'])): ?>
                        <img src="<?php echo asset_url(UPLOADS_WEB_PATH . $celebrity['image']); ?>" alt="<?php echo htmlspecialchars($celebrity['name']); ?>">
                        <?php else: ?>
                        <div class="placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                        <?php endif; ?>
                        <?php if ($celebrity['featured']): ?>
                        <div class="featured-badge">
                            <i class="fas fa-star"></i>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="celebrity-info">
                        <h3><?php echo htmlspecialchars($celebrity['name']); ?></h3>
                        <h4><?php echo htmlspecialchars($celebrity['name_ar']); ?></h4>
                        <p><?php echo htmlspecialchars($celebrity['role']); ?> / <?php echo htmlspecialchars($celebrity['role_ar']); ?></p>
                        <div class="celebrity-actions">
                            <button class="btn btn-sm btn-primary edit-celebrity"
                                data-id="<?php echo $celebrity['id']; ?>"
                                data-name="<?php echo htmlspecialchars($celebrity['name']); ?>"
                                data-name-ar="<?php echo htmlspecialchars($celebrity['name_ar']); ?>"
                                data-role="<?php echo htmlspecialchars($celebrity['role']); ?>"
                                data-role-ar="<?php echo htmlspecialchars($celebrity['role_ar']); ?>"
                                data-image="<?php echo htmlspecialchars($celebrity['image']); ?>"
                                data-featured="<?php echo $celebrity['featured']; ?>"
                                data-facebook="<?php echo htmlspecialchars($celebrity['facebook'] ?? ''); ?>"
                                data-twitter="<?php echo htmlspecialchars($celebrity['twitter'] ?? ''); ?>"
                                data-instagram="<?php echo htmlspecialchars($celebrity['instagram'] ?? ''); ?>"
                                data-youtube="<?php echo htmlspecialchars($celebrity['youtube'] ?? ''); ?>">
                                <i class="fas fa-edit"></i> تحرير
                            </button>
                            <button class="btn btn-sm btn-danger delete-celebrity"
                                data-id="<?php echo $celebrity['id']; ?>"
                                data-name="<?php echo htmlspecialchars($celebrity['name']); ?>">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Celebrity Modal -->
<div class="modal" id="addCelebrityModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>إضافة نجم جديد</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=celebrities" method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="add">

                <div class="form-row">
                    <div class="form-group">
                        <label for="name">الاسم (الإنجليزية)</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="name_ar">الاسم (العربية)</label>
                        <input type="text" id="name_ar" name="name_ar" required dir="rtl">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="role">الدور (الإنجليزية)</label>
                        <input type="text" id="role" name="role">
                    </div>

                    <div class="form-group">
                        <label for="role_ar">الدور (العربية)</label>
                        <input type="text" id="role_ar" name="role_ar" dir="rtl">
                    </div>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="featured" id="featured" checked>
                        <span>نجم مميز (يظهر في الصفحة الرئيسية)</span>
                    </label>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="image">الصورة</label>
                        <input type="file" id="image" name="image" accept="image/*">
                        <div class="file-preview"></div>
                    </div>
                </div>

                <h3>روابط وسائل التواصل الاجتماعي (اختياري)</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="facebook">فيسبوك</label>
                        <input type="url" id="facebook" name="facebook" placeholder="https://facebook.com/username">
                    </div>

                    <div class="form-group">
                        <label for="twitter">تويتر</label>
                        <input type="url" id="twitter" name="twitter" placeholder="https://twitter.com/username">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="instagram">انستغرام</label>
                        <input type="url" id="instagram" name="instagram" placeholder="https://instagram.com/username">
                    </div>

                    <div class="form-group">
                        <label for="youtube">يوتيوب</label>
                        <input type="url" id="youtube" name="youtube" placeholder="https://youtube.com/channel/ID">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">إضافة نجم</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Celebrity Modal -->
<div class="modal" id="editCelebrityModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>تحرير النجم</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=celebrities" method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="celebrity_id" id="edit_celebrity_id">

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_name">الاسم (الإنجليزية)</label>
                        <input type="text" id="edit_name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_name_ar">الاسم (العربية)</label>
                        <input type="text" id="edit_name_ar" name="name_ar" required dir="rtl">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_role">الدور (الإنجليزية)</label>
                        <input type="text" id="edit_role" name="role">
                    </div>

                    <div class="form-group">
                        <label for="edit_role_ar">الدور (العربية)</label>
                        <input type="text" id="edit_role_ar" name="role_ar" dir="rtl">
                    </div>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="featured" id="edit_featured">
                        <span>نجم مميز (يظهر في الصفحة الرئيسية)</span>
                    </label>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_image">الصورة</label>
                        <input type="file" id="edit_image" name="image" accept="image/*">
                        <div class="file-preview" id="edit_image_preview"></div>
                        <small class="form-text">اترك هذا الحقل فارغًا للاحتفاظ بالصورة الحالية.</small>
                    </div>
                </div>

                <h3>روابط وسائل التواصل الاجتماعي (اختياري)</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_facebook">فيسبوك</label>
                        <input type="url" id="edit_facebook" name="facebook" placeholder="https://facebook.com/username">
                    </div>

                    <div class="form-group">
                        <label for="edit_twitter">تويتر</label>
                        <input type="url" id="edit_twitter" name="twitter" placeholder="https://twitter.com/username">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_instagram">انستغرام</label>
                        <input type="url" id="edit_instagram" name="instagram" placeholder="https://instagram.com/username">
                    </div>

                    <div class="form-group">
                        <label for="edit_youtube">يوتيوب</label>
                        <input type="url" id="edit_youtube" name="youtube" placeholder="https://youtube.com/channel/ID">
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Celebrity Modal -->
<div class="modal" id="deleteCelebrityModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>حذف النجم</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من أنك تريد حذف النجم "<span id="delete_celebrity_name"></span>"؟</p>
            <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>

            <form action="index.php?page=celebrities" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="celebrity_id" id="delete_celebrity_id">

                <div class="form-actions">
                    <button type="submit" class="btn btn-danger">حذف</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add celebrity modal
    const addCelebrityBtn = document.getElementById('addCelebrityBtn');
    const addCelebrityModal = document.getElementById('addCelebrityModal');

    if (addCelebrityBtn) {
        addCelebrityBtn.addEventListener('click', function() {
            addCelebrityModal.style.display = 'block';
        });
    }

    // Edit celebrity modal
    const editButtons = document.querySelectorAll('.edit-celebrity');
    const editCelebrityModal = document.getElementById('editCelebrityModal');
    const editCelebrityId = document.getElementById('edit_celebrity_id');
    const editName = document.getElementById('edit_name');
    const editNameAr = document.getElementById('edit_name_ar');
    const editRole = document.getElementById('edit_role');
    const editRoleAr = document.getElementById('edit_role_ar');
    const editImagePreview = document.getElementById('edit_image_preview');
    const editFacebook = document.getElementById('edit_facebook');
    const editTwitter = document.getElementById('edit_twitter');
    const editInstagram = document.getElementById('edit_instagram');
    const editYoutube = document.getElementById('edit_youtube');

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const nameAr = this.getAttribute('data-name-ar');
            const role = this.getAttribute('data-role');
            const roleAr = this.getAttribute('data-role-ar');
            const image = this.getAttribute('data-image');
            const featured = this.getAttribute('data-featured');
            const facebook = this.getAttribute('data-facebook');
            const twitter = this.getAttribute('data-twitter');
            const instagram = this.getAttribute('data-instagram');
            const youtube = this.getAttribute('data-youtube');

            editCelebrityId.value = id;
            editName.value = name;
            editNameAr.value = nameAr;
            editRole.value = role;
            editRoleAr.value = roleAr;
            editFacebook.value = facebook || '';
            editTwitter.value = twitter || '';
            editInstagram.value = instagram || '';
            editYoutube.value = youtube || '';
            document.getElementById('edit_featured').checked = featured === '1';

            // Show image preview
            editImagePreview.innerHTML = '';
            if (image) {
                const img = document.createElement('img');
                img.src = '<?php echo asset_url('uploads/'); ?>' + image;
                img.alt = name;
                editImagePreview.appendChild(img);
            }

            editCelebrityModal.style.display = 'block';
        });
    });

    // Delete celebrity modal
    const deleteButtons = document.querySelectorAll('.delete-celebrity');
    const deleteCelebrityModal = document.getElementById('deleteCelebrityModal');
    const deleteCelebrityId = document.getElementById('delete_celebrity_id');
    const deleteCelebrityName = document.getElementById('delete_celebrity_name');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');

            deleteCelebrityId.value = id;
            deleteCelebrityName.textContent = name;

            deleteCelebrityModal.style.display = 'block';
        });
    });

    // Close modals
    const modalCloseButtons = document.querySelectorAll('.modal-close');

    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });

    // File input preview
    const fileInputs = document.querySelectorAll('input[type="file"]');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const previewContainer = this.nextElementSibling;

            if (previewContainer && this.files && this.files[0]) {
                const file = this.files[0];
                previewContainer.innerHTML = '';

                if (file.type.startsWith('image/')) {
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.alt = file.name;
                    previewContainer.appendChild(img);
                } else if (file.type.startsWith('video/')) {
                    const video = document.createElement('video');
                    video.src = URL.createObjectURL(file);
                    video.controls = true;
                    video.muted = true;
                    previewContainer.appendChild(video);
                }
            }
        });
    });
});
</script>
