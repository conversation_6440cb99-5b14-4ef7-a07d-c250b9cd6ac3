/* Authentication Pages Styles */
.auth-section {
    padding: 60px 0;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: linear-gradient(to bottom, #000000, #111111);
    position: relative;
    overflow: hidden;
}

.auth-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(255, 215, 0, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(255, 215, 0, 0.05), transparent 70%);
    pointer-events: none;
}

.auth-container {
    max-width: 500px;
    margin: 0 auto;
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.8), rgba(10, 10, 10, 0.9));
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5);
    position: relative;
    border: 1px solid rgba(255, 215, 0, 0.1);
    overflow: hidden;
}

.auth-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, transparent, #ffd700, transparent);
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-header h1 {
    color: #ffd700;
    font-size: 2rem;
    margin-bottom: 10px;
}

.auth-header p {
    color: #aaa;
    font-size: 1rem;
}

.auth-social {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
}

.social-login {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px;
    border-radius: 8px;
    text-decoration: none;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.social-login i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.social-login.google {
    background: #DB4437;
}

.social-login.facebook {
    background: #4267B2;
}

.social-login:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.auth-divider {
    position: relative;
    text-align: center;
    margin: 25px 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
}

.auth-divider span {
    position: relative;
    background: #1a1a1a;
    padding: 0 15px;
    color: #aaa;
    font-size: 0.9rem;
}

.auth-form .form-group {
    margin-bottom: 20px;
}

.auth-form label {
    display: block;
    margin-bottom: 8px;
    color: #ddd;
    font-weight: 500;
}

.input-group {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
}

.auth-form .form-control {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    background: rgba(0, 0, 0, 0.2);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.auth-form .form-control:focus {
    border-color: #ffd700;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
    outline: none;
}

.auth-form .form-control::placeholder {
    color: #666;
}

.auth-form .form-control.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 5px;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #ffd700;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input[type="checkbox"] {
    margin-right: 8px;
    accent-color: #ffd700;
}

.remember-me label {
    margin-bottom: 0;
    color: #aaa;
    font-weight: normal;
    font-size: 0.9rem;
}

.forgot-password {
    color: #ffd700;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #ffdf4d;
    text-decoration: underline;
}

.terms-checkbox {
    display: flex;
    align-items: flex-start;
}

.terms-checkbox input[type="checkbox"] {
    margin-right: 8px;
    margin-top: 3px;
    accent-color: #ffd700;
}

.terms-checkbox label {
    margin-bottom: 0;
    color: #aaa;
    font-weight: normal;
    font-size: 0.9rem;
}

.terms-checkbox a {
    color: #ffd700;
    text-decoration: none;
    transition: color 0.3s ease;
}

.terms-checkbox a:hover {
    color: #ffdf4d;
    text-decoration: underline;
}

.form-submit {
    margin-top: 30px;
}

.btn-block {
    display: block;
    width: 100%;
}

.btn-primary {
    background: linear-gradient(145deg, #ffd700, #ccac00);
    color: #000;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary i {
    margin-right: 8px;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(145deg, #ffdf4d, #ddb700);
}

.auth-footer {
    text-align: center;
    margin-top: 30px;
    color: #aaa;
    font-size: 0.9rem;
}

.auth-footer a {
    color: #ffd700;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.auth-footer a:hover {
    color: #ffdf4d;
    text-decoration: underline;
}

.alert {
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.5);
    color: #dc3545;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .auth-container {
        padding: 30px 20px;
        margin: 0 15px;
    }
    
    .auth-header h1 {
        font-size: 1.8rem;
    }
    
    .auth-social {
        flex-direction: column;
    }
}
