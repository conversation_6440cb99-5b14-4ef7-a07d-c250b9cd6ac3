<?php
/**
 * Nominee Profile Page
 */

// Get current user
$user_id = is_logged_in() ? $_SESSION['user_id'] : 0;
$is_logged_in = is_logged_in();

// Get nominee ID from URL
$nominee_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Get nominee data
$nominee_data = null;
if ($nominee_id > 0) {
    // Check if the table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_nominees'");
    if ($check_table->num_rows > 0) {
        // Get nominee details
        $nominee_sql = "SELECT * FROM voting_nominees WHERE id = ?";
        $stmt = $conn->prepare($nominee_sql);
        $stmt->bind_param("i", $nominee_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $nominee_data = $result->fetch_assoc();
        }
        
        $stmt->close();
    }
}

// If nominee not found, redirect to home
if (!$nominee_data) {
    header('Location: ' . base_url());
    exit;
}

// Get subcategory and category data
$subcategory = get_subcategory($nominee_data['subcategory_id']);
$category = $subcategory ? get_main_category($subcategory['category_id']) : null;

// Process vote
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['vote']) && $is_logged_in) {
    // Check if user has already voted for this nominee
    if (!has_voted_for_nominee($user_id, $nominee_id)) {
        // Add vote
        if (add_vote($user_id, $nominee_id)) {
            $success_message = get_content('vote', 'vote_success', 'Your vote has been recorded successfully!');
            
            // Refresh nominee data to get updated votes count
            $stmt = $conn->prepare($nominee_sql);
            $stmt->bind_param("i", $nominee_id);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                $nominee_data = $result->fetch_assoc();
            }
            
            $stmt->close();
        } else {
            $error_message = get_content('vote', 'vote_error', 'An error occurred while processing your vote. Please try again.');
        }
    } else {
        $error_message = get_content('vote', 'already_voted', 'You have already voted for this nominee.');
    }
}

// Set page title
$page_title = $nominee_data['name'] . ' - ' . ($category ? get_main_category_name($category) . ' / ' : '') . ($subcategory ? get_subcategory_name($subcategory) : '');
?>

<!-- Nominee Profile Page -->
<section class="nominee-page">
    <div class="container">
        <div class="nominee-header">
            <h1><?php echo htmlspecialchars($nominee_data['name']); ?></h1>
            <?php if ($category && $subcategory): ?>
            <div class="nominee-category">
                <a href="?page=vote&main_category=<?php echo $category['id']; ?>" class="category-link">
                    <?php echo htmlspecialchars(get_main_category_name($category)); ?>
                </a>
                <span class="separator">/</span>
                <a href="?page=vote&main_category=<?php echo $category['id']; ?>&subcategory=<?php echo $subcategory['id']; ?>" class="subcategory-link">
                    <?php echo htmlspecialchars(get_subcategory_name($subcategory)); ?>
                </a>
            </div>
            <?php endif; ?>
        </div>

        <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <div class="nominee-profile-content">
            <div class="nominee-profile-image">
                <img src="<?php echo asset_url('uploads/' . $nominee_data['image']); ?>" alt="<?php echo htmlspecialchars($nominee_data['name']); ?>">
            </div>
            <div class="nominee-profile-info">
                <div class="nominee-bio">
                    <h2><?php echo get_content('nominee', 'about', 'About'); ?></h2>
                    <p><?php echo $current_lang === 'ar' ? $nominee_data['bio_ar'] : $nominee_data['bio']; ?></p>
                </div>

                <div class="nominee-stats">
                    <div class="votes-count">
                        <i class="fas fa-vote-yea"></i>
                        <span><?php echo (int)$nominee_data['votes_count']; ?></span>
                        <span class="votes-label"><?php echo get_content('vote', 'votes', 'Votes'); ?></span>
                    </div>
                </div>

                <div class="nominee-social">
                    <?php if (!empty($nominee_data['facebook'])): ?>
                    <a href="<?php echo htmlspecialchars($nominee_data['facebook']); ?>" class="social-icon" title="Facebook" target="_blank">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($nominee_data['twitter'])): ?>
                    <a href="<?php echo htmlspecialchars($nominee_data['twitter']); ?>" class="social-icon" title="Twitter" target="_blank">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($nominee_data['instagram'])): ?>
                    <a href="<?php echo htmlspecialchars($nominee_data['instagram']); ?>" class="social-icon" title="Instagram" target="_blank">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <?php endif; ?>
                </div>

                <div class="nominee-actions">
                    <?php if ($is_logged_in): ?>
                    <form action="" method="post" class="vote-form">
                        <input type="hidden" name="nominee_id" value="<?php echo $nominee_data['id']; ?>">
                        <button type="submit" name="vote" class="btn btn-vote <?php echo has_voted_for_nominee($user_id, $nominee_data['id']) ? 'voted' : ''; ?>">
                            <i class="fas fa-vote-yea"></i>
                            <?php echo has_voted_for_nominee($user_id, $nominee_data['id'])
                                ? get_content('vote', 'already_voted_button', 'Already Voted')
                                : get_content('vote', 'vote_button', 'Vote Now'); ?>
                        </button>
                    </form>
                    <?php else: ?>
                    <div class="login-prompt">
                        <p><?php echo get_content('vote', 'login_required', 'You need to login to vote.'); ?></p>
                        <a href="?page=login&redirect=nominee&id=<?php echo $nominee_id; ?>" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> <?php echo get_content('vote', 'login_button', 'Login Now'); ?>
                        </a>
                    </div>
                    <?php endif; ?>

                    <div class="share-buttons">
                        <span><?php echo get_content('vote', 'share', 'Share:'); ?></span>
                        <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(current_url() . '?page=nominee&id=' . $nominee_data['id']); ?>" target="_blank" class="share-button facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(current_url() . '?page=nominee&id=' . $nominee_data['id']); ?>&text=<?php echo urlencode(get_content('vote', 'share_text', 'Vote for') . ' ' . $nominee_data['name']); ?>" target="_blank" class="share-button twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="https://api.whatsapp.com/send?text=<?php echo urlencode(get_content('vote', 'share_text', 'Vote for') . ' ' . $nominee_data['name'] . ': ' . current_url() . '?page=nominee&id=' . $nominee_data['id']); ?>" target="_blank" class="share-button whatsapp">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($subcategory): ?>
        <!-- Other Nominees in Same Subcategory -->
        <div class="other-nominees">
            <h2><?php echo get_content('nominee', 'other_nominees', 'Other Nominees in this Category'); ?></h2>
            <div class="nominees-grid">
                <?php 
                $other_nominees = get_nominees_by_subcategory($subcategory['id']);
                foreach ($other_nominees as $other_nominee):
                    if ($other_nominee['id'] == $nominee_id) continue; // Skip current nominee
                ?>
                <div class="nominee-card">
                    <a href="?page=nominee&id=<?php echo $other_nominee['id']; ?>" class="nominee-link">
                        <div class="nominee-image">
                            <img src="<?php echo asset_url('uploads/' . $other_nominee['image']); ?>" alt="<?php echo htmlspecialchars($other_nominee['name']); ?>">
                        </div>
                        <div class="nominee-info">
                            <h3><?php echo htmlspecialchars($other_nominee['name']); ?></h3>
                            <div class="nominee-votes">
                                <i class="fas fa-vote-yea"></i>
                                <span class="votes-count"><?php echo (int)$other_nominee['votes_count']; ?></span>
                                <span class="votes-label"><?php echo get_content('vote', 'votes', 'Votes'); ?></span>
                            </div>
                        </div>
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>
