<?php
/**
 * Vote Page
 */

// Get current user
$user_id = is_logged_in() ? $_SESSION['user_id'] : 0;
$is_logged_in = is_logged_in();

// Get all main categories
$main_categories = get_main_categories();

// Get selected main category
$selected_main_category = isset($_GET['main_category']) ? (int)$_GET['main_category'] : 0;

// Get subcategories for selected main category
$subcategories = [];
if ($selected_main_category > 0) {
    $subcategories = get_subcategories($selected_main_category);
}

// Get selected subcategory
$selected_subcategory = isset($_GET['subcategory']) ? (int)$_GET['subcategory'] : 0;

// Get nominees for selected subcategory
$nominees = [];
if ($selected_subcategory > 0) {
    $nominees = get_nominees_by_subcategory($selected_subcategory);
}

// Get selected nominee for profile view
$selected_nominee = isset($_GET['nominee']) ? (int)$_GET['nominee'] : 0;
$nominee_data = null;
if ($selected_nominee > 0) {
    $nominee_data = get_nominee($selected_nominee);
}

// Process vote
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['vote']) && $is_logged_in) {
    $nominee_id = (int)$_POST['nominee_id'];

    // Check if user has already voted for this nominee
    if (!has_voted_for_nominee($user_id, $nominee_id)) {
        // Add vote
        if (add_vote($user_id, $nominee_id)) {
            $success_message = get_content('vote', 'vote_success', 'Your vote has been recorded successfully!');
        } else {
            $error_message = get_content('vote', 'vote_error', 'An error occurred while processing your vote. Please try again.');
        }
    } else {
        $error_message = get_content('vote', 'already_voted', 'You have already voted for this nominee.');
    }
}
?>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200..1000&display=swap" rel="stylesheet">
<!-- Vote Page -->
<section class="vote-section">
    <div class="container" style="margin-top: 100px;">
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success">
            <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger">
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <?php if ($nominee_data): ?>
        <!-- Nominee Profile View -->
        <div class="nominee-profile">
            <div class="back-link">
                <a href="?page=vote&main_category=<?php echo $selected_main_category; ?>&subcategory=<?php echo $selected_subcategory; ?>">
                    <i class="fas fa-arrow-left"></i> <?php echo get_content('vote', 'back_to_nominees', 'Back to Nominees'); ?>
                </a>
                <a href="?page=nominee&id=<?php echo $nominee_data['id']; ?>" class="view-profile-link">
                    <i class="fas fa-user"></i> <?php echo get_content('vote', 'view_profile', 'View Full Profile'); ?>
                </a>
            </div>

            <div class="nominee-profile-content">
                <div class="nominee-profile-image">
                    <img src="<?php echo asset_url('uploads/' . $nominee_data['image']); ?>" alt="<?php echo htmlspecialchars($nominee_data['name']); ?>">
                </div>
                <div class="nominee-profile-info">
                    <h2><?php echo htmlspecialchars($nominee_data['name']); ?></h2>
                    <p class="nominee-category">
                        <?php
                        $subcategory = get_subcategory($nominee_data['subcategory_id']);
                        $category = get_main_category($subcategory['category_id']);
                        echo get_main_category_name($category) . ' / ' . get_subcategory_name($subcategory);
                        ?>
                    </p>
                    <div class="nominee-bio">
                        <p><?php echo $current_lang === 'ar' ? $nominee_data['bio_ar'] : $nominee_data['bio']; ?></p>
                    </div>

                    <div class="nominee-stats">
                        <div class="votes-count">
                            <i class="fas fa-vote-yea"></i>
                            <span><?php echo get_nominee_votes_count($nominee_data['id']); ?></span>
                            <span class="votes-label"><?php echo get_content('vote', 'votes', 'Votes'); ?></span>
                        </div>
                    </div>

                    <div class="nominee-social">
                        <?php if (!empty($nominee_data['facebook'])): ?>
                        <a href="<?php echo htmlspecialchars($nominee_data['facebook']); ?>" class="social-icon" title="Facebook" target="_blank">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <?php endif; ?>

                        <?php if (!empty($nominee_data['twitter'])): ?>
                        <a href="<?php echo htmlspecialchars($nominee_data['twitter']); ?>" class="social-icon" title="Twitter" target="_blank">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <?php endif; ?>

                        <?php if (!empty($nominee_data['instagram'])): ?>
                        <a href="<?php echo htmlspecialchars($nominee_data['instagram']); ?>" class="social-icon" title="Instagram" target="_blank">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <?php endif; ?>
                    </div>

                    <div class="nominee-actions">
                        <?php if ($is_logged_in): ?>
                        <form action="" method="post" class="vote-form">
                            <input type="hidden" name="nominee_id" value="<?php echo $nominee_data['id']; ?>">
                            <button type="submit" name="vote" class="btn btn-vote <?php echo has_voted_for_nominee($user_id, $nominee_data['id']) ? 'voted' : ''; ?>">
                                <i class="fas fa-vote-yea"></i>
                                <?php echo has_voted_for_nominee($user_id, $nominee_data['id'])
                                    ? get_content('vote', 'already_voted_button', 'Already Voted')
                                    : get_content('vote', 'vote_button', 'Vote Now'); ?>
                            </button>
                        </form>
                        <?php endif; ?>

                        <div class="share-buttons">
                            <span><?php echo get_content('vote', 'share', 'Share:'); ?></span>
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(current_url() . '?page=vote&nominee=' . $nominee_data['id']); ?>" target="_blank" class="share-button facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(current_url() . '?page=vote&nominee=' . $nominee_data['id']); ?>&text=<?php echo urlencode(get_content('vote', 'share_text', 'Vote for') . ' ' . $nominee_data['name']); ?>" target="_blank" class="share-button twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://api.whatsapp.com/send?text=<?php echo urlencode(get_content('vote', 'share_text', 'Vote for') . ' ' . $nominee_data['name'] . ': ' . current_url() . '?page=vote&nominee=' . $nominee_data['id']); ?>" target="_blank" class="share-button whatsapp">
                                <i class="fab fa-whatsapp"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="vote-container">
            <!-- Main Categories Selection - Fixed at top -->
            <div class="main-categories-section">
                <div class="main-categories-slider">
                    <?php if (empty($main_categories)): ?>
                    <div class="no-categories-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <p><?php echo get_content('vote', 'no_categories', 'No categories available yet. Please check back later.'); ?></p>
                    </div>
                    <?php else: ?>
                    <div class="categories-scroll">
                        <?php foreach ($main_categories as $category): ?>
                        <a href="?page=vote&main_category=<?php echo $category['id']; ?>" style="text-align: center; font-weight: bold; font-family: 'Cairo';">
                            <div class="category-icon">
                                <?php if (!empty($category['image'])): ?>
                                <img src="<?php echo asset_url('uploads/' . $category['image']); ?>"   style="margin-right: 0px; " class="main-category-item <?php echo $selected_main_category == $category['id'] ? 'active' : ''; ?>"  alt="<?php echo htmlspecialchars(get_main_category_name($category)); ?>">
                                <?php else: ?>
                                <i class="fas fa-award"></i>
                                <?php endif; ?>
                            </div>
                            <div class="category-name">
                                <?php echo htmlspecialchars(get_main_category_name($category)); ?>
                            </div>
                        </a>
                        
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="voting-content">
                <?php if ($selected_main_category > 0 && !empty($subcategories)): ?>
                <!-- Subcategories Selection -->
                <div class="subcategories-section">
                    <div class="subcategories-slider">
                        <div class="subcategories-scroll">
                            <?php foreach ($subcategories as $subcategory): ?>
                            <a href="?page=vote&main_category=<?php echo $selected_main_category; ?>&subcategory=<?php echo $subcategory['id']; ?>"  style="text-align: center; font-weight: bold; font-family: 'Cairo';">
                                <div class="subcategory-icon">
                                    <?php if (!empty($subcategory['image'])): ?>
                                    <img src="<?php echo asset_url('uploads/' . $subcategory['image']); ?>"   class="subcategory-item <?php echo $selected_subcategory == $subcategory['id'] ? 'active' : ''; ?>" alt="<?php echo htmlspecialchars(get_subcategory_name($subcategory)); ?>">
                                    <?php else: ?>
                                    <i class="fas fa-trophy"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="subcategory-name">
                                    <?php echo htmlspecialchars(get_subcategory_name($subcategory)); ?>
                                </div>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <?php if ($selected_subcategory > 0 && !empty($nominees)): ?>
                <!-- Nominees List -->
                <div class="nominees-section">
                    <div class="nominees-container">
                        <div class="nominees-grid">
                            <?php
                            // Sort nominees by votes count (descending)
                            usort($nominees, function($a, $b) {
                                return $b['votes_count'] - $a['votes_count'];
                            });

                            foreach ($nominees as $nominee):
                            ?>
                            <div class="nominee-card">
                                <div class="nominee-image">
                                    <img src="<?php echo asset_url('uploads/' . $nominee['image']); ?>" alt="<?php echo htmlspecialchars($nominee['name']); ?>">
                                    <div class="nominee-rank">
                                        <span class="rank-number">#<?php echo array_search($nominee, $nominees) + 1; ?></span>
                                    </div>
                                </div>
                                <div class="nominee-info">
                                    <h3 class="nominee-name"><?php echo htmlspecialchars($nominee['name']); ?></h3>
                                    <div class="nominee-description">
                                        <?php
                                        $subcategory = get_subcategory($nominee['subcategory_id']);
                                        $category = get_main_category($subcategory['category_id']);
                                        echo get_main_category_name($category) . ' / ' . get_subcategory_name($subcategory);
                                        ?>
                                    </div>
                                    <div class="nominee-meta">
                                        <div class="nominee-votes">
                                            <i class="fas fa-vote-yea"></i>
                                            <span><?php echo (int)$nominee['votes_count']; ?> <?php echo get_content('vote', 'votes', 'Votes'); ?></span>
                                        </div>
                                        <div class="nominee-actions">
                                            <a href="?page=nominee&id=<?php echo $nominee['id']; ?>" class="btn-view">
                                                <i class="fas fa-eye"></i> <?php echo get_content('vote', 'view_profile', 'View'); ?>
                                            </a>

                                            <?php if ($is_logged_in): ?>
                                                <?php if (!has_voted_for_nominee($user_id, $nominee['id'])): ?>
                                                <form action="" method="post" class="quick-vote-form">
                                                    <input type="hidden" name="nominee_id" value="<?php echo $nominee['id']; ?>">
                                                    <button type="submit" name="vote" class="btn-vote">
                                                        <i class="fas fa-vote-yea"></i> <?php echo get_content('vote', 'vote_button', 'Vote'); ?>
                                                    </button>
                                                </form>
                                                <?php else: ?>
                                                <span class="voted-badge"><i class="fas fa-check-circle"></i> <?php echo get_content('vote', 'voted', 'Voted'); ?></span>
                                                <?php endif; ?>

                                                <a href="<?php echo base_url('?page=compare&nominee1=' . $nominee['id'] . '&subcategory=' . $selected_subcategory); ?>" class="btn-compare">
                                                    <i class="fas fa-exchange-alt"></i> <?php echo get_content('vote', 'compare', 'Compare'); ?>
                                                </a>
                                            <?php else: ?>
                                                <a href="?page=login&redirect=vote&main_category=<?php echo $selected_main_category; ?>&subcategory=<?php echo $selected_subcategory; ?>" class="btn-vote">
                                                    <i class="fas fa-sign-in-alt"></i> <?php echo get_content('vote', 'login_to_vote', 'Login'); ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>
