/**
 * Advanced Lazy Loading System
 * نظام تحميل بطيء متقدم للصور والفيديوهات
 */

class LazyLoader {
    constructor() {
        this.imageObserver = null;
        this.videoObserver = null;
        this.loadedImages = new Set();
        this.loadedVideos = new Set();
        this.init();
    }

    init() {
        // Check if Intersection Observer is supported
        if ('IntersectionObserver' in window) {
            this.setupImageObserver();
            this.setupVideoObserver();
            this.observeElements();
        } else {
            // Fallback for older browsers
            this.loadAllImagesAndVideos();
        }

        // Add loading animations
        this.addLoadingAnimations();
        
        // Setup progressive loading
        this.setupProgressiveLoading();
    }

    setupImageObserver() {
        const imageOptions = {
            root: null,
            rootMargin: '50px 0px', // Start loading 50px before element enters viewport
            threshold: 0.1
        };

        this.imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    this.imageObserver.unobserve(entry.target);
                }
            });
        }, imageOptions);
    }

    setupVideoObserver() {
        const videoOptions = {
            root: null,
            rootMargin: '100px 0px', // Start loading 100px before element enters viewport
            threshold: 0.25
        };

        this.videoObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadVideo(entry.target);
                    this.videoObserver.unobserve(entry.target);
                }
            });
        }, videoOptions);
    }

    observeElements() {
        // Observe images
        const lazyImages = document.querySelectorAll('img[data-src], img[data-lazy], .lazy-load');
        lazyImages.forEach(img => {
            if (this.imageObserver) {
                this.imageObserver.observe(img);
            } else {
                // Fallback - load immediately
                this.loadImage(img);
            }
        });

        // Observe videos
        const lazyVideos = document.querySelectorAll('video[data-src], video[data-lazy], .lazy-video');
        lazyVideos.forEach(video => {
            if (this.videoObserver) {
                this.videoObserver.observe(video);
            } else {
                // Fallback - load immediately
                this.loadVideo(video);
            }
        });

        // Observe video sources
        const videoSources = document.querySelectorAll('video source[data-src]');
        videoSources.forEach(source => {
            const video = source.parentElement;
            if (this.videoObserver) {
                this.videoObserver.observe(video);
            } else {
                this.loadVideoSource(source);
            }
        });

        // Observe background images
        const lazyBackgrounds = document.querySelectorAll('[data-bg]');
        lazyBackgrounds.forEach(element => {
            if (this.imageObserver) {
                this.imageObserver.observe(element);
            } else {
                this.loadBackground(element);
            }
        });
    }

    loadImage(img) {
        if (this.loadedImages.has(img)) return;

        const src = img.dataset.src || img.dataset.lazy;
        if (!src) {
            // If no data-src, just mark as loaded
            img.classList.add('loaded');
            return;
        }

        // Set loading state
        img.classList.add('loading');

        // Create a new image to preload
        const imageLoader = new Image();

        imageLoader.onload = () => {
            // Set the source
            img.src = src;
            img.removeAttribute('data-src');
            img.removeAttribute('data-lazy');

            // Remove loading and add loaded class
            img.classList.remove('loading');
            img.classList.add('loaded');

            this.loadedImages.add(img);
        };

        imageLoader.onerror = () => {
            // Handle error - show fallback or default image
            const fallback = img.dataset.fallback || img.getAttribute('data-fallback') || 'assets/img/default-gallery.jpg';
            img.src = fallback;
            img.classList.remove('loading');
            img.classList.add('error');
        };

        // Start loading
        imageLoader.src = src;
    }

    loadVideo(video) {
        if (this.loadedVideos.has(video)) return;

        // Check for video sources with data-src
        const sources = video.querySelectorAll('source[data-src]');
        if (sources.length > 0) {
            sources.forEach(source => this.loadVideoSource(source));
        } else {
            const src = video.dataset.src || video.dataset.lazy;
            if (src) {
                video.src = src;
                video.removeAttribute('data-src');
                video.removeAttribute('data-lazy');
            }
        }

        // Load video metadata
        video.load();
        video.classList.add('loaded');
        this.loadedVideos.add(video);
    }

    loadVideoSource(source) {
        const src = source.dataset.src;
        if (src) {
            source.src = src;
            source.removeAttribute('data-src');
        }
    }

    loadBackground(element) {
        const bg = element.dataset.bg;
        if (bg) {
            element.style.backgroundImage = `url(${bg})`;
            element.removeAttribute('data-bg');
            element.classList.add('loaded');
        }
    }

    // Simplified placeholder system - just use CSS
    addImagePlaceholder(img) {
        // Simple CSS-based placeholder
        img.classList.add('lazy-loading');
    }

    addVideoPlaceholder(video) {
        // Simple CSS-based placeholder
        video.classList.add('lazy-loading');
    }

    removePlaceholder(element) {
        element.classList.remove('lazy-loading');
    }

    addLoadingAnimations() {
        // Add simple CSS for loading animations
        const style = document.createElement('style');
        style.textContent = `
            .lazy-loading {
                background: #f0f0f0;
                position: relative;
                overflow: hidden;
            }

            .lazy-loading::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
                animation: shimmer 1.5s infinite;
                z-index: 1;
            }

            @keyframes shimmer {
                0% { left: -100%; }
                100% { left: 100%; }
            }

            .lazy-load {
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .lazy-load.loaded {
                opacity: 1;
            }

            .lazy-load.loading {
                opacity: 0.5;
            }

            .lazy-load.error {
                opacity: 0.7;
                filter: grayscale(50%);
            }

            .lazy-video.loading {
                background: #000;
            }
        `;
        document.head.appendChild(style);
    }

    setupProgressiveLoading() {
        // Progressive loading for large images
        const progressiveImages = document.querySelectorAll('img[data-src-small]');
        progressiveImages.forEach(img => {
            const smallSrc = img.dataset.srcSmall;
            if (smallSrc) {
                img.src = smallSrc;
                img.style.filter = 'blur(5px)';
                img.style.transition = 'filter 0.5s ease';
            }
        });
    }

    loadAllImagesAndVideos() {
        // Fallback for browsers without Intersection Observer
        const lazyImages = document.querySelectorAll('img[data-src], img[data-lazy]');
        const lazyVideos = document.querySelectorAll('video[data-src], video[data-lazy]');

        lazyImages.forEach(img => this.loadImage(img));
        lazyVideos.forEach(video => this.loadVideo(video));
    }

    // Public method to manually load an element
    loadElement(element) {
        if (element.tagName === 'IMG') {
            this.loadImage(element);
        } else if (element.tagName === 'VIDEO') {
            this.loadVideo(element);
        }
    }

    // Public method to preload critical images
    preloadCritical(selectors) {
        const criticalElements = document.querySelectorAll(selectors);
        criticalElements.forEach(element => {
            this.loadElement(element);
        });
    }
}

// Initialize lazy loading when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.lazyLoader = new LazyLoader();

    // Preload critical above-the-fold images
    window.lazyLoader.preloadCritical('.hero img, .header img, .logo img');

    // Simple fallback for immediate loading
    const immediateImages = document.querySelectorAll('img:not(.lazy-load)');
    immediateImages.forEach(img => {
        if (!img.src && img.dataset.src) {
            img.src = img.dataset.src;
        }
    });
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LazyLoader;
}
