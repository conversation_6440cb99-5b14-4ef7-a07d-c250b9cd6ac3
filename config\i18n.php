<?php
/**
 * Internationalization Configuration
 *
 * This file contains functions for handling multilingual support.
 */

// Available languages
$available_languages = [
    'en' => [
        'code' => 'en',
        'name' => 'English',
        'locale' => 'en_US',
        'direction' => 'ltr',
        'flag' => 'us.png'
    ],
    'ar' => [
        'code' => 'ar',
        'name' => 'العربية',
        'locale' => 'ar_SA',
        'direction' => 'rtl',
        'flag' => 'sa.png'
    ]
];
if (!defined('DEFAULT_LANG') || !in_array(DEFAULT_LANG, ['en', 'ar'])) {
    define('DEFAULT_LANG', 'en');
}
// Set default language
$current_lang = DEFAULT_LANG;

// Initialize language
function init_language() {
    global $current_lang, $available_languages;

    // Make sure current_lang has a valid default value
    if (!isset($current_lang) || !array_key_exists($current_lang, $available_languages)) {
        $current_lang = DEFAULT_LANG;
    }

    // Check if language is set in session
    if (isset($_SESSION['lang']) && array_key_exists($_SESSION['lang'], $available_languages)) {
        $current_lang = $_SESSION['lang'];
    }

    // Check if language is set in URL
    if (isset($_GET['lang']) && array_key_exists($_GET['lang'], $available_languages)) {
        $current_lang = $_GET['lang'];
        $_SESSION['lang'] = $current_lang;
    }

    // Double check that current_lang is valid
    if (!array_key_exists($current_lang, $available_languages)) {
        $current_lang = DEFAULT_LANG;
    }

    // Set locale
    if (isset($available_languages[$current_lang]['locale'])) {
        @setlocale(LC_ALL, $available_languages[$current_lang]['locale']);
    }

    return $current_lang;
}

// Get current language
function get_current_language() {
    global $current_lang;
    return $current_lang;
}

// Get language direction
function get_language_direction() {
    global $current_lang, $available_languages;

    // Make sure current_lang is valid
    if (!isset($current_lang) || !array_key_exists($current_lang, $available_languages)) {
        $current_lang = DEFAULT_LANG;
    }

    // Make sure direction is set
    if (isset($available_languages[$current_lang]['direction'])) {
        return $available_languages[$current_lang]['direction'];
    }

    // Default to LTR if direction is not set
    return 'ltr';
}

// Check if current language is RTL
function is_rtl() {
    return get_language_direction() === 'rtl';
}

// Get translated content from database
function get_content($section, $key, $default = '') {
    global $conn, $current_lang;

    static $content_cache = [];

    // Create a unique cache key based on section, key, and language
    $cache_key = $section . '_' . $key . '_' . $current_lang;

    // Return from cache if already set
    if (isset($content_cache[$cache_key])) {
        return $content_cache[$cache_key];
    }

    // Check if $conn is a valid mysqli connection
    if (!isset($conn) || !$conn instanceof mysqli) {
        $content_cache[$cache_key] = $default;
        return $default;
    }

    // Prepare SQL query
    $sql = "SELECT * FROM content WHERE section = ? AND key_name = ?";
    $stmt = $conn->prepare($sql);

    // Check if prepare failed
    if (!$stmt) {
        $content_cache[$cache_key] = $default;
        return $default;
    }

    // Bind and execute
    $stmt->bind_param("ss", $section, $key);
    $stmt->execute();
    $result = $stmt->get_result();

    // Fetch result
    $content = $result ? $result->fetch_assoc() : false;

    if (is_array($content)) {
        // Check if translation fields exist
        if ($current_lang === 'ar' && isset($content['value_ar'])) {
            $value = $content['value_ar'];
        } elseif (isset($content['value_en'])) {
            $value = $content['value_en'];
        } else {
            $value = $default;
        }

        // Store in cache
        $content_cache[$cache_key] = $value;
        return $value;
    }

    // No result or invalid content
    $content_cache[$cache_key] = $default;
    return $default;
}


// Get language switcher HTML with preserved URL parameters
function language_switcher() {
    global $available_languages, $current_lang;

    // Preserve current page and parameters when switching language
    $current_url = $_SERVER['REQUEST_URI'];
    $url_parts = parse_url($current_url);

    // Check if parse_url returned false
    if ($url_parts === false) {
        // Fallback to default values
        $path = '/';
        $query = [];
    } else {
        // Set default path if not present
        $path = isset($url_parts['path']) ? $url_parts['path'] : '/';
        $query = [];

        if (isset($url_parts['query'])) {
            parse_str($url_parts['query'], $query);
        }
    }

    // Build language switcher HTML
    $html = '<div class="language-switcher">';

    // For simplified version with just EN/AR
    $query_en = $query;
    $query_ar = $query;

    $query_en['lang'] = 'en';
    $query_ar['lang'] = 'ar';

    // Build URLs with proper error handling
    $en_url = $path . '?' . http_build_query($query_en);
    $ar_url = $path . '?' . http_build_query($query_ar);

    // Ensure URLs are properly formatted
    $en_url = htmlspecialchars($en_url, ENT_QUOTES, 'UTF-8');
    $ar_url = htmlspecialchars($ar_url, ENT_QUOTES, 'UTF-8');

    $html .= '<a href="' . $en_url . '" class="' . ($current_lang === 'en' ? 'active' : '') . '">EN</a>';
    $html .= '<span class="separator">|</span>';
    $html .= '<a href="' . $ar_url . '" class="' . ($current_lang === 'ar' ? 'active' : '') . '">عربي</a>';

    $html .= '</div>';

    return $html;
}

// Initialize language
init_language();
