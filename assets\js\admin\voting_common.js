/**
 * Common JavaScript for all voting-related pages
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add animation to cards
    animateCards();
    
    // Add ripple effect to buttons
    initRippleEffect();
    
    // Add auto-resize to textareas
    initTextareaAutoResize();
    
    // Add form validation
    initFormValidation();
    
    // Add image preview
    initImagePreview();
    
    // Add confirmation for delete actions
    initDeleteConfirmation();
    
    // Add tooltips
    initTooltips();
});

/**
 * Add animation to cards
 */
function animateCards() {
    const cards = document.querySelectorAll('.card');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.3s ease-out, transform 0.3s ease-out';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + (index * 100));
    });
}

/**
 * Add ripple effect to buttons
 */
function initRippleEffect() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Skip if the button is inside a form with delete action
            if (this.closest('form.delete-form')) return;
            
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * Add auto-resize to textareas
 */
function initTextareaAutoResize() {
    const textareas = document.querySelectorAll('textarea');
    
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
        
        // Trigger the event on load
        const event = new Event('input');
        textarea.dispatchEvent(event);
    });
}

/**
 * Add form validation
 */
function initFormValidation() {
    const forms = document.querySelectorAll('form:not(.delete-form)');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredInputs = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredInputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('is-invalid');
                    
                    // Add error message if it doesn't exist
                    const errorMessage = input.parentElement.querySelector('.error-message');
                    if (!errorMessage) {
                        const error = document.createElement('div');
                        error.className = 'error-message';
                        error.textContent = 'هذا الحقل مطلوب';
                        input.parentElement.appendChild(error);
                    }
                } else {
                    input.classList.remove('is-invalid');
                    
                    // Remove error message if it exists
                    const errorMessage = input.parentElement.querySelector('.error-message');
                    if (errorMessage) {
                        errorMessage.remove();
                    }
                }
            });
            
            if (!isValid) {
                e.preventDefault();
            }
        });
    });
}

/**
 * Add image preview
 */
function initImagePreview() {
    const imageInputs = document.querySelectorAll('input[type="file"]');
    
    imageInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            if (!file) return;
            
            // Check if it's an image
            if (!file.type.match('image.*')) {
                alert('الرجاء اختيار ملف صورة');
                this.value = '';
                return;
            }
            
            // Create preview container if it doesn't exist
            let previewContainer = this.parentElement.querySelector('.image-preview');
            if (!previewContainer) {
                previewContainer = document.createElement('div');
                previewContainer.className = 'image-preview';
                previewContainer.style.marginTop = '10px';
                previewContainer.style.textAlign = 'center';
                this.parentElement.appendChild(previewContainer);
            }
            
            // Clear previous preview
            previewContainer.innerHTML = '';
            
            // Create image element
            const img = document.createElement('img');
            img.style.maxWidth = '200px';
            img.style.maxHeight = '200px';
            img.style.borderRadius = '5px';
            img.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
            previewContainer.appendChild(img);
            
            // Create caption
            const caption = document.createElement('p');
            caption.textContent = 'معاينة الصورة';
            caption.style.marginTop = '5px';
            caption.style.fontSize = '0.9rem';
            caption.style.color = '#aaa';
            previewContainer.appendChild(caption);
            
            // Read the image file
            const reader = new FileReader();
            reader.onload = function(e) {
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        });
    });
}

/**
 * Add confirmation for delete actions
 */
function initDeleteConfirmation() {
    const deleteForms = document.querySelectorAll('.delete-form');
    
    deleteForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const confirmMessage = this.getAttribute('data-confirm') || 'هل أنت متأكد من حذف هذا العنصر؟';
            
            if (!confirm(confirmMessage)) {
                e.preventDefault();
            }
        });
    });
}

/**
 * Add tooltips
 */
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        const tooltipText = element.getAttribute('data-tooltip');
        
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = tooltipText;
            document.body.appendChild(tooltip);
            
            const rect = element.getBoundingClientRect();
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';
            tooltip.style.left = (rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2)) + 'px';
            
            setTimeout(() => {
                tooltip.classList.add('show');
            }, 10);
        });
        
        element.addEventListener('mouseleave', function() {
            const tooltip = document.querySelector('.tooltip');
            if (tooltip) {
                tooltip.classList.remove('show');
                setTimeout(() => {
                    tooltip.remove();
                }, 200);
            }
        });
    });
}

// Add CSS for tooltips
const style = document.createElement('style');
style.textContent = `
    .tooltip {
        position: fixed;
        background-color: var(--color-black);
        color: var(--color-white);
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1000;
        opacity: 0;
        transform: translateY(10px);
        transition: opacity 0.2s, transform 0.2s;
        pointer-events: none;
        border: 1px solid var(--color-gray);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    }
    
    .tooltip.show {
        opacity: 1;
        transform: translateY(0);
    }
    
    .error-message {
        color: #dc3545;
        font-size: 0.85rem;
        margin-top: 5px;
    }
    
    .is-invalid {
        border-color: #dc3545 !important;
    }
`;
document.head.appendChild(style);
