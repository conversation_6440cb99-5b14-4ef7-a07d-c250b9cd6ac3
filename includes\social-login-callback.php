<?php
/**
 * Social Login Callback Handler
 */

// Include config and functions
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get provider from URL
$provider = isset($_GET['provider']) ? $_GET['provider'] : '';
$code = isset($_GET['code']) ? $_GET['code'] : '';
$state = isset($_GET['state']) ? $_GET['state'] : '';

// Verify state to prevent CSRF
if (empty($state) || !isset($_SESSION['oauth_state']) || $state !== $_SESSION['oauth_state']) {
    $_SESSION['error'] = 'Invalid state parameter. Please try again.';
    header('Location: ' . base_url('?page=login'));
    exit;
}

// Clear the state from session
unset($_SESSION['oauth_state']);

// Redirect URL
$redirect_url = base_url('includes/social-login-callback.php?provider=' . $provider);

// Handle different providers
switch ($provider) {
    case 'google':
        // Google OAuth configuration
        $client_id = '1095907255562-852kdts3okvrbpulkgkomv5cccd40aol.apps.googleusercontent.com'; // Google Client ID
        $client_secret = '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'; // Google Client Secret

        // Exchange code for token
        $token_url = 'https://oauth2.googleapis.com/token';
        $token_data = [
            'code' => $code,
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'redirect_uri' => $redirect_url,
            'grant_type' => 'authorization_code'
        ];

        $ch = curl_init($token_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($token_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/x-www-form-urlencoded']);

        $token_response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            $_SESSION['error'] = 'cURL Error: ' . curl_error($ch);
            header('Location: ' . base_url('?page=login'));
            exit;
        }

        $token_info = json_decode($token_response, true);

        // Log token response for debugging
        error_log('Google Token Response: ' . print_r($token_response, true));

        if (!isset($token_info['access_token'])) {
            $_SESSION['error'] = 'Failed to get access token from Google: ' . ($token_info['error'] ?? 'Unknown error');
            header('Location: ' . base_url('?page=login'));
            exit;
        }

        // Get user info
        $user_info_url = 'https://www.googleapis.com/oauth2/v3/userinfo';
        $ch = curl_init($user_info_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer ' . $token_info['access_token']]);

        $user_info_response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            $_SESSION['error'] = 'cURL Error: ' . curl_error($ch);
            header('Location: ' . base_url('?page=login'));
            exit;
        }

        $user_info = json_decode($user_info_response, true);

        // Log user info response for debugging
        error_log('Google User Info Response: ' . print_r($user_info_response, true));

        if (!isset($user_info['email'])) {
            $_SESSION['error'] = 'Failed to get user info from Google: ' . ($user_info['error'] ?? 'Email not provided');
            header('Location: ' . base_url('?page=login'));
            exit;
        }

        // Process user data
        $email = $user_info['email'];
        $full_name = $user_info['name'] ?? '';
        $given_name = $user_info['given_name'] ?? '';
        $family_name = $user_info['family_name'] ?? '';
        $picture = $user_info['picture'] ?? '';
        $google_id = $user_info['sub'] ?? '';

        // Check if user exists
        $sql = "SELECT * FROM users WHERE email = ? OR google_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $email, $google_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            // User exists, update Google ID if needed
            $user = $result->fetch_assoc();

            if (empty($user['google_id'])) {
                $update_sql = "UPDATE users SET google_id = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("si", $google_id, $user['id']);
                $result = $update_stmt->execute();

                // Log update result
                error_log('Google ID Update for existing user: ' . ($result ? 'Success' : 'Failed - ' . $update_stmt->error));
            }

            // Log successful login
            error_log('Google login successful for existing user: ' . $user['email']);

            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
        } else {
            // Create new user
            $username = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $given_name) . rand(100, 999));

            // Check if username exists
            $check_username_sql = "SELECT id FROM users WHERE username = ?";
            $check_username_stmt = $conn->prepare($check_username_sql);
            $check_username_stmt->bind_param("s", $username);
            $check_username_stmt->execute();
            $check_username_result = $check_username_stmt->get_result();

            // If username exists, add random numbers
            if ($check_username_result && $check_username_result->num_rows > 0) {
                $username = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $given_name) . rand(1000, 9999));
            }

            // Insert user
            $insert_sql = "INSERT INTO users (full_name, username, email, google_id, role, created_at) VALUES (?, ?, ?, ?, 'user', NOW())";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("ssss", $full_name, $username, $email, $google_id);

            $insert_result = $insert_stmt->execute();

            // Log insert result
            error_log('Google user creation: ' . ($insert_result ? 'Success' : 'Failed - ' . $insert_stmt->error));
            error_log('User data: Email=' . $email . ', Name=' . $full_name . ', Google ID=' . $google_id);

            if ($insert_result) {
                $user_id = $insert_stmt->insert_id;

                // Set session variables
                $_SESSION['user_id'] = $user_id;
                $_SESSION['user_name'] = $username;
                $_SESSION['full_name'] = $full_name;
                $_SESSION['email'] = $email;
                $_SESSION['role'] = 'user';

                // Log successful creation
                error_log('New Google user created with ID: ' . $user_id);
            } else {
                $_SESSION['error'] = 'Failed to create user account: ' . $insert_stmt->error;
                header('Location: ' . base_url('?page=login'));
                exit;
            }
        }

        // Redirect to home page
        header('Location: ' . base_url());
        exit;

    case 'facebook':
        // Facebook OAuth configuration
        $app_id = '****************'; // Facebook App ID
        $app_secret = '********************************'; // Facebook App Secret

        // Exchange code for token
        $token_url = 'https://graph.facebook.com/v12.0/oauth/access_token';
        $token_params = [
            'client_id' => $app_id,
            'client_secret' => $app_secret,
            'redirect_uri' => $redirect_url,
            'code' => $code
        ];

        // Use cURL instead of file_get_contents for better error handling
        $ch = curl_init();
        $token_url .= '?' . http_build_query($token_params);

        curl_setopt($ch, CURLOPT_URL, $token_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $token_response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            error_log('Facebook cURL Error: ' . curl_error($ch));
            $_SESSION['error'] = 'Connection error when contacting Facebook: ' . curl_error($ch);
            header('Location: ' . base_url('?page=login'));
            exit;
        }

        curl_close($ch);

        // Log token response for debugging
        error_log('Facebook Token Response: ' . print_r($token_response, true));

        $token_info = json_decode($token_response, true);

        if (!isset($token_info['access_token'])) {
            $error_msg = isset($token_info['error']) ? $token_info['error']['message'] : 'Unknown error';
            error_log('Facebook Token Error: ' . $error_msg);
            $_SESSION['error'] = 'Failed to get access token from Facebook: ' . $error_msg;
            header('Location: ' . base_url('?page=login'));
            exit;
        }

        // Get user info
        $user_info_url = 'https://graph.facebook.com/v12.0/me?fields=id,name,email,first_name,last_name,picture.type(large)';
        $user_info_url .= '&access_token=' . $token_info['access_token'];

        // Use cURL instead of file_get_contents for better error handling
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $user_info_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $user_info_response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            error_log('Facebook User Info cURL Error: ' . curl_error($ch));
            $_SESSION['error'] = 'Connection error when getting user info from Facebook: ' . curl_error($ch);
            header('Location: ' . base_url('?page=login'));
            exit;
        }

        curl_close($ch);

        // Log user info response for debugging
        error_log('Facebook User Info Response: ' . print_r($user_info_response, true));

        $user_info = json_decode($user_info_response, true);

        if (!isset($user_info['id'])) {
            $error_msg = isset($user_info['error']) ? $user_info['error']['message'] : 'User ID not provided';
            error_log('Facebook User Info Error: ' . $error_msg);
            $_SESSION['error'] = 'Failed to get user info from Facebook: ' . $error_msg;
            header('Location: ' . base_url('?page=login'));
            exit;
        }

        // Process user data
        $email = $user_info['email'] ?? '';
        $full_name = $user_info['name'] ?? '';
        $first_name = $user_info['first_name'] ?? '';
        $last_name = $user_info['last_name'] ?? '';
        $picture = $user_info['picture']['data']['url'] ?? '';
        $facebook_id = $user_info['id'] ?? '';

        // Log user data
        error_log("Facebook user data: ID=$facebook_id, Name=$full_name, Email=$email");

        // If email is not provided by Facebook
        if (empty($email)) {
            $email = "{$facebook_id}@facebook.com";
            error_log("Email not provided by Facebook, using: $email");
        }

        // Check if user exists
        $sql = "SELECT * FROM users WHERE email = ? OR facebook_id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ss", $email, $facebook_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            // User exists, update Facebook ID if needed
            $user = $result->fetch_assoc();
            error_log("Existing user found: ID={$user['id']}, Username={$user['username']}");

            if (empty($user['facebook_id'])) {
                $update_sql = "UPDATE users SET facebook_id = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("si", $facebook_id, $user['id']);
                $update_result = $update_stmt->execute();

                error_log("Updated Facebook ID for user {$user['id']}: " . ($update_result ? 'Success' : 'Failed - ' . $update_stmt->error));
            }

            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['username'];
            $_SESSION['full_name'] = $user['full_name'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['role'] = $user['role'] ?? 'user';

            error_log("Facebook login successful for existing user: {$user['email']}");
        } else {
            // Create new user
            error_log("No existing user found, creating new user");

            // Generate username from first name
            $username = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $first_name) . rand(100, 999));
            error_log("Generated username: $username");

            // Check if username exists
            $check_username_sql = "SELECT id FROM users WHERE username = ?";
            $check_username_stmt = $conn->prepare($check_username_sql);
            $check_username_stmt->bind_param("s", $username);
            $check_username_stmt->execute();
            $check_username_result = $check_username_stmt->get_result();

            // If username exists, add random numbers
            if ($check_username_result && $check_username_result->num_rows > 0) {
                $username = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $first_name) . rand(1000, 9999));
                error_log("Username already exists, new username: $username");
            }

            // Insert user
            $insert_sql = "INSERT INTO users (full_name, username, email, facebook_id, role, created_at) VALUES (?, ?, ?, ?, 'user', NOW())";
            $insert_stmt = $conn->prepare($insert_sql);
            $insert_stmt->bind_param("ssss", $full_name, $username, $email, $facebook_id);

            $insert_result = $insert_stmt->execute();

            // Log insert result
            error_log("Facebook user creation: " . ($insert_result ? 'Success' : 'Failed - ' . $insert_stmt->error));
            error_log("User data: Email=$email, Name=$full_name, Facebook ID=$facebook_id");

            if ($insert_result) {
                $user_id = $insert_stmt->insert_id;

                // Set session variables
                $_SESSION['user_id'] = $user_id;
                $_SESSION['user_name'] = $username;
                $_SESSION['full_name'] = $full_name;
                $_SESSION['email'] = $email;
                $_SESSION['role'] = 'user';

                // Log successful creation
                error_log("New Facebook user created with ID: $user_id");
            } else {
                $_SESSION['error'] = "Failed to create user account: {$insert_stmt->error}";
                header('Location: ' . base_url('?page=login'));
                exit;
            }
        }

        // Redirect to home page
        header('Location: ' . base_url());
        exit;

    default:
        // Invalid provider
        $_SESSION['error'] = 'Invalid social login provider';
        header('Location: ' . base_url('?page=login'));
        exit;
}
