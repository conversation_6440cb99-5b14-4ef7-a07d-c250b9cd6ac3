<?php
/**
 * Compare Nominees Page
 */

// Get nominees IDs from URL
$nominee1_id = isset($_GET['nominee1']) ? (int)$_GET['nominee1'] : 0;
$nominee2_id = isset($_GET['nominee2']) ? (int)$_GET['nominee2'] : 0;

// Get subcategory ID if available
$subcategory_id = isset($_GET['subcategory']) ? (int)$_GET['subcategory'] : 0;

// Get nominees data
$nominee1 = null;
$nominee2 = null;
$subcategory = null;
$other_nominees = [];

// Get subcategory if available
if ($subcategory_id > 0) {
    $subcategory = get_subcategory($subcategory_id);
}

// Debug information
$debug = [];
$debug[] = "Nominee1 ID: " . $nominee1_id;
$debug[] = "Nominee2 ID: " . $nominee2_id;
$debug[] = "Subcategory ID: " . $subcategory_id;

// Get nominees
if ($nominee1_id > 0) {
    $sql = "SELECT * FROM voting_nominees WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $nominee1_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $nominee1 = $result->fetch_assoc();
        $debug[] = "Nominee1 found: " . ($nominee1['name'] ?? 'Unknown');

        // Get subcategory if not already set
        if (!$subcategory && isset($nominee1['subcategory_id'])) {
            $subcategory = get_subcategory($nominee1['subcategory_id']);
            $subcategory_id = $nominee1['subcategory_id'];
            $debug[] = "Subcategory set from nominee1: " . $subcategory_id;
        }
    } else {
        $debug[] = "Nominee1 not found in database";
    }
}

if ($nominee2_id > 0) {
    $sql = "SELECT * FROM voting_nominees WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $nominee2_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $nominee2 = $result->fetch_assoc();
        $debug[] = "Nominee2 found: " . ($nominee2['name'] ?? 'Unknown');

        // Get subcategory if not already set
        if (!$subcategory && isset($nominee2['subcategory_id'])) {
            $subcategory = get_subcategory($nominee2['subcategory_id']);
            $subcategory_id = $nominee2['subcategory_id'];
            $debug[] = "Subcategory set from nominee2: " . $subcategory_id;
        }
    } else {
        $debug[] = "Nominee2 not found in database";
    }
}

// Get other nominees from the same subcategory for selection
if ($subcategory_id > 0) {
    $sql = "SELECT * FROM voting_nominees WHERE subcategory_id = ? ORDER BY votes_count DESC, name ASC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $subcategory_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $other_nominees[] = $row;
        }
        $debug[] = "Found " . count($other_nominees) . " nominees in subcategory " . $subcategory_id;
    } else {
        $debug[] = "No nominees found in subcategory " . $subcategory_id;
    }
} else {
    $debug[] = "No subcategory ID available to fetch nominees";
}

// Get main category if subcategory is available
$main_category = null;
if ($subcategory && isset($subcategory['category_id'])) {
    $main_category = get_main_category($subcategory['category_id']);
}

// Get user votes if logged in
$user_votes = [];
if (is_logged_in()) {
    $user_id = $_SESSION['user_id'];

    $sql = "SELECT nominee_id FROM voting_votes WHERE user_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $user_votes[] = $row['nominee_id'];
        }
    }
}

// Process vote form
$vote_message = '';
$vote_status = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['vote'])) {
    if (!is_logged_in()) {
        $vote_message = get_content('vote', 'login_required', 'Please login to vote');
        $vote_status = 'error';
    } else {
        $nominee_id = isset($_POST['nominee_id']) ? (int)$_POST['nominee_id'] : 0;

        if ($nominee_id > 0) {
            $user_id = $_SESSION['user_id'];

            // Check if user has already voted for this nominee
            if (has_voted_for_nominee($user_id, $nominee_id)) {
                $vote_message = get_content('vote', 'already_voted', 'You have already voted for this nominee');
                $vote_status = 'error';
            } else {
                // Add vote
                if (add_vote($user_id, $nominee_id)) {
                    $vote_message = get_content('vote', 'vote_success', 'Your vote has been recorded');
                    $vote_status = 'success';

                    // Add nominee ID to user votes
                    $user_votes[] = $nominee_id;

                    // Update nominees data
                    if ($nominee1 && $nominee1['id'] === $nominee_id) {
                        $nominee1['votes_count']++;
                    } elseif ($nominee2 && $nominee2['id'] === $nominee_id) {
                        $nominee2['votes_count']++;
                    }
                } else {
                    $vote_message = get_content('vote', 'vote_error', 'An error occurred while recording your vote');
                    $vote_status = 'error';
                }
            }
        }
    }
}

// Page title
$page_title = get_content('compare', 'title', 'Compare Nominees');

// Get nominee name based on language
function get_nominee_name_compare($nominee) {
    global $current_lang;

    // Check if name fields exist
    $name_ar = isset($nominee['name_ar']) ? $nominee['name_ar'] : '';
    $name_en = isset($nominee['name']) ? $nominee['name'] : '';

    // Return appropriate language or empty string if not available
    return $current_lang === 'ar' ? $name_ar : $name_en;
}

// Get nominee description based on language
function get_nominee_description($nominee) {
    global $current_lang;

    // Check if description fields exist
    $description_ar = isset($nominee['description_ar']) ? $nominee['description_ar'] : '';
    $description_en = isset($nominee['description']) ? $nominee['description'] : '';

    // Return appropriate language or empty string if not available
    return $current_lang === 'ar' ? $description_ar : $description_en;
}

// Calculate vote percentage
function calculate_vote_percentage($nominee1, $nominee2) {
    $total_votes = ($nominee1['votes_count'] ?? 0) + ($nominee2['votes_count'] ?? 0);

    if ($total_votes === 0) {
        return [50, 50]; // Equal if no votes
    }

    $nominee1_percentage = round(($nominee1['votes_count'] / $total_votes) * 100);
    $nominee2_percentage = 100 - $nominee1_percentage;

    return [$nominee1_percentage, $nominee2_percentage];
}

// Calculate percentages
$vote_percentages = [50, 50]; // Default equal
if ($nominee1 && $nominee2) {
    $vote_percentages = calculate_vote_percentage($nominee1, $nominee2);
}
?>

<!-- Compare Page -->
<section class="compare-section">
    <div class="container">
        <div class="compare-header">
            <h1 class="section-title"><?php echo $page_title; ?></h1>

            <?php if ($subcategory): ?>
            <div class="compare-breadcrumb">
                <?php if ($main_category): ?>
                <a href="<?php echo base_url('?page=vote&main_category=' . $main_category['id']); ?>"><?php echo get_main_category_name($main_category); ?></a>
                <span class="separator"><i class="fas fa-chevron-right"></i></span>
                <?php endif; ?>

                <a href="<?php echo base_url('?page=vote&main_category=' . ($main_category ? $main_category['id'] : '') . '&subcategory=' . $subcategory['id']); ?>"><?php echo get_subcategory_name($subcategory); ?></a>
            </div>
            <?php endif; ?>

            <?php if (!empty($vote_message)): ?>
            <div class="alert alert-<?php echo $vote_status === 'success' ? 'success' : 'danger'; ?>">
                <?php echo $vote_message; ?>
            </div>
            <?php endif; ?>
        </div>

        <div class="compare-selectors">
            <form action="<?php echo base_url('?page=compare'); ?>" method="get" class="compare-form">
                <input type="hidden" name="page" value="compare">

                <?php if ($subcategory_id): ?>
                <input type="hidden" name="subcategory" value="<?php echo $subcategory_id; ?>">
                <?php endif; ?>

                <div class="form-group">
                    <label for="nominee1"><?php echo get_content('compare', 'first_nominee', 'First Nominee'); ?></label>
                    <select name="nominee1" id="nominee1" class="form-control" required>
                        <option value=""><?php echo get_content('compare', 'select_nominee', 'Select Nominee'); ?></option>
                        <?php foreach ($other_nominees as $nominee): ?>
                        <option value="<?php echo $nominee['id']; ?>" <?php echo ($nominee1 && $nominee1['id'] === $nominee['id']) ? 'selected' : ''; ?>>
                            <?php echo get_nominee_name_compare($nominee); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="nominee2"><?php echo get_content('compare', 'second_nominee', 'Second Nominee'); ?></label>
                    <select name="nominee2" id="nominee2" class="form-control" required>
                        <option value=""><?php echo get_content('compare', 'select_nominee', 'Select Nominee'); ?></option>
                        <?php foreach ($other_nominees as $nominee): ?>
                        <option value="<?php echo $nominee['id']; ?>" <?php echo ($nominee2 && $nominee2['id'] === $nominee['id']) ? 'selected' : ''; ?>>
                            <?php echo get_nominee_name_compare($nominee); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-exchange-alt"></i>
                        <?php echo get_content('compare', 'compare_button', 'Compare'); ?>
                    </button>
                </div>
            </form>
        </div>

        <?php if ($nominee1 && $nominee2): ?>
        <div class="compare-content">
            <div class="compare-progress">
                <div class="progress-bar">
                    <div class="progress-fill nominee1" style="width: <?php echo $vote_percentages[0]; ?>%;">
                        <span class="progress-text"><?php echo $vote_percentages[0]; ?>%</span>
                    </div>
                    <div class="progress-fill nominee2" style="width: <?php echo $vote_percentages[1]; ?>%;">
                        <span class="progress-text"><?php echo $vote_percentages[1]; ?>%</span>
                    </div>
                </div>
                <div class="progress-labels">
                    <span class="nominee1-label"><?php echo get_nominee_name_compare($nominee1); ?></span>
                    <span class="nominee2-label"><?php echo get_nominee_name_compare($nominee2); ?></span>
                </div>
            </div>

            <div class="compare-cards">
                <div class="compare-card nominee1-card">
                    <div class="nominee-image">
                        <img src="<?php echo asset_url('uploads/' . (isset($nominee1['image']) ? $nominee1['image'] : 'default.jpg')); ?>" alt="<?php echo get_nominee_name_compare($nominee1); ?>">
                        <div class="nominee-votes">
                            <i class="fas fa-vote-yea"></i>
                            <span class="votes-count"><?php echo number_format($nominee1['votes_count'] ?? 0); ?></span>
                            <span class="votes-label"><?php echo get_content('vote', 'votes', 'votes'); ?></span>
                        </div>
                    </div>
                    <div class="nominee-info">
                        <h3><?php echo get_nominee_name_compare($nominee1); ?></h3>
                        <div class="nominee-description">
                            <?php echo get_nominee_description($nominee1); ?>
                        </div>

                        <?php if (is_logged_in()): ?>
                            <?php if (in_array($nominee1['id'], $user_votes)): ?>
                            <div class="voted-badge">
                                <i class="fas fa-check-circle"></i>
                                <?php echo get_content('vote', 'voted', 'You voted'); ?>
                            </div>
                            <?php else: ?>
                            <form action="" method="post" class="vote-form">
                                <input type="hidden" name="nominee_id" value="<?php echo $nominee1['id']; ?>">
                                <button type="submit" name="vote" class="btn-vote">
                                    <i class="fas fa-vote-yea"></i>
                                    <?php echo get_content('vote', 'vote_button', 'Vote Now'); ?>
                                </button>
                            </form>
                            <?php endif; ?>
                        <?php else: ?>
                            <a href="<?php echo base_url('?page=login&redirect=compare&nominee1=' . $nominee1['id'] . '&nominee2=' . $nominee2['id'] . '&subcategory=' . $subcategory_id); ?>" class="btn-login">
                                <i class="fas fa-sign-in-alt"></i>
                                <?php echo get_content('vote', 'login_to_vote', 'Login to Vote'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="compare-vs">
                    <span>VS</span>
                </div>

                <div class="compare-card nominee2-card">
                    <div class="nominee-image">
                        <img src="<?php echo asset_url('uploads/' . (isset($nominee2['image']) ? $nominee2['image'] : 'default.jpg')); ?>" alt="<?php echo get_nominee_name_compare($nominee2); ?>">
                        <div class="nominee-votes">
                            <i class="fas fa-vote-yea"></i>
                            <span class="votes-count"><?php echo number_format($nominee2['votes_count'] ?? 0); ?></span>
                            <span class="votes-label"><?php echo get_content('vote', 'votes', 'votes'); ?></span>
                        </div>
                    </div>
                    <div class="nominee-info">
                        <h3><?php echo get_nominee_name_compare($nominee2); ?></h3>
                        <div class="nominee-description">
                            <?php echo get_nominee_description($nominee2); ?>
                        </div>

                        <?php if (is_logged_in()): ?>
                            <?php if (in_array($nominee2['id'], $user_votes)): ?>
                            <div class="voted-badge">
                                <i class="fas fa-check-circle"></i>
                                <?php echo get_content('vote', 'voted', 'You voted'); ?>
                            </div>
                            <?php else: ?>
                            <form action="" method="post" class="vote-form">
                                <input type="hidden" name="nominee_id" value="<?php echo $nominee2['id']; ?>">
                                <button type="submit" name="vote" class="btn-vote">
                                    <i class="fas fa-vote-yea"></i>
                                    <?php echo get_content('vote', 'vote_button', 'Vote Now'); ?>
                                </button>
                            </form>
                            <?php endif; ?>
                        <?php else: ?>
                            <a href="<?php echo base_url('?page=login&redirect=compare&nominee1=' . $nominee1['id'] . '&nominee2=' . $nominee2['id'] . '&subcategory=' . $subcategory_id); ?>" class="btn-login">
                                <i class="fas fa-sign-in-alt"></i>
                                <?php echo get_content('vote', 'login_to_vote', 'Login to Vote'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php elseif ($other_nominees): ?>
        <div class="compare-placeholder">
            <div class="placeholder-icon">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <h2><?php echo get_content('compare', 'select_two', 'Select two nominees to compare'); ?></h2>
            <p><?php echo get_content('compare', 'select_instruction', 'Use the dropdown menus above to select two nominees for comparison'); ?></p>
        </div>
        <?php else: ?>
        <div class="compare-placeholder">
            <div class="placeholder-icon">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <h2><?php echo get_content('compare', 'no_nominees', 'No nominees available for comparison'); ?></h2>
            <p><?php echo get_content('compare', 'select_category', 'Please select a category and subcategory first'); ?></p>
            <a href="<?php echo base_url('?page=vote'); ?>" class="btn btn-primary">
                <i class="fas fa-list"></i>
                <?php echo get_content('compare', 'go_to_vote', 'Go to Voting Page'); ?>
            </a>

            <?php if (isset($_GET['debug']) && $_GET['debug'] === '1'): ?>
            <!-- Debug Information -->
            <div class="debug-info">
                <h3>Debug Information</h3>
                <ul>
                    <?php foreach ($debug as $message): ?>
                    <li><?php echo htmlspecialchars($message); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>
</section>
