<?php
/**
 * Visits Statistics Page for Admin Dashboard
 */

// Load visits tracking system if not already loaded
if (!function_exists('get_daily_visits')) {
    require_once '../includes/visits.php';
}

/**
 * Get flag emoji for a country code
 *
 * @param string $country_code The two-letter country code
 * @return string The flag emoji
 */
function getFlagEmoji($country_code) {
    // For special cases
    if ($country_code == 'LO') {
        return '🌐';
    }

    // Convert country code to flag emoji
    $country_code = strtoupper($country_code);
    $flag = '';

    // Each character is converted to a Regional Indicator Symbol Letter
    // which are Unicode characters in the range U+1F1E6 to U+1F1FF
    for ($i = 0; $i < strlen($country_code); $i++) {
        $char = ord($country_code[$i]) - ord('A') + ord('🇦');
        $flag .= mb_chr($char, 'UTF-8');
    }

    return $flag;
}

/**
 * Get icon for a device type
 *
 * @param string $device_type The device type
 * @return string The icon name
 */
function getDeviceIcon($device_type) {
    switch ($device_type) {
        case 'Desktop':
            return 'desktop';
        case 'Mobile':
            return 'mobile-alt';
        case 'Tablet':
            return 'tablet-alt';
        default:
            return 'question-circle';
    }
}

/**
 * Get icon for a browser
 *
 * @param string $browser The browser name
 * @return string The icon name
 */
function getBrowserIcon($browser) {
    switch ($browser) {
        case 'Chrome':
            return 'chrome';
        case 'Firefox':
            return 'firefox';
        case 'Safari':
            return 'safari';
        case 'Edge':
            return 'edge';
        case 'Internet Explorer':
            return 'internet-explorer';
        case 'Opera':
            return 'opera';
        default:
            return 'globe';
    }
}

/**
 * Get icon for an operating system
 *
 * @param string $os The operating system name
 * @return string The icon name
 */
function getOSIcon($os) {
    if (strpos($os, 'Windows') !== false) {
        return 'windows';
    } else if (strpos($os, 'Mac') !== false || strpos($os, 'iOS') !== false) {
        return 'apple';
    } else if (strpos($os, 'Android') !== false) {
        return 'android';
    } else if (strpos($os, 'Linux') !== false) {
        return 'linux';
    } else {
        return 'desktop';
    }
}

/**
 * Get icon for a referrer
 *
 * @param string $referrer The referrer domain
 * @return string The icon name
 */
function getReferrerIcon($referrer) {
    // Check for common social media and search engines
    $referrer = strtolower($referrer);

    if (strpos($referrer, 'facebook') !== false) {
        return 'facebook';
    } else if (strpos($referrer, 'twitter') !== false || strpos($referrer, 'x.com') !== false) {
        return 'twitter';
    } else if (strpos($referrer, 'instagram') !== false) {
        return 'instagram';
    } else if (strpos($referrer, 'linkedin') !== false) {
        return 'linkedin';
    } else if (strpos($referrer, 'youtube') !== false) {
        return 'youtube';
    } else if (strpos($referrer, 'google') !== false) {
        return 'google';
    } else if (strpos($referrer, 'bing') !== false) {
        return 'microsoft';
    } else if (strpos($referrer, 'yahoo') !== false) {
        return 'yahoo';
    } else if (strpos($referrer, 'baidu') !== false) {
        return 'baidu';
    } else if (strpos($referrer, 'yandex') !== false) {
        return 'yandex';
    } else if (strpos($referrer, 'duckduckgo') !== false) {
        return 'duck';
    } else {
        return 'external-link-alt';
    }
}

// Get statistics
$daily_visits = get_daily_visits(7);
$monthly_visits = get_monthly_visits(12);
$total_visits = get_total_visits();
$most_visited_pages = get_most_visited_pages(5);

// Get geographic statistics
$country_stats = get_country_stats(10);
$city_stats = get_city_stats(10);

// Get device statistics
$device_stats = get_device_stats();
$browser_stats = get_browser_stats();
$os_stats = get_os_stats();

// Get referrer statistics
$referrer_stats = get_referrer_stats(10);

// Prepare data for charts
$daily_labels = [];
$daily_total_data = [];
$daily_unique_data = [];

foreach ($daily_visits as $visit) {
    $daily_labels[] = $visit['formatted_date'];
    $daily_total_data[] = $visit['total_visits'];
    $daily_unique_data[] = $visit['unique_visits'];
}

$monthly_labels = [];
$monthly_total_data = [];
$monthly_unique_data = [];

foreach ($monthly_visits as $visit) {
    $monthly_labels[] = $visit['formatted_month'];
    $monthly_total_data[] = $visit['total_visits'];
    $monthly_unique_data[] = $visit['unique_visits'];
}

// Prepare data for geo charts
$country_labels = [];
$country_data = [];
$country_codes = [];

foreach ($country_stats as $country) {
    $country_labels[] = $country['country'];
    $country_data[] = $country['total_visits'];
    $country_codes[] = $country['country_code'];
}

// Prepare data for device charts
$device_labels = [];
$device_data = [];

foreach ($device_stats as $device) {
    $device_labels[] = $device['device_type'];
    $device_data[] = $device['total_visits'];
}

// Prepare data for browser charts
$browser_labels = [];
$browser_data = [];

foreach ($browser_stats as $browser) {
    $browser_labels[] = $browser['browser'];
    $browser_data[] = $browser['total_visits'];
}

// Prepare data for OS charts
$os_labels = [];
$os_data = [];

foreach ($os_stats as $os) {
    $os_labels[] = $os['os'];
    $os_data[] = $os['total_visits'];
}

// Prepare data for referrer charts
$referrer_labels = [];
$referrer_data = [];

foreach ($referrer_stats as $referrer) {
    $referrer_labels[] = $referrer['referrer'];
    $referrer_data[] = $referrer['total_visits'];
}

// Convert data to JSON for JavaScript
$daily_labels_json = json_encode($daily_labels);
$daily_total_json = json_encode($daily_total_data);
$daily_unique_json = json_encode($daily_unique_data);

$monthly_labels_json = json_encode($monthly_labels);
$monthly_total_json = json_encode($monthly_total_data);
$monthly_unique_json = json_encode($monthly_unique_data);

$country_labels_json = json_encode($country_labels);
$country_data_json = json_encode($country_data);
$country_codes_json = json_encode($country_codes);

$device_labels_json = json_encode($device_labels);
$device_data_json = json_encode($device_data);

$browser_labels_json = json_encode($browser_labels);
$browser_data_json = json_encode($browser_data);

$os_labels_json = json_encode($os_labels);
$os_data_json = json_encode($os_data);

$referrer_labels_json = json_encode($referrer_labels);
$referrer_data_json = json_encode($referrer_data);
?>

<div class="visits-statistics">
    <div class="dashboard-header">
        <h1 class="page-title">إحصائيات الزيارات</h1>
        <div class="dashboard-actions">
            <div class="date-display">
                <i class="fas fa-calendar-alt"></i>
                <span><?php echo date('l, d F Y'); ?></span>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="statistics-overview">
        <div class="row">
            <div class="col-md-4">
                <div class="stat-card total-visits">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-content">
                        <h3>إجمالي الزيارات</h3>
                        <div class="stat-value"><?php echo number_format($total_visits['total_visits']); ?></div>
                        <div class="stat-description">منذ إطلاق الموقع</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card unique-visits">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3>الزيارات الفريدة</h3>
                        <div class="stat-value"><?php echo number_format($total_visits['unique_visits']); ?></div>
                        <div class="stat-description">منذ إطلاق الموقع</div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card today-visits">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-content">
                        <h3>زيارات اليوم</h3>
                        <div class="stat-value"><?php echo number_format($daily_visits[0]['total_visits']); ?></div>
                        <div class="stat-description"><?php echo number_format($daily_visits[0]['unique_visits']); ?> زيارة فريدة</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="statistics-charts">
        <div class="row">
            <div class="col-md-8">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-chart-bar"></i> الزيارات اليومية</h3>
                        <div class="chart-actions">
                            <button class="btn btn-sm btn-outline chart-toggle active" data-chart="daily">7 أيام</button>
                            <button class="btn btn-sm btn-outline chart-toggle" data-chart="monthly">12 شهر</button>
                        </div>
                    </div>
                    <div class="chart-body">
                        <canvas id="visitsChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-file-alt"></i> الصفحات الأكثر زيارة</h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="pagesChart"></canvas>
                        <div class="pages-list">
                            <ul>
                                <?php foreach ($most_visited_pages as $page): ?>
                                <li>
                                    <span class="page-name"><?php echo htmlspecialchars($page['page']); ?></span>
                                    <span class="page-count"><?php echo number_format($page['visit_count']); ?> زيارة</span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Geographic Statistics -->
    <div class="statistics-section">
        <h2 class="section-title">
            <i class="fas fa-globe"></i>
            <span>الإحصائيات الجغرافية</span>
        </h2>

        <div class="row">
            <div class="col-md-8">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-map-marker-alt"></i> الزيارات حسب الدولة</h3>
                    </div>
                    <div class="chart-body">
                        <div id="worldMap" class="world-map"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-flag"></i> أعلى الدول زيارة</h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="countryChart"></canvas>
                        <div class="countries-list">
                            <ul>
                                <?php foreach ($country_stats as $country): ?>
                                <li>
                                    <span class="country-flag"><?php echo isset($country['country_code']) ? getFlagEmoji($country['country_code']) : '🌐'; ?></span>
                                    <span class="country-name"><?php echo htmlspecialchars($country['country']); ?></span>
                                    <span class="country-count"><?php echo number_format($country['total_visits']); ?> زيارة</span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="table-card">
                    <div class="table-header">
                        <h3><i class="fas fa-city"></i> أعلى المدن زيارة</h3>
                    </div>
                    <div class="table-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>المدينة</th>
                                        <th>المنطقة</th>
                                        <th>الدولة</th>
                                        <th>إجمالي الزيارات</th>
                                        <th>الزيارات الفريدة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($city_stats as $city): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($city['city']); ?></td>
                                        <td><?php echo htmlspecialchars($city['region']); ?></td>
                                        <td>
                                            <span class="country-flag"><?php echo isset($country['country_code']) ? getFlagEmoji($country['country_code']) : '🌐'; ?></span>
                                            <?php echo htmlspecialchars($city['country']); ?>
                                        </td>
                                        <td><?php echo number_format($city['total_visits']); ?></td>
                                        <td><?php echo number_format($city['unique_visits']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Device Statistics -->
    <div class="statistics-section">
        <h2 class="section-title">
            <i class="fas fa-mobile-alt"></i>
            <span>إحصائيات الأجهزة</span>
        </h2>

        <div class="row">
            <div class="col-md-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-laptop"></i> نوع الجهاز</h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="deviceChart"></canvas>
                        <div class="device-list">
                            <ul>
                                <?php foreach ($device_stats as $device): ?>
                                <li>
                                    <span class="device-icon">
                                        <i class="fas fa-<?php echo getDeviceIcon($device['device_type']); ?>"></i>
                                    </span>
                                    <span class="device-name"><?php echo htmlspecialchars($device['device_type']); ?></span>
                                    <span class="device-count"><?php echo number_format($device['total_visits']); ?> زيارة</span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-globe"></i> المتصفح</h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="browserChart"></canvas>
                        <div class="browser-list">
                            <ul>
                                <?php foreach ($browser_stats as $browser): ?>
                                <li>
                                    <span class="browser-icon">
                                        <i class="fab fa-<?php echo getBrowserIcon($browser['browser']); ?>"></i>
                                    </span>
                                    <span class="browser-name"><?php echo htmlspecialchars($browser['browser']); ?></span>
                                    <span class="browser-count"><?php echo number_format($browser['total_visits']); ?> زيارة</span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-desktop"></i> نظام التشغيل</h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="osChart"></canvas>
                        <div class="os-list">
                            <ul>
                                <?php foreach ($os_stats as $os): ?>
                                <li>
                                    <span class="os-icon">
                                        <i class="fab fa-<?php echo getOSIcon($os['os']); ?>"></i>
                                    </span>
                                    <span class="os-name"><?php echo htmlspecialchars($os['os']); ?></span>
                                    <span class="os-count"><?php echo number_format($os['total_visits']); ?> زيارة</span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Referrer Statistics -->
    <div class="statistics-section">
        <h2 class="section-title">
            <i class="fas fa-link"></i>
            <span>إحصائيات المصادر</span>
        </h2>

        <div class="row">
            <div class="col-md-8">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-external-link-alt"></i> مصادر الزيارات</h3>
                    </div>
                    <div class="chart-body">
                        <canvas id="referrerChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-card">
                    <div class="chart-header">
                        <h3><i class="fas fa-globe"></i> أعلى المصادر</h3>
                    </div>
                    <div class="chart-body">
                        <div class="referrer-list">
                            <ul>
                                <?php foreach ($referrer_stats as $referrer): ?>
                                <li>
                                    <span class="referrer-icon">
                                        <i class="fas fa-<?php echo getReferrerIcon($referrer['referrer']); ?>"></i>
                                    </span>
                                    <span class="referrer-name"><?php echo htmlspecialchars($referrer['referrer']); ?></span>
                                    <span class="referrer-count"><?php echo number_format($referrer['total_visits']); ?> زيارة</span>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Statistics -->
    <div class="statistics-tables">
        <div class="row">
            <div class="col-md-6">
                <div class="table-card">
                    <div class="table-header">
                        <h3><i class="fas fa-calendar-week"></i> إحصائيات الأسبوع</h3>
                    </div>
                    <div class="table-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>إجمالي الزيارات</th>
                                        <th>الزيارات الفريدة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($daily_visits as $visit): ?>
                                    <tr>
                                        <td><?php echo $visit['formatted_date']; ?></td>
                                        <td><?php echo number_format($visit['total_visits']); ?></td>
                                        <td><?php echo number_format($visit['unique_visits']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="table-card">
                    <div class="table-header">
                        <h3><i class="fas fa-calendar-alt"></i> إحصائيات الشهر</h3>
                    </div>
                    <div class="table-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الشهر</th>
                                        <th>إجمالي الزيارات</th>
                                        <th>الزيارات الفريدة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    // Show only the last 6 months for space
                                    $recent_months = array_slice($monthly_visits, 0, 6);
                                    foreach ($recent_months as $visit):
                                    ?>
                                    <tr>
                                        <td><?php echo $visit['formatted_month']; ?></td>
                                        <td><?php echo number_format($visit['total_visits']); ?></td>
                                        <td><?php echo number_format($visit['unique_visits']); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jsvectormap"></script>
<script src="https://cdn.jsdelivr.net/npm/jsvectormap/dist/maps/world.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Chart colors
    const colors = {
        gold: 'rgba(212, 175, 55, 1)',
        goldTransparent: 'rgba(212, 175, 55, 0.2)',
        blue: 'rgba(54, 162, 235, 1)',
        blueTransparent: 'rgba(54, 162, 235, 0.2)',
        red: 'rgba(255, 99, 132, 1)',
        redTransparent: 'rgba(255, 99, 132, 0.2)',
        green: 'rgba(75, 192, 192, 1)',
        greenTransparent: 'rgba(75, 192, 192, 0.2)',
        purple: 'rgba(153, 102, 255, 1)',
        purpleTransparent: 'rgba(153, 102, 255, 0.2)',
        orange: 'rgba(255, 159, 64, 1)',
        orangeTransparent: 'rgba(255, 159, 64, 0.2)',
        pageColors: [
            'rgba(212, 175, 55, 1)',
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(201, 203, 207, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 99, 132, 0.8)'
        ]
    };

    // Chart data
    const dailyLabels = <?php echo $daily_labels_json; ?>;
    const dailyTotalData = <?php echo $daily_total_json; ?>;
    const dailyUniqueData = <?php echo $daily_unique_json; ?>;

    const monthlyLabels = <?php echo $monthly_labels_json; ?>;
    const monthlyTotalData = <?php echo $monthly_total_json; ?>;
    const monthlyUniqueData = <?php echo $monthly_unique_json; ?>;

    const countryLabels = <?php echo $country_labels_json; ?>;
    const countryData = <?php echo $country_data_json; ?>;
    const countryCodes = <?php echo $country_codes_json; ?>;

    const deviceLabels = <?php echo $device_labels_json; ?>;
    const deviceData = <?php echo $device_data_json; ?>;

    const browserLabels = <?php echo $browser_labels_json; ?>;
    const browserData = <?php echo $browser_data_json; ?>;

    const osLabels = <?php echo $os_labels_json; ?>;
    const osData = <?php echo $os_data_json; ?>;

    const referrerLabels = <?php echo $referrer_labels_json; ?>;
    const referrerData = <?php echo $referrer_data_json; ?>;

    // Visits Chart
    const visitsCtx = document.getElementById('visitsChart').getContext('2d');
    const visitsChart = new Chart(visitsCtx, {
        type: 'bar',
        data: {
            labels: dailyLabels,
            datasets: [
                {
                    label: 'إجمالي الزيارات',
                    data: dailyTotalData,
                    backgroundColor: colors.goldTransparent,
                    borderColor: colors.gold,
                    borderWidth: 1
                },
                {
                    label: 'الزيارات الفريدة',
                    data: dailyUniqueData,
                    backgroundColor: colors.blueTransparent,
                    borderColor: colors.blue,
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Pages Chart
    const pagesCtx = document.getElementById('pagesChart').getContext('2d');
    const pagesChart = new Chart(pagesCtx, {
        type: 'doughnut',
        data: {
            labels: <?php echo json_encode(array_column($most_visited_pages, 'page')); ?>,
            datasets: [{
                data: <?php echo json_encode(array_column($most_visited_pages, 'visit_count')); ?>,
                backgroundColor: colors.pageColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Country Chart
    const countryCtx = document.getElementById('countryChart').getContext('2d');
    const countryChart = new Chart(countryCtx, {
        type: 'doughnut',
        data: {
            labels: countryLabels,
            datasets: [{
                data: countryData,
                backgroundColor: colors.pageColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Device Chart
    const deviceCtx = document.getElementById('deviceChart').getContext('2d');
    const deviceChart = new Chart(deviceCtx, {
        type: 'doughnut',
        data: {
            labels: deviceLabels,
            datasets: [{
                data: deviceData,
                backgroundColor: [colors.gold, colors.blue, colors.red],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Browser Chart
    const browserCtx = document.getElementById('browserChart').getContext('2d');
    const browserChart = new Chart(browserCtx, {
        type: 'doughnut',
        data: {
            labels: browserLabels,
            datasets: [{
                data: browserData,
                backgroundColor: colors.pageColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // OS Chart
    const osCtx = document.getElementById('osChart').getContext('2d');
    const osChart = new Chart(osCtx, {
        type: 'doughnut',
        data: {
            labels: osLabels,
            datasets: [{
                data: osData,
                backgroundColor: colors.pageColors,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Referrer Chart
    const referrerCtx = document.getElementById('referrerChart').getContext('2d');
    const referrerChart = new Chart(referrerCtx, {
        type: 'bar',
        data: {
            labels: referrerLabels,
            datasets: [{
                label: 'الزيارات',
                data: referrerData,
                backgroundColor: colors.goldTransparent,
                borderColor: colors.gold,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // World Map
    try {
        // Prepare data for the map
        const mapData = {};
        countryCodes.forEach((code, index) => {
            if (code && code !== 'LO') {
                mapData[code.toLowerCase()] = countryData[index];
            }
        });

        // Initialize the map
        const map = new jsVectorMap({
            selector: '#worldMap',
            map: 'world',
            zoomButtons: true,
            zoomOnScroll: true,
            markersSelectable: true,
            markers: [],
            markerStyle: {
                initial: {
                    r: 7,
                    fill: colors.gold,
                    stroke: '#fff',
                    'stroke-width': 2,
                    'stroke-opacity': 0.5
                },
                hover: {
                    fill: colors.blue,
                    stroke: '#fff',
                    'stroke-width': 2,
                    'stroke-opacity': 0.5
                },
                selected: {
                    fill: colors.red
                }
            },
            markerLabelStyle: {
                initial: {
                    fontFamily: 'Poppins',
                    fontSize: 13,
                    fontWeight: 500,
                    fill: '#35373e'
                }
            },
            labels: {
                markers: {
                    render: marker => marker.name
                }
            },
            series: {
                regions: [{
                    values: mapData,
                    scale: ['#C8EEFF', '#0071A4'],
                    normalizeFunction: 'polynomial'
                }]
            },
            regionStyle: {
                initial: {
                    fill: '#e9e9e9',
                    'fill-opacity': 1,
                    stroke: 'none',
                    'stroke-width': 0,
                    'stroke-opacity': 1
                },
                hover: {
                    'fill-opacity': 0.8,
                    cursor: 'pointer'
                },
                selected: {
                    fill: '#d9d9d9'
                }
            },
            regionsSelectable: true,
            onRegionTipShow: function(event, label, code) {
                if (mapData[code]) {
                    label.html(
                        '<div class="map-tooltip">' +
                        '<div class="map-tooltip-title">' + label.html() + '</div>' +
                        '<div class="map-tooltip-content">' + mapData[code] + ' زيارة</div>' +
                        '</div>'
                    );
                }
            }
        });
    } catch (e) {
        console.error('Error initializing map:', e);
        document.getElementById('worldMap').innerHTML = '<div class="map-error">حدث خطأ أثناء تحميل الخريطة</div>';
    }

    // Toggle between daily and monthly charts
    const chartToggles = document.querySelectorAll('.chart-toggle');
    chartToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            // Update active state
            chartToggles.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Update chart data
            const chartType = this.getAttribute('data-chart');
            if (chartType === 'daily') {
                visitsChart.data.labels = dailyLabels;
                visitsChart.data.datasets[0].data = dailyTotalData;
                visitsChart.data.datasets[1].data = dailyUniqueData;
            } else {
                visitsChart.data.labels = monthlyLabels;
                visitsChart.data.datasets[0].data = monthlyTotalData;
                visitsChart.data.datasets[1].data = monthlyUniqueData;
            }
            visitsChart.update();
        });
    });
});
</script>

<style>
.visits-statistics {
    padding: 20px;
}

.statistics-overview {
    margin-bottom: 30px;
}

.statistics-section {
    margin-bottom: 40px;
}

.section-title {
    color: var(--color-gold);
    font-size: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 10px;
    font-size: 24px;
}

.stat-card {
    background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-gray);
    box-shadow: var(--shadow-md);
    padding: 20px;
    display: flex;
    align-items: center;
    transition: all var(--transition-normal);
    height: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    background-color: rgba(212, 175, 55, 0.1);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.stat-icon i {
    color: var(--color-gold);
    font-size: 24px;
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    color: var(--color-light);
    font-size: 14px;
    margin-bottom: 5px;
}

.stat-value {
    color: var(--color-gold);
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-description {
    color: var(--color-light-gray);
    font-size: 12px;
}

.chart-card, .table-card {
    background: linear-gradient(135deg, var(--color-black) 0%, #1a1a1a 100%);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-gray);
    box-shadow: var(--shadow-md);
    margin-bottom: 30px;
    overflow: hidden;
    height: 100%;
}

.chart-header, .table-header {
    background-color: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid var(--color-gray);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-header h3, .table-header h3 {
    color: var(--color-gold);
    font-size: 16px;
    margin: 0;
    display: flex;
    align-items: center;
}

.chart-header h3 i, .table-header h3 i {
    margin-right: 10px;
}

.chart-body, .table-body {
    padding: 20px;
}

.chart-body {
    height: 300px;
    position: relative;
}

.chart-toggle {
    background-color: transparent;
    border: 1px solid var(--color-gray);
    color: var(--color-light);
    padding: 5px 10px;
    font-size: 12px;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.chart-toggle.active, .chart-toggle:hover {
    background-color: var(--color-gold);
    border-color: var(--color-gold);
    color: var(--color-black);
}

.pages-list, .countries-list, .device-list, .browser-list, .os-list, .referrer-list {
    margin-top: 20px;
    max-height: 200px;
    overflow-y: auto;
}

.pages-list ul, .countries-list ul, .device-list ul, .browser-list ul, .os-list ul, .referrer-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.pages-list li, .countries-list li, .device-list li, .browser-list li, .os-list li, .referrer-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.pages-list li:last-child, .countries-list li:last-child, .device-list li:last-child, .browser-list li:last-child, .os-list li:last-child, .referrer-list li:last-child {
    border-bottom: none;
}

.page-name, .country-name, .device-name, .browser-name, .os-name, .referrer-name {
    color: var(--color-light);
    font-size: 14px;
    flex: 1;
}

.page-count, .country-count, .device-count, .browser-count, .os-count, .referrer-count {
    color: var(--color-gold);
    font-size: 14px;
    font-weight: bold;
    margin-left: 10px;
}

.country-flag {
    margin-right: 10px;
    font-size: 18px;
}

.device-icon, .browser-icon, .os-icon, .referrer-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(212, 175, 55, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
}

.device-icon i, .browser-icon i, .os-icon i, .referrer-icon i {
    color: var(--color-gold);
    font-size: 14px;
}

.table {
    width: 100%;
    color: var(--color-light);
}

.table th {
    color: var(--color-gold);
    font-weight: bold;
    border-bottom: 1px solid rgba(212, 175, 55, 0.3);
    padding: 10px;
}

.table td {
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table tr:last-child td {
    border-bottom: none;
}

.world-map {
    height: 300px;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-sm);
}

.map-error {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-light-gray);
    font-style: italic;
}

.map-tooltip {
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: var(--border-radius-sm);
    padding: 10px;
    color: var(--color-light);
    font-size: 12px;
    max-width: 200px;
}

.map-tooltip-title {
    color: var(--color-gold);
    font-weight: bold;
    margin-bottom: 5px;
}

.map-tooltip-content {
    color: var(--color-light);
}

/* RTL Support */
html[dir="rtl"] .stat-icon {
    margin-right: 0;
    margin-left: 20px;
}

html[dir="rtl"] .chart-header h3 i,
html[dir="rtl"] .table-header h3 i,
html[dir="rtl"] .section-title i {
    margin-right: 0;
    margin-left: 10px;
}

html[dir="rtl"] .country-flag,
html[dir="rtl"] .device-icon,
html[dir="rtl"] .browser-icon,
html[dir="rtl"] .os-icon,
html[dir="rtl"] .referrer-icon {
    margin-right: 0;
    margin-left: 10px;
}

html[dir="rtl"] .page-count,
html[dir="rtl"] .country-count,
html[dir="rtl"] .device-count,
html[dir="rtl"] .browser-count,
html[dir="rtl"] .os-count,
html[dir="rtl"] .referrer-count {
    margin-left: 0;
    margin-right: 10px;
}
</style>
