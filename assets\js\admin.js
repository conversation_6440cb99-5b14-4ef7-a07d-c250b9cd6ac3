/**
 * Admin JavaScript for Social Media Festival
 * Enhanced version with improved animations and interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add page transition effect
    document.body.classList.add('page-loaded');

    // Sidebar toggle with animation
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const adminWrapper = document.querySelector('.admin-wrapper');
    const adminSidebar = document.querySelector('.admin-sidebar');

    if (sidebarToggle && adminWrapper && adminSidebar) {
        // Check if sidebar should be collapsed on mobile
        if (window.innerWidth < 992 && !adminSidebar.classList.contains('expanded')) {
            adminWrapper.classList.add('collapsed');
        }

        sidebarToggle.addEventListener('click', function() {
            adminSidebar.classList.toggle('expanded');
            adminWrapper.classList.toggle('collapsed');

            // Add rotation animation to toggle icon
            this.querySelector('i').classList.add('fa-spin');
            setTimeout(() => {
                this.querySelector('i').classList.remove('fa-spin');
            }, 300);
        });
    }

    // Handle sidebar dropdown menus
    const sidebarDropdownToggles = document.querySelectorAll('.sidebar-nav .dropdown-toggle');

    sidebarDropdownToggles.forEach(toggle => {
        // Check if this dropdown should be active based on current page
        const dropdownMenu = toggle.nextElementSibling;
        const activeItem = dropdownMenu ? dropdownMenu.querySelector('.active') : null;

        // If there's an active item in the dropdown, expand it
        if (activeItem) {
            toggle.classList.add('active');
            dropdownMenu.style.display = 'block';
            dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
        }

        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent event bubbling

            // Toggle active class
            this.classList.toggle('active');

            // Toggle dropdown menu
            const dropdownMenu = this.nextElementSibling;

            if (dropdownMenu) {
                if (dropdownMenu.style.display === 'block') {
                    // Close dropdown with animation
                    dropdownMenu.style.maxHeight = '0';
                    setTimeout(() => {
                        dropdownMenu.style.display = 'none';
                    }, 300);
                } else {
                    // Open dropdown with animation
                    dropdownMenu.style.display = 'block';
                    dropdownMenu.style.maxHeight = '0';
                    setTimeout(() => {
                        dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
                    }, 10);
                }
            }
        });
    });

    // Special handling for content management dropdown
    // First try to find active dropdown toggle
    let contentManagementToggle = document.querySelector('.sidebar-nav li.active a.dropdown-toggle');

    // If not found, try to find by content
    if (!contentManagementToggle) {
        const allToggles = document.querySelectorAll('.sidebar-nav a.dropdown-toggle');
        for (const toggle of allToggles) {
            const spanElement = toggle.querySelector('span');
            if (spanElement && spanElement.textContent.includes('إدارة المحتوى')) {
                contentManagementToggle = toggle;
                break;
            }
        }
    }

    if (contentManagementToggle) {
        // Get the dropdown menu
        const dropdownMenu = contentManagementToggle.nextElementSibling;

        // Force open the dropdown menu
        if (dropdownMenu) {
            contentManagementToggle.classList.add('active');
            dropdownMenu.style.display = 'block';
            dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
        }
    }

    // Direct fix for content management dropdown
    const contentLinks = document.querySelectorAll('.sidebar-nav a');

    contentLinks.forEach(link => {
        const spanElement = link.querySelector('span');
        if (spanElement && spanElement.textContent.includes('إدارة المحتوى')) {
            // This is the content management link
            link.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Get the dropdown menu
                const dropdownMenu = this.nextElementSibling;

                if (dropdownMenu) {
                    // Toggle dropdown visibility
                    if (window.getComputedStyle(dropdownMenu).display === 'block') {
                        this.classList.remove('active');
                        dropdownMenu.style.maxHeight = '0';
                        setTimeout(() => {
                            dropdownMenu.style.display = 'none';
                        }, 300);
                    } else {
                        this.classList.add('active');
                        dropdownMenu.style.display = 'block';
                        setTimeout(() => {
                            dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
                        }, 10);
                    }
                }
            });

            // Check if we're on a content management page
            const currentPath = window.location.pathname + window.location.search;
            if (currentPath.includes('page=content') || currentPath.includes('page=header_content')) {
                // Manually open the dropdown
                const dropdownMenu = link.nextElementSibling;
                if (dropdownMenu) {
                    link.classList.add('active');
                    dropdownMenu.style.display = 'block';
                    dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
                }
            }
        }
    });

    // Content tabs with smooth transitions
    const contentTabs = document.querySelectorAll('.content-tab');
    const contentPanels = document.querySelectorAll('.content-panel');

    if (contentTabs.length > 0 && contentPanels.length > 0) {
        contentTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                if (this.classList.contains('active')) return;

                // Remove active class from all tabs and panels
                contentTabs.forEach(t => t.classList.remove('active'));

                // First hide all panels with fade out
                contentPanels.forEach(p => {
                    if (p.classList.contains('active')) {
                        p.style.opacity = '0';
                        setTimeout(() => {
                            p.classList.remove('active');
                        }, 200);
                    } else {
                        p.classList.remove('active');
                    }
                });

                // Add active class to clicked tab
                const tabId = this.getAttribute('data-tab');
                this.classList.add('active');

                // Show the corresponding panel with a slight delay for animation
                setTimeout(() => {
                    const activePanel = document.getElementById(tabId);
                    activePanel.classList.add('active');

                    // Trigger reflow
                    void activePanel.offsetWidth;

                    // Fade in
                    activePanel.style.opacity = '1';
                }, 250);
            });
        });

        // Set initial opacity for active panel
        const activePanel = document.querySelector('.content-panel.active');
        if (activePanel) {
            activePanel.style.opacity = '1';
        }

        // Set transition for all panels
        contentPanels.forEach(panel => {
            panel.style.transition = 'opacity 0.3s ease-in-out';
            panel.style.opacity = '0';
        });
    }

    // File input preview
    const fileInputs = document.querySelectorAll('input[type="file"]');

    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const previewContainer = this.parentElement.querySelector('.file-preview');

            if (previewContainer) {
                previewContainer.innerHTML = '';

                if (this.files && this.files[0]) {
                    const file = this.files[0];

                    if (file.type.startsWith('image/')) {
                        // Image preview
                        const img = document.createElement('img');
                        img.src = URL.createObjectURL(file);
                        img.alt = file.name;
                        previewContainer.appendChild(img);
                    } else if (file.type.startsWith('video/')) {
                        // Video preview
                        const video = document.createElement('video');
                        video.src = URL.createObjectURL(file);
                        video.controls = true;
                        video.muted = true;
                        previewContainer.appendChild(video);
                    } else {
                        // File name
                        const fileInfo = document.createElement('div');
                        fileInfo.className = 'file-info';
                        fileInfo.textContent = file.name;
                        previewContainer.appendChild(fileInfo);
                    }
                }
            }
        });
    });

    // Enhanced modal confirm delete
    const deleteButtons = document.querySelectorAll('.btn-delete');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Create modal dynamically
            const modal = document.createElement('div');
            modal.className = 'modal';
            modal.style.display = 'block';

            const modalContent = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>تأكيد الحذف</h2>
                        <button class="modal-close">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>هل أنت متأكد من أنك تريد حذف هذا العنصر؟</p>
                        <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-danger confirm-delete">حذف</button>
                        <button class="btn btn-secondary cancel-delete">إلغاء</button>
                    </div>
                </div>
            `;

            modal.innerHTML = modalContent;
            document.body.appendChild(modal);

            // Handle close button
            const closeBtn = modal.querySelector('.modal-close');
            closeBtn.addEventListener('click', function() {
                document.body.removeChild(modal);
            });

            // Handle cancel button
            const cancelBtn = modal.querySelector('.cancel-delete');
            cancelBtn.addEventListener('click', function() {
                document.body.removeChild(modal);
            });

            // Handle confirm button
            const confirmBtn = modal.querySelector('.confirm-delete');
            confirmBtn.addEventListener('click', function() {
                document.body.removeChild(modal);

                // Get the original link and navigate to it
                const link = button.getAttribute('href');
                if (link) {
                    window.location.href = link;
                } else if (button.closest('form')) {
                    button.closest('form').submit();
                }
            });

            // Close when clicking outside
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    document.body.removeChild(modal);
                }
            });
        });
    });

    // Form validation
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredInputs = form.querySelectorAll('[required]');
            let isValid = true;

            requiredInputs.forEach(input => {
                if (!input.value.trim()) {
                    isValid = false;
                    input.classList.add('is-invalid');

                    // Add error message if it doesn't exist
                    const errorMessage = input.parentElement.querySelector('.error-message');
                    if (!errorMessage) {
                        const error = document.createElement('div');
                        error.className = 'error-message';
                        error.textContent = 'This field is required';
                        input.parentElement.appendChild(error);
                    }
                } else {
                    input.classList.remove('is-invalid');

                    // Remove error message if it exists
                    const errorMessage = input.parentElement.querySelector('.error-message');
                    if (errorMessage) {
                        errorMessage.remove();
                    }
                }
            });

            if (!isValid) {
                e.preventDefault();
            }
        });
    });

    // AJAX form submission
    const ajaxForms = document.querySelectorAll('form[data-ajax="true"]');

    ajaxForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            const submitButton = form.querySelector('[type="submit"]');
            const resultContainer = document.querySelector(form.getAttribute('data-result'));

            if (submitButton) {
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            }

            fetch(form.action, {
                method: form.method,
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (resultContainer) {
                    resultContainer.innerHTML = '';

                    const alert = document.createElement('div');
                    alert.className = data.success ? 'alert alert-success' : 'alert alert-danger';
                    alert.innerHTML = data.message;

                    resultContainer.appendChild(alert);

                    // Scroll to result
                    resultContainer.scrollIntoView({ behavior: 'smooth' });
                }

                if (data.success && data.redirect) {
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                }

                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Submit';
                }

                if (data.success && form.getAttribute('data-reset') === 'true') {
                    form.reset();
                }
            })
            .catch(error => {
                console.error('Error:', error);

                if (resultContainer) {
                    resultContainer.innerHTML = '';

                    const alert = document.createElement('div');
                    alert.className = 'alert alert-danger';
                    alert.innerHTML = 'An error occurred. Please try again.';

                    resultContainer.appendChild(alert);
                }

                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Submit';
                }
            });
        });
    });

    // Initialize rich text editors
    const richTextAreas = document.querySelectorAll('.rich-text');

    if (richTextAreas.length > 0 && typeof ClassicEditor !== 'undefined') {
        richTextAreas.forEach(textarea => {
            ClassicEditor
                .create(textarea)
                .catch(error => {
                    console.error(error);
                });
        });
    }
});
