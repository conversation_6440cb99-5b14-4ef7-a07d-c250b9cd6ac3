<?php
/**
 * Voting Nominees Management Page
 */

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Get subcategory ID from URL
$subcategory_id = isset($_GET['subcategory_id']) ? (int)$_GET['subcategory_id'] : 0;

// Get subcategory details
$subcategory = $subcategory_id > 0 ? get_subcategory($subcategory_id) : null;

// Get category details if subcategory is selected
$category = $subcategory ? get_main_category($subcategory['category_id']) : null;

// Process form submission for adding/editing nominee
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save') {
    $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    $name = $_POST['name'] ?? '';
    $name_ar = $_POST['name_ar'] ?? '';
    $bio = $_POST['bio'] ?? '';
    $bio_ar = $_POST['bio_ar'] ?? '';
    $nominee_subcategory_id = (int)($_POST['subcategory_id'] ?? 0);
    $facebook = $_POST['facebook'] ?? '';
    $twitter = $_POST['twitter'] ?? '';
    $instagram = $_POST['instagram'] ?? '';

    // Handle image upload
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/uploads/';
        $temp_name = $_FILES['image']['tmp_name'];
        $original_name = $_FILES['image']['name'];
        $extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $new_name = 'voting_nominee_' . time() . '.' . $extension;

        if (move_uploaded_file($temp_name, $upload_dir . $new_name)) {
            $image = $new_name;
        }
    }

    // Check if table exists
    $check_table = $conn->query("SHOW TABLES LIKE 'voting_nominees'");
    if ($check_table->num_rows == 0) {
        // Create table if it doesn't exist
        $create_table_sql = file_get_contents('../sql/voting_hierarchy.sql');
        $conn->multi_query($create_table_sql);

        // Clear results
        while ($conn->more_results() && $conn->next_result()) {
            if ($result = $conn->store_result()) {
                $result->free();
            }
        }
    }

    if ($id > 0) {
        // Update existing nominee
        $update_sql = "UPDATE voting_nominees SET
                      name = ?,
                      name_ar = ?,
                      bio = ?,
                      bio_ar = ?,
                      subcategory_id = ?,
                      facebook = ?,
                      twitter = ?,
                      instagram = ?";

        // Only update image if a new one was uploaded
        if (!empty($image)) {
            $update_sql .= ", image = ?";
        }

        $update_sql .= " WHERE id = ?";

        $stmt = $conn->prepare($update_sql);

        if (!empty($image)) {
            $stmt->bind_param("ssssisssi", $name, $name_ar, $bio, $bio_ar, $nominee_subcategory_id, $facebook, $twitter, $instagram, $image, $id);
        } else {
            $stmt->bind_param("ssssisssi", $name, $name_ar, $bio, $bio_ar, $nominee_subcategory_id, $facebook, $twitter, $instagram, $id);
        }

        if ($stmt->execute()) {
            $success_message = "تم تحديث المرشح بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء تحديث البيانات: " . $stmt->error;
        }

        $stmt->close();
    } else {
        // Insert new nominee
        $insert_sql = "INSERT INTO voting_nominees (name, name_ar, bio, bio_ar, subcategory_id, facebook, twitter, instagram, image)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("ssssissss", $name, $name_ar, $bio, $bio_ar, $nominee_subcategory_id, $facebook, $twitter, $instagram, $image);

        if ($stmt->execute()) {
            $success_message = "تم إضافة المرشح بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء إضافة البيانات: " . $stmt->error;
        }

        $stmt->close();
    }

    // Update subcategory_id in URL if it changed
    if ($id == 0 && $nominee_subcategory_id != $subcategory_id) {
        $subcategory_id = $nominee_subcategory_id;
        $subcategory = get_subcategory($subcategory_id);
        if ($subcategory) {
            $category = get_main_category($subcategory['category_id']);
        }
    }
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $id = (int)$_POST['id'];

    // Get the image filename before deleting
    $image_sql = "SELECT image FROM voting_nominees WHERE id = ?";
    $stmt = $conn->prepare($image_sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($image_filename);
    $stmt->fetch();
    $stmt->close();

    // Delete the nominee
    $delete_sql = "DELETE FROM voting_nominees WHERE id = ?";
    $stmt = $conn->prepare($delete_sql);
    $stmt->bind_param("i", $id);

    if ($stmt->execute()) {
        // Delete the image file if it exists
        if (!empty($image_filename)) {
            $image_path = '../assets/uploads/' . $image_filename;
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }

        $success_message = "تم حذف المرشح بنجاح";
    } else {
        $error_message = "حدث خطأ أثناء حذف البيانات: " . $stmt->error;
    }

    $stmt->close();
}

// Get all categories for dropdown
$all_categories = get_main_categories();

// Get all subcategories for the selected category
$all_subcategories = [];
if ($category) {
    $all_subcategories = get_subcategories($category['id']);
} else {
    // Get all subcategories if no category is selected
    foreach ($all_categories as $cat) {
        $cat_subcategories = get_subcategories($cat['id']);
        $all_subcategories = array_merge($all_subcategories, $cat_subcategories);
    }
}

// Get nominees for the selected subcategory
$nominees = $subcategory_id > 0 ? get_nominees_by_subcategory($subcategory_id) : [];

// Get nominee for editing
$edit_nominee = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];

    // Get nominee details
    $edit_nominee_sql = "SELECT * FROM voting_nominees WHERE id = ?";
    $stmt = $conn->prepare($edit_nominee_sql);
    $stmt->bind_param("i", $edit_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $edit_nominee = $result->fetch_assoc();

        // Update subcategory_id if nominee belongs to a different subcategory
        if ($edit_nominee && $edit_nominee['subcategory_id'] != $subcategory_id) {
            $subcategory_id = $edit_nominee['subcategory_id'];
            $subcategory = get_subcategory($subcategory_id);
            if ($subcategory) {
                $category = get_main_category($subcategory['category_id']);
                $all_subcategories = get_subcategories($category['id']);
                $nominees = get_nominees_by_subcategory($subcategory_id);
            }
        }
    }

    $stmt->close();
}
?>

<div class="page-header">
    <h1><i class="fas fa-user-friends"></i> إدارة مرشحي التصويت</h1>
    <?php if ($subcategory && $category): ?>
    <p>إدارة المرشحين للفئة: <strong><?php echo htmlspecialchars(get_main_category_name($category)); ?> / <?php echo htmlspecialchars(get_subcategory_name($subcategory)); ?></strong></p>
    <?php elseif ($subcategory): ?>
    <p>إدارة المرشحين للفئة الفرعية: <strong><?php echo htmlspecialchars(get_subcategory_name($subcategory)); ?></strong></p>
    <?php else: ?>
    <p>إدارة المرشحين لنظام التصويت</p>
    <?php endif; ?>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success">
    <?php echo $success_message; ?>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger">
    <?php echo $error_message; ?>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h2><?php echo $edit_nominee ? 'تعديل المرشح' : 'إضافة مرشح جديد'; ?></h2>
            </div>
            <div class="card-body">
                <form action="" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="save">
                    <?php if ($edit_nominee): ?>
                    <input type="hidden" name="id" value="<?php echo $edit_nominee['id']; ?>">
                    <?php endif; ?>

                    <div class="form-group">
                        <label for="subcategory_id">الفئة الفرعية</label>
                        <select id="subcategory_id" name="subcategory_id" class="form-control" required>
                            <option value="">-- اختر الفئة الفرعية --</option>
                            <?php foreach ($all_subcategories as $sub): ?>
                            <option value="<?php echo $sub['id']; ?>" <?php echo ($edit_nominee && $edit_nominee['subcategory_id'] == $sub['id']) || (!$edit_nominee && $subcategory_id == $sub['id']) ? 'selected' : ''; ?>>
                                <?php
                                $sub_category = get_main_category($sub['category_id']);
                                echo htmlspecialchars(get_main_category_name($sub_category) . ' / ' . get_subcategory_name($sub));
                                ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="name">الاسم (الإنجليزية)</label>
                        <input type="text" id="name" name="name" class="form-control" value="<?php echo $edit_nominee ? htmlspecialchars($edit_nominee['name']) : ''; ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="name_ar">الاسم (العربية)</label>
                        <input type="text" id="name_ar" name="name_ar" class="form-control" value="<?php echo $edit_nominee ? htmlspecialchars($edit_nominee['name_ar']) : ''; ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="bio">نبذة تعريفية (الإنجليزية)</label>
                        <textarea id="bio" name="bio" class="form-control" rows="3"><?php echo $edit_nominee ? htmlspecialchars($edit_nominee['bio']) : ''; ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="bio_ar">نبذة تعريفية (العربية)</label>
                        <textarea id="bio_ar" name="bio_ar" class="form-control" rows="3"><?php echo $edit_nominee ? htmlspecialchars($edit_nominee['bio_ar']) : ''; ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="image">الصورة</label>
                        <?php if ($edit_nominee && !empty($edit_nominee['image'])): ?>
                        <div class="current-image">
                            <img src="<?php echo asset_url('uploads/' . $edit_nominee['image']); ?>" alt="<?php echo htmlspecialchars($edit_nominee['name']); ?>" style="max-width: 100px; margin-bottom: 10px;">
                            <p>الصورة الحالية</p>
                        </div>
                        <?php endif; ?>
                        <input type="file" id="image" name="image" class="form-control-file" <?php echo !$edit_nominee ? 'required' : ''; ?>>
                        <small class="form-text text-muted">يفضل صورة بأبعاد 400×600 بكسل</small>
                    </div>

                    <h3 class="mt-4">روابط التواصل الاجتماعي</h3>

                    <div class="form-group">
                        <label for="facebook"><i class="fab fa-facebook"></i> فيسبوك</label>
                        <input type="url" id="facebook" name="facebook" class="form-control" value="<?php echo $edit_nominee ? htmlspecialchars($edit_nominee['facebook']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="twitter"><i class="fab fa-twitter"></i> تويتر</label>
                        <input type="url" id="twitter" name="twitter" class="form-control" value="<?php echo $edit_nominee ? htmlspecialchars($edit_nominee['twitter']) : ''; ?>">
                    </div>

                    <div class="form-group">
                        <label for="instagram"><i class="fab fa-instagram"></i> انستغرام</label>
                        <input type="url" id="instagram" name="instagram" class="form-control" value="<?php echo $edit_nominee ? htmlspecialchars($edit_nominee['instagram']) : ''; ?>">
                    </div>

                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                        <?php if ($edit_nominee): ?>
                        <a href="index.php?page=voting_nominees<?php echo $subcategory_id ? '&subcategory_id=' . $subcategory_id : ''; ?>" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h2>المرشحين</h2>
                <?php if (!$subcategory_id && count($all_subcategories) > 0): ?>
                <div class="subcategory-filter">
                    <form action="" method="get" class="form-inline">
                        <input type="hidden" name="page" value="voting_nominees">
                        <div class="form-group mr-2">
                            <select name="subcategory_id" class="form-control" onchange="this.form.submit()">
                                <option value="">-- جميع الفئات --</option>
                                <?php foreach ($all_subcategories as $sub): ?>
                                <option value="<?php echo $sub['id']; ?>" <?php echo $subcategory_id == $sub['id'] ? 'selected' : ''; ?>>
                                    <?php
                                    $sub_category = get_main_category($sub['category_id']);
                                    echo htmlspecialchars(get_main_category_name($sub_category) . ' / ' . get_subcategory_name($sub));
                                    ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (empty($all_categories)): ?>
                <div class="alert alert-warning">
                    لا توجد فئات رئيسية. يرجى <a href="index.php?page=voting_categories">إضافة فئات رئيسية</a> أولاً.
                </div>
                <?php elseif (empty($all_subcategories)): ?>
                <div class="alert alert-warning">
                    لا توجد فئات فرعية. يرجى <a href="index.php?page=voting_subcategories">إضافة فئات فرعية</a> أولاً.
                </div>
                <?php elseif ($subcategory_id > 0 && empty($nominees)): ?>
                <div class="alert alert-info">
                    لا يوجد مرشحين لهذه الفئة. قم بإضافة مرشحين جدد.
                </div>
                <?php elseif (empty($nominees) && !$subcategory_id): ?>
                <div class="alert alert-info">
                    لا يوجد مرشحين. قم بإضافة مرشحين جدد.
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <?php if (!$subcategory_id): ?>
                                <th>الفئة</th>
                                <?php endif; ?>
                                <th>الأصوات</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($nominees as $nominee): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($nominee['image'])): ?>
                                    <img src="<?php echo asset_url('uploads/' . $nominee['image']); ?>" alt="<?php echo htmlspecialchars($nominee['name']); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%;">
                                    <?php else: ?>
                                    <div class="no-image">لا توجد صورة</div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($current_lang === 'ar' ? $nominee['name_ar'] : $nominee['name']); ?></td>
                                <?php if (!$subcategory_id): ?>
                                <td>
                                    <?php
                                    $nominee_subcategory = get_subcategory($nominee['subcategory_id']);
                                    $nominee_category = $nominee_subcategory ? get_main_category($nominee_subcategory['category_id']) : null;

                                    if ($nominee_subcategory && $nominee_category) {
                                        echo htmlspecialchars(get_main_category_name($nominee_category) . ' / ' . get_subcategory_name($nominee_subcategory));
                                    } else {
                                        echo 'غير محدد';
                                    }
                                    ?>
                                </td>
                                <?php endif; ?>
                                <td><?php echo (int)$nominee['votes_count']; ?></td>
                                <td>
                                    <a href="index.php?page=voting_nominees&subcategory_id=<?php echo $nominee['subcategory_id']; ?>&edit=<?php echo $nominee['id']; ?>" class="btn btn-sm btn-primary"><i class="fas fa-edit"></i> تعديل</a>
                                    <form action="" method="post" class="d-inline delete-form" onsubmit="return confirm('هل أنت متأكد من حذف هذا المرشح؟');">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="<?php echo $nominee['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> حذف</button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
