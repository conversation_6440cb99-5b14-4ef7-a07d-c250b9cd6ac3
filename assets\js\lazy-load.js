/**
 * Lazy Loading محسن للصور والفيديوهات
 * يحسن سرعة تحميل الصفحة بشكل كبير
 */

class LazyLoader {
    constructor() {
        this.imageObserver = null;
        this.videoObserver = null;
        this.init();
    }

    init() {
        // التحقق من دعم Intersection Observer
        if ('IntersectionObserver' in window) {
            this.setupImageObserver();
            this.setupVideoObserver();
            this.observeElements();
        } else {
            // Fallback للمتصفحات القديمة
            this.loadAllImages();
            this.loadAllVideos();
        }
    }

    setupImageObserver() {
        const imageOptions = {
            root: null,
            rootMargin: '50px 0px', // تحميل قبل 50px من الوصول
            threshold: 0.01
        };

        this.imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadImage(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, imageOptions);
    }

    setupVideoObserver() {
        const videoOptions = {
            root: null,
            rootMargin: '100px 0px', // تحميل قبل 100px من الوصول
            threshold: 0.1
        };

        this.videoObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.loadVideo(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, videoOptions);
    }

    observeElements() {
        // مراقبة الصور
        const lazyImages = document.querySelectorAll('img[data-src], img[data-lazy]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });

        // مراقبة الفيديوهات
        const lazyVideos = document.querySelectorAll('video[data-src], video[data-lazy]');
        lazyVideos.forEach(video => {
            this.videoObserver.observe(video);
        });

        // مراقبة الخلفيات
        const lazyBackgrounds = document.querySelectorAll('[data-bg]');
        lazyBackgrounds.forEach(element => {
            this.imageObserver.observe(element);
        });
    }

    loadImage(img) {
        // تحميل الصورة الأساسية
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        }

        // تحميل الصورة البديلة (srcset)
        if (img.dataset.srcset) {
            img.srcset = img.dataset.srcset;
            img.removeAttribute('data-srcset');
        }

        // تحميل خلفية العنصر
        if (img.dataset.bg) {
            img.style.backgroundImage = `url(${img.dataset.bg})`;
            img.removeAttribute('data-bg');
        }

        // إضافة كلاس loaded
        img.classList.add('lazy-loaded');
        
        // إزالة placeholder
        img.classList.remove('lazy-placeholder');

        console.log('✅ Image loaded:', img.src || img.dataset.bg);
    }

    loadVideo(video) {
        // تحميل مصدر الفيديو
        if (video.dataset.src) {
            video.src = video.dataset.src;
            video.removeAttribute('data-src');
        }

        // تحميل مصادر متعددة
        const sources = video.querySelectorAll('source[data-src]');
        sources.forEach(source => {
            source.src = source.dataset.src;
            source.removeAttribute('data-src');
        });

        // تحميل poster
        if (video.dataset.poster) {
            video.poster = video.dataset.poster;
            video.removeAttribute('data-poster');
        }

        // تحميل الفيديو
        video.load();
        
        // إضافة كلاس loaded
        video.classList.add('lazy-loaded');
        video.classList.remove('lazy-placeholder');

        console.log('✅ Video loaded:', video.src);
    }

    loadAllImages() {
        // Fallback للمتصفحات القديمة
        const lazyImages = document.querySelectorAll('img[data-src], [data-bg]');
        lazyImages.forEach(element => {
            this.loadImage(element);
        });
    }

    loadAllVideos() {
        // Fallback للمتصفحات القديمة
        const lazyVideos = document.querySelectorAll('video[data-src]');
        lazyVideos.forEach(video => {
            this.loadVideo(video);
        });
    }

    // إضافة عناصر جديدة للمراقبة (للمحتوى الديناميكي)
    observeNewElements() {
        this.observeElements();
    }
}

// تشغيل Lazy Loading عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Lazy Loading...');
    window.lazyLoader = new LazyLoader();
});

// إعادة تشغيل للمحتوى الديناميكي
window.addEventListener('load', function() {
    if (window.lazyLoader) {
        window.lazyLoader.observeNewElements();
    }
});

// CSS للـ Placeholder
const lazyStyles = `
<style>
.lazy-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 0.9rem;
}

.lazy-placeholder::before {
    content: "جاري التحميل...";
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.lazy-loaded {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* تحسين الصور أثناء التحميل */
img[data-src] {
    filter: blur(5px);
    transition: filter 0.3s;
}

img.lazy-loaded {
    filter: blur(0);
}
</style>
`;

// إضافة الـ CSS للصفحة
document.head.insertAdjacentHTML('beforeend', lazyStyles);
