<?php
/**
 * Gallery Management Page
 */

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $result = add_gallery_image();
                break;
            case 'edit':
                $result = edit_gallery_image();
                break;
            case 'delete':
                $result = delete_gallery_image();
                break;
            case 'toggle_status':
                $result = toggle_gallery_status();
                break;
        }
        
        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    }
}

// Get all gallery images
$gallery_images = get_all_gallery_images();

function add_gallery_image() {
    global $conn;
    
    try {
        $title = trim($_POST['title']);
        $title_ar = trim($_POST['title_ar']);
        $description = trim($_POST['description'] ?? '');
        $description_ar = trim($_POST['description_ar'] ?? '');
        $category = trim($_POST['category'] ?? 'general');
        $category_ar = trim($_POST['category_ar'] ?? 'عام');
        $featured = isset($_POST['featured']) ? 1 : 0;
        $sort_order = intval($_POST['sort_order'] ?? 0);
        
        // Handle image upload
        $image = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../assets/uploads/';
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (!in_array($file_extension, $allowed_extensions)) {
                return ['success' => false, 'message' => 'نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP'];
            }
            
            if ($_FILES['image']['size'] > 10 * 1024 * 1024) { // 10MB
                return ['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى 10MB'];
            }
            
            $image = 'gallery_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
            
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image)) {
                return ['success' => false, 'message' => 'خطأ في رفع الصورة'];
            }
        }
        
        $sql = "INSERT INTO gallery (title, title_ar, description, description_ar, image, category, category_ar, featured, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('sssssssii', $title, $title_ar, $description, $description_ar, $image, $category, $category_ar, $featured, $sort_order);
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم إضافة الصورة للمعرض بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في إضافة الصورة للمعرض'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

function edit_gallery_image() {
    global $conn;
    
    try {
        $id = intval($_POST['id']);
        $title = trim($_POST['title']);
        $title_ar = trim($_POST['title_ar']);
        $description = trim($_POST['description'] ?? '');
        $description_ar = trim($_POST['description_ar'] ?? '');
        $category = trim($_POST['category'] ?? 'general');
        $category_ar = trim($_POST['category_ar'] ?? 'عام');
        $featured = isset($_POST['featured']) ? 1 : 0;
        $sort_order = intval($_POST['sort_order'] ?? 0);
        
        // Handle image upload
        $image_sql = '';
        $image_param = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../assets/uploads/';
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (!in_array($file_extension, $allowed_extensions)) {
                return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
            }
            
            if ($_FILES['image']['size'] > 10 * 1024 * 1024) {
                return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
            }
            
            // Delete old image
            $old_result = $conn->query("SELECT image FROM gallery WHERE id = $id");
            if ($old_result && $old_row = $old_result->fetch_assoc()) {
                $old_image = $upload_dir . $old_row['image'];
                if (file_exists($old_image)) {
                    unlink($old_image);
                }
            }
            
            $image = 'gallery_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image)) {
                $image_sql = ', image = ?';
                $image_param = $image;
            }
        }
        
        $sql = "UPDATE gallery SET title = ?, title_ar = ?, description = ?, description_ar = ?, category = ?, category_ar = ?, featured = ?, sort_order = ?" . $image_sql . " WHERE id = ?";
        $stmt = $conn->prepare($sql);
        
        if ($image_param) {
            $stmt->bind_param('ssssssiisi', $title, $title_ar, $description, $description_ar, $category, $category_ar, $featured, $sort_order, $image_param, $id);
        } else {
            $stmt->bind_param('ssssssiii', $title, $title_ar, $description, $description_ar, $category, $category_ar, $featured, $sort_order, $id);
        }
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم تحديث الصورة بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في تحديث الصورة'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

function delete_gallery_image() {
    global $conn;
    
    try {
        $id = intval($_POST['id']);
        
        // Get image name to delete file
        $result = $conn->query("SELECT image FROM gallery WHERE id = $id");
        if ($result && $row = $result->fetch_assoc()) {
            $image_file = '../assets/uploads/' . $row['image'];
            if (file_exists($image_file)) {
                unlink($image_file);
            }
        }
        
        $sql = "DELETE FROM gallery WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم حذف الصورة بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في حذف الصورة'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

function toggle_gallery_status() {
    global $conn;
    
    try {
        $id = intval($_POST['id']);
        
        $sql = "UPDATE gallery SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم تغيير حالة الصورة بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في تغيير حالة الصورة'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

function get_all_gallery_images() {
    global $conn;
    
    $sql = "SELECT * FROM gallery ORDER BY category ASC, sort_order ASC, id DESC";
    $result = $conn->query($sql);
    
    $images = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $images[] = $row;
        }
    }
    
    return $images;
}

// Get categories for filter
$categories = [];
foreach ($gallery_images as $image) {
    if (!in_array($image['category'], $categories)) {
        $categories[] = $image['category'];
    }
}
?>

<div class="admin-page-header">
    <h1><i class="fas fa-images"></i> إدارة معرض الصور</h1>
    <button class="btn btn-primary" onclick="openAddModal()">
        <i class="fas fa-plus"></i> إضافة صورة جديدة
    </button>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-error">
    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-images"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo count($gallery_images); ?></h3>
            <p>إجمالي الصور</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-star"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo count(array_filter($gallery_images, function($img) { return $img['featured'] == 1; })); ?></h3>
            <p>الصور المميزة</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo count(array_filter($gallery_images, function($img) { return $img['status'] === 'active'; })); ?></h3>
            <p>الصور النشطة</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-layer-group"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo count($categories); ?></h3>
            <p>التصنيفات</p>
        </div>
    </div>
</div>

<!-- Gallery Table -->
<div class="admin-card">
    <div class="card-header">
        <h3><i class="fas fa-list"></i> قائمة صور المعرض</h3>
        <div class="card-actions">
            <select id="categoryFilter" onchange="filterByCategory()">
                <option value="">جميع التصنيفات</option>
                <?php foreach ($categories as $category): ?>
                <option value="<?php echo $category; ?>"><?php echo ucfirst($category); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="admin-table" id="galleryTable">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>العنوان</th>
                        <th>التصنيف</th>
                        <th>مميزة</th>
                        <th>الترتيب</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($gallery_images as $image): ?>
                    <tr data-category="<?php echo $image['category']; ?>">
                        <td>
                            <div class="gallery-image-preview">
                                <?php if (!empty($image['image'])): ?>
                                <img src="../assets/uploads/<?php echo $image['image']; ?>"
                                     alt="<?php echo htmlspecialchars($image['title']); ?>"
                                     onerror="this.src='../assets/img/default-gallery.jpg'"
                                     onclick="previewImage('../assets/uploads/<?php echo $image['image']; ?>', '<?php echo addslashes($image['title']); ?>')">
                                <?php else: ?>
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="gallery-title">
                                <strong><?php echo htmlspecialchars($image['title']); ?></strong>
                                <br>
                                <small><?php echo htmlspecialchars($image['title_ar']); ?></small>
                            </div>
                        </td>
                        <td>
                            <span class="category-badge">
                                <?php echo ucfirst($image['category']); ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($image['featured']): ?>
                            <span class="status-badge featured">مميزة</span>
                            <?php else: ?>
                            <span class="status-badge">عادية</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $image['sort_order']; ?></td>
                        <td>
                            <button class="status-toggle <?php echo $image['status']; ?>" 
                                    onclick="toggleStatus(<?php echo $image['id']; ?>)">
                                <?php echo $image['status'] === 'active' ? 'نشطة' : 'غير نشطة'; ?>
                            </button>
                        </td>
                        <td><?php echo date('Y-m-d', strtotime($image['created_at'])); ?></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-primary" onclick="editImage(<?php echo $image['id']; ?>)" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-info" onclick="previewImage('../assets/uploads/<?php echo $image['image']; ?>', '<?php echo addslashes($image['title']); ?>')" title="معاينة">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteImage(<?php echo $image['id']; ?>)" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php if (empty($gallery_images)): ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-images" style="font-size: 3rem; margin-bottom: 15px; display: block; opacity: 0.3;"></i>
                            <h4>لا توجد صور في المعرض</h4>
                            <p>ابدأ بإضافة أول صورة للمعرض</p>
                            <button class="btn btn-primary" onclick="openAddModal()" style="margin-top: 15px;">
                                <i class="fas fa-plus"></i> إضافة صورة جديدة
                            </button>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
<div id="galleryModal" class="modal">
    <div class="modal-content large">
        <div class="modal-header">
            <h3 id="modalTitle">إضافة صورة للمعرض</h3>
            <span class="close" onclick="closeModal()">&times;</span>
        </div>
        <form id="galleryForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" id="formAction" value="add">
            <input type="hidden" name="id" id="imageId">

            <div class="form-row">
                <div class="form-group">
                    <label for="title">العنوان (English) <span class="required">*</span></label>
                    <input type="text" name="title" id="title" required placeholder="Enter title in English">
                </div>
                <div class="form-group">
                    <label for="title_ar">العنوان (العربية) <span class="required">*</span></label>
                    <input type="text" name="title_ar" id="title_ar" required placeholder="أدخل العنوان بالعربية">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="description">الوصف (English)</label>
                    <textarea name="description" id="description" rows="3" placeholder="Enter description in English"></textarea>
                </div>
                <div class="form-group">
                    <label for="description_ar">الوصف (العربية)</label>
                    <textarea name="description_ar" id="description_ar" rows="3" placeholder="أدخل الوصف بالعربية"></textarea>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="category">التصنيف (English) <span class="required">*</span></label>
                    <select name="category" id="category" required>
                        <option value="">اختر التصنيف</option>
                        <option value="ceremony">Ceremony</option>
                        <option value="awards">Awards</option>
                        <option value="red_carpet">Red Carpet</option>
                        <option value="networking">Networking</option>
                        <option value="behind_scenes">Behind Scenes</option>
                        <option value="workshops">Workshops</option>
                        <option value="exhibitions">Exhibitions</option>
                        <option value="general">General</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="category_ar">التصنيف (العربية) <span class="required">*</span></label>
                    <select name="category_ar" id="category_ar" required>
                        <option value="">اختر التصنيف</option>
                        <option value="الحفلات">الحفلات</option>
                        <option value="الجوائز">الجوائز</option>
                        <option value="السجادة الحمراء">السجادة الحمراء</option>
                        <option value="التواصل">التواصل</option>
                        <option value="خلف الكواليس">خلف الكواليس</option>
                        <option value="ورش العمل">ورش العمل</option>
                        <option value="المعارض">المعارض</option>
                        <option value="عام">عام</option>
                    </select>
                </div>
            </div>

            <div class="form-group">
                <label for="sort_order">ترتيب العرض</label>
                <input type="number" name="sort_order" id="sort_order" value="0" min="0" placeholder="0">
            </div>

            <div class="form-group">
                <label for="image">الصورة <span class="required">*</span></label>
                <input type="file" name="image" id="image" accept="image/*">
                <small>الحد الأقصى: 10MB. الأنواع المدعومة: JPG, PNG, GIF, WebP. الأبعاد المثلى: 350x250 بكسل</small>
                <div id="imagePreview" class="image-preview"></div>
            </div>

            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="featured" id="featured">
                    <span class="checkmark"></span>
                    صورة مميزة (ستظهر في المقدمة)
                </label>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ الصورة
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Image Preview Modal -->
<div id="previewModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="previewTitle">معاينة الصورة</h3>
            <span class="close" onclick="closePreview()">&times;</span>
        </div>
        <div class="preview-container">
            <img id="previewImage" src="" alt="Preview">
        </div>
    </div>
</div>

<style>
.gallery-image-preview {
    width: 80px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e0e0e0;
    background: #f8f9fa;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-image-preview:hover {
    transform: scale(1.05);
}

.gallery-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1.5rem;
}

.gallery-title {
    max-width: 200px;
}

.gallery-title strong {
    color: #333;
    font-size: 0.95rem;
    display: block;
    margin-bottom: 4px;
}

.gallery-title small {
    color: #666;
    font-size: 0.85rem;
    font-style: italic;
}

.category-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: capitalize;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1976d2;
    border: 1px solid #2196f3;
}

.status-badge {
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.featured {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #f57c00;
    border: 1px solid #ff9800;
}

.status-toggle {
    padding: 6px 14px;
    border: none;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.status-toggle.active {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #28a745;
}

.status-toggle.active:hover {
    background: linear-gradient(135deg, #c3e6cb, #b8dacc);
    transform: translateY(-1px);
}

.status-toggle.inactive {
    background: linear-gradient(135deg, #f8d7da, #f1b0b7);
    color: #721c24;
    border: 1px solid #dc3545;
}

.status-toggle.inactive:hover {
    background: linear-gradient(135deg, #f1b0b7, #e2a6ad);
    transform: translateY(-1px);
}

.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-buttons .btn {
    padding: 8px 12px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.card-actions select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
}

.required {
    color: #dc3545;
    font-weight: bold;
}

.modal-content.large {
    max-width: 900px;
    width: 90%;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
    margin-left: 10px;
    width: auto;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.image-preview {
    margin-top: 15px;
    max-width: 200px;
}

.image-preview img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-container {
    text-align: center;
    padding: 20px;
}

.preview-container img {
    max-width: 100%;
    max-height: 70vh;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-content.large {
        width: 95%;
        margin: 20px auto;
    }

    .gallery-title {
        max-width: 150px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .gallery-image-preview {
        width: 60px;
        height: 45px;
    }
}
</style>

<script>
// Gallery data for editing
const galleryData = <?php echo json_encode($gallery_images); ?>;

function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة صورة للمعرض';
    document.getElementById('formAction').value = 'add';
    document.getElementById('galleryForm').reset();
    document.getElementById('imageId').value = '';
    document.getElementById('imagePreview').innerHTML = '';
    document.getElementById('galleryModal').style.display = 'block';
}

function editImage(id) {
    const image = galleryData.find(img => img.id == id);
    if (!image) {
        alert('لم يتم العثور على البيانات');
        return;
    }

    document.getElementById('modalTitle').textContent = 'تعديل صورة المعرض';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('imageId').value = id;

    document.getElementById('title').value = image.title || '';
    document.getElementById('title_ar').value = image.title_ar || '';
    document.getElementById('description').value = image.description || '';
    document.getElementById('description_ar').value = image.description_ar || '';
    document.getElementById('category').value = image.category || '';
    document.getElementById('category_ar').value = image.category_ar || '';
    document.getElementById('sort_order').value = image.sort_order || 0;
    document.getElementById('featured').checked = image.featured == 1;

    // Show current image
    const imagePreview = document.getElementById('imagePreview');
    if (image.image) {
        imagePreview.innerHTML = `<img src="../assets/uploads/${image.image}" alt="Current image">`;
    } else {
        imagePreview.innerHTML = '';
    }

    document.getElementById('galleryModal').style.display = 'block';
}

function deleteImage(id) {
    if (confirm('هل أنت متأكد من حذف هذه الصورة؟\n\nسيتم حذف الصورة نهائياً ولا يمكن التراجع عن هذا الإجراء.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function toggleStatus(id) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="toggle_status">
        <input type="hidden" name="id" value="${id}">
    `;
    document.body.appendChild(form);
    form.submit();
}

function filterByCategory() {
    const filter = document.getElementById('categoryFilter').value;
    const rows = document.querySelectorAll('#galleryTable tbody tr');
    let visibleCount = 0;

    rows.forEach(row => {
        const category = row.getAttribute('data-category');
        if (filter === '' || category === filter) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Update table message if no results
    const tbody = document.querySelector('#galleryTable tbody');
    const existingMessage = tbody.querySelector('.no-results-message');

    if (visibleCount === 0 && filter !== '') {
        if (!existingMessage) {
            const messageRow = document.createElement('tr');
            messageRow.className = 'no-results-message';
            messageRow.innerHTML = `
                <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 10px; display: block;"></i>
                    لا توجد نتائج للفلتر المحدد
                </td>
            `;
            tbody.appendChild(messageRow);
        }
    } else if (existingMessage) {
        existingMessage.remove();
    }
}

function previewImage(src, title) {
    document.getElementById('previewImage').src = src;
    document.getElementById('previewTitle').textContent = title || 'معاينة الصورة';
    document.getElementById('previewModal').style.display = 'block';
}

function closeModal() {
    document.getElementById('galleryModal').style.display = 'none';
}

function closePreview() {
    document.getElementById('previewModal').style.display = 'none';
}

// Sync category selections
document.getElementById('category').addEventListener('change', function() {
    const categoryAr = document.getElementById('category_ar');
    const mapping = {
        'ceremony': 'الحفلات',
        'awards': 'الجوائز',
        'red_carpet': 'السجادة الحمراء',
        'networking': 'التواصل',
        'behind_scenes': 'خلف الكواليس',
        'workshops': 'ورش العمل',
        'exhibitions': 'المعارض',
        'general': 'عام'
    };
    categoryAr.value = mapping[this.value] || '';
});

document.getElementById('category_ar').addEventListener('change', function() {
    const category = document.getElementById('category');
    const mapping = {
        'الحفلات': 'ceremony',
        'الجوائز': 'awards',
        'السجادة الحمراء': 'red_carpet',
        'التواصل': 'networking',
        'خلف الكواليس': 'behind_scenes',
        'ورش العمل': 'workshops',
        'المعارض': 'exhibitions',
        'عام': 'general'
    };
    category.value = mapping[this.value] || '';
});

// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('imagePreview');

    if (file) {
        // Validate file size
        if (file.size > 10 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 10MB');
            this.value = '';
            preview.innerHTML = '';
            return;
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP');
            this.value = '';
            preview.innerHTML = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
});

// Close modals when clicking outside
window.onclick = function(event) {
    const galleryModal = document.getElementById('galleryModal');
    const previewModal = document.getElementById('previewModal');

    if (event.target === galleryModal) {
        galleryModal.style.display = 'none';
    }
    if (event.target === previewModal) {
        previewModal.style.display = 'none';
    }
}

// Form validation
document.getElementById('galleryForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const titleAr = document.getElementById('title_ar').value.trim();
    const category = document.getElementById('category').value;
    const categoryAr = document.getElementById('category_ar').value;
    const image = document.getElementById('image').files[0];
    const isEdit = document.getElementById('formAction').value === 'edit';

    if (!title || !titleAr || !category || !categoryAr) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (!isEdit && !image) {
        e.preventDefault();
        alert('يرجى اختيار صورة');
        return;
    }

    if (image && image.size > 10 * 1024 * 1024) {
        e.preventDefault();
        alert('حجم الصورة كبير جداً. الحد الأقصى 10MB');
        return;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    submitBtn.disabled = true;

    // Re-enable button after a delay (in case of errors)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>
