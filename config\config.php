<?php
/**
 * General Configuration
 *
 * This file contains general configuration settings for the Social Media Festival website.
 */

// Site settings
define('SITE_NAME', 'Social Media Festival');
define('SITE_URL', 'http://localhost/fast/');
define('ADMIN_EMAIL', '<EMAIL>');

// Default language (en or ar)
define('DEFAULT_LANG', 'en');

// File upload settings
define('UPLOAD_DIR', __DIR__ . '/../assets/uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/png', 'image/gif']);
define('ALLOWED_VIDEO_TYPES', ['video/mp4', 'video/webm']);

// Session settings are in session.php

// Error reporting
ini_set('display_errors', 1); // Set to 0 in production
ini_set('display_startup_errors', 1); // Set to 0 in production
error_reporting(E_ALL);

// Time zone
date_default_timezone_set('UTC');

// Security settings
define('CSRF_TOKEN_SECRET', 'social_media_festival_secret_key');
define('PASSWORD_HASH_COST', 10);

// Function to get base URL
function base_url($path = '') {
    return SITE_URL . '/' . ltrim($path, '/');
}

// Function to get asset URL
function asset_url($path = '') {
    $clean_path = ltrim($path, '/');
    $file_path = __DIR__ . '/../assets/' . $clean_path;

    // Add cache-busting parameter to prevent caching issues
    $timestamp = @filemtime($file_path) ?: time();

    return base_url('assets/' . $clean_path) . '?v=' . $timestamp;
}

// Function to redirect
function redirect($path) {
    header('Location: ' . base_url($path));
    exit;
}

// Function to sanitize input
function sanitize($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Function to generate CSRF token
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Function to verify CSRF token
function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token'], $token) && 
           is_string($token) && 
           is_string($_SESSION['csrf_token']) &&
           hash_equals($_SESSION['csrf_token'], $token);
}
