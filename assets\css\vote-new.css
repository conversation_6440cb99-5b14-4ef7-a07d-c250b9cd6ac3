/* Vote Page Styles */
.vote-section {
    padding: var(--spacing-xl) 0;
    background: linear-gradient(to bottom, #000000, #111111);
    min-height: 80vh;
}

.vote-section .section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.vote-section .section-header h1 {
    color: var(--color-gold);
    font-size: 2.5rem;
    margin-bottom: var(--spacing-sm);
}

.vote-section .section-header p {
    color: var(--color-light);
    font-size: 1.2rem;
}

.login-prompt {
    background: rgba(0, 0, 0, 0.5);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.login-prompt .alert {
    margin-bottom: var(--spacing-md);
    font-size: 1.1rem;
}

.login-prompt .btn {
    font-size: 1.1rem;
    padding: 0.75rem 2rem;
}

.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.5);
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.5);
    color: #dc3545;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.2);
    border: 1px solid rgba(255, 193, 7, 0.5);
    color: #ffc107;
}

/* Main Categories Section */
.main-categories-section {
    margin-bottom: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 100;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.95), rgba(0, 0, 0, 0.85));
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.main-categories-section h2 {
    color: var(--color-white);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    text-align: center;
    position: relative;
    display: inline-block;
}

.main-categories-slider {
    overflow: hidden;
    position: relative;
}

.categories-scroll {
    display: flex;
    overflow-x: auto;
    padding: var(--spacing-sm) 0;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--color-gold) rgba(0, 0, 0, 0.3);
}

.categories-scroll::-webkit-scrollbar {
    height: 6px;
}

.categories-scroll::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.categories-scroll::-webkit-scrollbar-thumb {
    background-color: var(--color-gold);
    border-radius: 10px;
}

.main-category-item {
    flex: 0 0 auto;
    width: 120px;
    margin-right: var(--spacing-sm);
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: center;
    text-decoration: none;
    color: var(--color-white);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.main-category-item:hover {
    transform: translateY(-5px);
    border-color: var(--color-gold);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.main-category-item.active {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    border-color: var(--color-gold);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
}

.category-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.category-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-icon i {
    font-size: 2rem;
    color: var(--color-gold);
}

.main-category-item h3 {
    font-size: 0.9rem;
    margin: 0;
    color: var(--color-white);
    transition: color 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.main-category-item:hover h3 {
    color: var(--color-gold);
}

/* Voting Content */
.voting-content {
    padding-top: var(--spacing-md);
}

/* Subcategories Section */
.subcategories-section {
    margin-bottom: var(--spacing-xl);
    background: linear-gradient(145deg, rgba(20, 20, 20, 0.7), rgba(10, 10, 10, 0.5));
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.subcategories-section h2 {
    color: var(--color-white);
    font-size: 1.8rem;
    margin-bottom: var(--spacing-md);
    text-align: center;
    position: relative;
    display: inline-block;
}

.subcategories-slider {
    overflow: hidden;
    position: relative;
}

.subcategories-scroll {
    display: flex;
    overflow-x: auto;
    padding: var(--spacing-sm) 0;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: var(--color-gold) rgba(0, 0, 0, 0.3);
}

.subcategories-scroll::-webkit-scrollbar {
    height: 6px;
}

.subcategories-scroll::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
}

.subcategories-scroll::-webkit-scrollbar-thumb {
    background-color: var(--color-gold);
    border-radius: 10px;
}

.subcategory-item {
    flex: 0 0 auto;
    width: 110px;
    margin-right: var(--spacing-sm);
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    text-align: center;
    text-decoration: none;
    color: var(--color-white);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.subcategory-item:hover {
    transform: translateY(-5px);
    border-color: var(--color-gold);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.subcategory-item.active {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
    border-color: var(--color-gold);
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
}

.subcategory-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm);
    overflow: hidden;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.subcategory-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.subcategory-icon i {
    font-size: 1.5rem;
    color: var(--color-gold);
}

.subcategory-item h3 {
    font-size: 0.85rem;
    margin: 0;
    color: var(--color-white);
    transition: color 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.subcategory-item:hover h3 {
    color: var(--color-gold);
}
