/* Nominees Page Styles */
:root {
    --nominees-bg-color: #000000;
    --nominees-text-color: #ffffff;
    --nominees-gold: #d4af37;
    --nominees-gold-light: #e5c158;
    --nominees-gold-dark: #b38728;
    --nominees-card-bg: #111111;
    --nominees-card-hover-bg: #1a1a1a;
    --nominees-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    --nominees-border-radius: 8px;
    --nominees-transition: all 0.3s ease;
}

.nominees-section {
    background: linear-gradient(to bottom, var(--nominees-bg-color), #111111);
    padding: 60px 0;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

/* No nominees message */
.no-nominees {
    text-align: center;
    padding: 60px 20px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--nominees-border-radius);
    border: 1px solid rgba(212, 175, 55, 0.1);
    margin: 40px auto;
    max-width: 600px;
}

.no-nominees i {
    font-size: 3rem;
    color: var(--nominees-gold);
    margin-bottom: 20px;
    opacity: 0.7;
}

.no-nominees p {
    color: var(--nominees-text-color);
    font-size: 1.2rem;
}

.nominees-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(212, 175, 55, 0.05), transparent 70%);
    pointer-events: none;
}

.nominees-header {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

.nominees-title {
    color: var(--nominees-gold);
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.nominees-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, transparent, var(--nominees-gold), transparent);
}

.nominees-description {
    color: var(--nominees-text-color);
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

/* Categories Filter */
.nominees-filter {
    margin-bottom: 40px;
}

.filter-tabs {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
}

.filter-tab {
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.5);
    color: var(--nominees-text-color);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 30px;
    cursor: pointer;
    transition: var(--nominees-transition);
    font-size: 0.9rem;
}

.filter-tab:hover {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-3px);
}

.filter-tab.active {
    background: linear-gradient(145deg, var(--nominees-gold), var(--nominees-gold-dark));
    color: #000;
    border-color: var(--nominees-gold);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Nominees Grid */
.nominees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.nominee-card {
    background: linear-gradient(145deg, var(--nominees-card-bg), #0a0a0a);
    border-radius: var(--nominees-border-radius);
    overflow: hidden;
    box-shadow: var(--nominees-shadow);
    transition: var(--nominees-transition);
    border: 1px solid rgba(212, 175, 55, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.nominee-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(212, 175, 55, 0.1), transparent);
    opacity: 0;
    transition: var(--nominees-transition);
    pointer-events: none;
}

.nominee-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
    border-color: rgba(212, 175, 55, 0.3);
}

.nominee-card:hover::before {
    opacity: 1;
}

.nominee-image {
    height: 300px;
    overflow: hidden;
    position: relative;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--nominees-transition);
}

.nominee-card:hover .nominee-image img {
    transform: scale(1.05);
}

.nominee-category {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0, 0, 0, 0.7);
    color: var(--nominees-gold);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    z-index: 1;
    border: 1px solid rgba(212, 175, 55, 0.3);
}

.nominee-info {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.nominee-name {
    color: var(--nominees-gold);
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.nominee-description {
    color: var(--nominees-text-color);
    opacity: 0.8;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 15px;
    flex: 1;
}

.nominee-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.nominee-votes {
    display: flex;
    align-items: center;
    color: var(--nominees-text-color);
    font-size: 0.9rem;
}

.nominee-votes i {
    color: var(--nominees-gold);
    margin-right: 5px;
}

.nominee-actions {
    display: flex;
    gap: 10px;
}

.btn-view, .btn-vote {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--nominees-transition);
    cursor: pointer;
    text-decoration: none;
}

.btn-view {
    background: rgba(255, 255, 255, 0.1);
    color: var(--nominees-text-color);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-vote {
    background: linear-gradient(145deg, var(--nominees-gold), var(--nominees-gold-dark));
    color: #000;
    border: none;
}

.btn-view:hover, .btn-vote:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-view i, .btn-vote i {
    margin-right: 5px;
}

/* Pagination */
.nominees-pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 40px;
}

.page-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    color: var(--nominees-text-color);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--nominees-transition);
    border: 1px solid rgba(212, 175, 55, 0.2);
}

.page-item.active {
    background: linear-gradient(145deg, var(--nominees-gold), var(--nominees-gold-dark));
    color: #000;
}

.page-item:hover:not(.active) {
    background: rgba(212, 175, 55, 0.2);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }

    .nominee-image {
        height: 250px;
    }
}

@media (max-width: 768px) {
    .nominees-section {
        padding: 40px 0;
    }

    .nominees-title {
        font-size: 2rem;
    }

    .filter-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        padding-bottom: 10px;
        justify-content: flex-start;
    }

    .filter-tab {
        white-space: nowrap;
        flex: 0 0 auto;
    }

    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 15px;
    }

    .nominee-image {
        height: 200px;
    }
}

@media (max-width: 576px) {
    .nominees-grid {
        grid-template-columns: 1fr;
    }

    .nominee-image {
        height: 250px;
    }
}
