<?php
/**
 * أداة ضغط ملفات CSS و JS
 */

$directories = ['assets/css/', 'assets/js/', 'js/', 'css/'];
$extensions = ['css', 'js'];
$total_compressed = 0;

echo "<h2>🧩 أداة ضغط ملفات CSS و JS</h2>";

foreach ($directories as $dir) {
    if (!is_dir($dir)) continue;

    echo "<h3>📁 المجلد: $dir</h3>";

    foreach ($extensions as $ext) {
        $files = glob($dir . "*.$ext");

        foreach ($files as $file) {
            if (str_contains($file, '.min.')) continue; // تجاهل الملفات المضغوطة مسبقًا

            $original = file_get_contents($file);
            $original_size = strlen($original);

            $compressed = compressCode($original, $ext);
            $compressed_size = strlen($compressed);
            $saved = $original_size - $compressed_size;

            $min_file = preg_replace("/\.$ext$/", ".min.$ext", $file);
            file_put_contents($min_file, $compressed);

            echo "✅ تم ضغط: <strong>" . basename($file) . "</strong> ➜ " . basename($min_file) . "<br>";
            echo "📦 الحجم: " . formatBytes($original_size) . " ➜ " . formatBytes($compressed_size) . " | 💾 توفير: " . formatBytes($saved) . "<br><br>";

            $total_compressed++;
        }
    }
}

echo "<hr><p>🎉 تم ضغط $total_compressed ملف</p>";

/**
 * دالة ضغط كود CSS أو JS
 */
function compressCode($code, $type) {
    // إزالة التعليقات
    if ($type === 'css') {
        $code = preg_replace('!/\*.*?\*/!s', '', $code); // تعليقات /* */
    } else if ($type === 'js') {
        $code = preg_replace('/\/\/[^\n\r]*/', '', $code); // تعليقات //
        $code = preg_replace('!/\*.*?\*/!s', '', $code);   // تعليقات /* */
    }

    // إزالة الفراغات
    $code = preg_replace('/\s+/', ' ', $code);
    $code = preg_replace('/\s*([{};:,])\s*/', '$1', $code);
    $code = trim($code);

    return $code;
}

/**
 * تنسيق الحجم
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}
