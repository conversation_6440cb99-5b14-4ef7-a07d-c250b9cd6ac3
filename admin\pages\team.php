<?php
/**
 * Team Management Page
 */

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    // Redirect to login page
    header('Location: login.php');
    exit;
}

// Process form submission for adding/editing team member
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'save') {
    $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    $name = $_POST['name'] ?? '';
    $position = $_POST['position'] ?? '';
    $position_ar = $_POST['position_ar'] ?? '';
    $display_order = (int)($_POST['display_order'] ?? 0);
    $facebook = $_POST['facebook'] ?? '';
    $twitter = $_POST['twitter'] ?? '';
    $instagram = $_POST['instagram'] ?? '';
    $linkedin = $_POST['linkedin'] ?? '';
    
    // Handle image upload
    $image = '';
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $upload_dir = '../assets/uploads/';
        $temp_name = $_FILES['image']['tmp_name'];
        $original_name = $_FILES['image']['name'];
        $extension = pathinfo($original_name, PATHINFO_EXTENSION);
        $new_name = 'team_' . time() . '.' . $extension;
        
        if (move_uploaded_file($temp_name, $upload_dir . $new_name)) {
            $image = $new_name;
        }
    }
    
    if ($id > 0) {
        // Update existing team member
        $update_sql = "UPDATE team_members SET 
                      name = ?, 
                      position = ?, 
                      position_ar = ?, 
                      display_order = ?, 
                      facebook = ?, 
                      twitter = ?, 
                      instagram = ?, 
                      linkedin = ?";
        
        // Only update image if a new one was uploaded
        if (!empty($image)) {
            $update_sql .= ", image = ?";
        }
        
        $update_sql .= " WHERE id = ?";
        
        $stmt = $conn->prepare($update_sql);
        
        if (!empty($image)) {
            $stmt->bind_param("ssssssssi", $name, $position, $position_ar, $display_order, $facebook, $twitter, $instagram, $linkedin, $image, $id);
        } else {
            $stmt->bind_param("sssissssi", $name, $position, $position_ar, $display_order, $facebook, $twitter, $instagram, $linkedin, $id);
        }
        
        if ($stmt->execute()) {
            $success_message = "تم تحديث عضو الفريق بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء تحديث البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    } else {
        // Insert new team member
        $insert_sql = "INSERT INTO team_members (name, position, position_ar, display_order, facebook, twitter, instagram, linkedin, image) 
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_sql);
        $stmt->bind_param("sssssssss", $name, $position, $position_ar, $display_order, $facebook, $twitter, $instagram, $linkedin, $image);
        
        if ($stmt->execute()) {
            $success_message = "تم إضافة عضو الفريق بنجاح";
        } else {
            $error_message = "حدث خطأ أثناء إضافة البيانات: " . $stmt->error;
        }
        
        $stmt->close();
    }
}

// Process delete request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $id = (int)$_POST['id'];
    
    // Get the image filename before deleting
    $image_sql = "SELECT image FROM team_members WHERE id = ?";
    $stmt = $conn->prepare($image_sql);
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $stmt->bind_result($image_filename);
    $stmt->fetch();
    $stmt->close();
    
    // Delete the team member
    $delete_sql = "DELETE FROM team_members WHERE id = ?";
    $stmt = $conn->prepare($delete_sql);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        // Delete the image file if it exists
        if (!empty($image_filename)) {
            $image_path = '../assets/uploads/' . $image_filename;
            if (file_exists($image_path)) {
                unlink($image_path);
            }
        }
        
        $success_message = "تم حذف عضو الفريق بنجاح";
    } else {
        $error_message = "حدث خطأ أثناء حذف البيانات: " . $stmt->error;
    }
    
    $stmt->close();
}

// Get team members
$team_members = get_team_members();

// Get team member for editing
$edit_member = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    $edit_id = (int)$_GET['edit'];
    
    foreach ($team_members as $member) {
        if ($member['id'] == $edit_id) {
            $edit_member = $member;
            break;
        }
    }
}
?>

<div class="page-header">
    <h1><i class="fas fa-users-cog"></i> إدارة فريق العمل</h1>
    <p>إضافة وتعديل وحذف أعضاء فريق العمل</p>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success">
    <?php echo $success_message; ?>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-danger">
    <?php echo $error_message; ?>
</div>
<?php endif; ?>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h2><?php echo $edit_member ? 'تعديل عضو الفريق' : 'إضافة عضو جديد'; ?></h2>
            </div>
            <div class="card-body">
                <form action="" method="post" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="save">
                    <?php if ($edit_member): ?>
                    <input type="hidden" name="id" value="<?php echo $edit_member['id']; ?>">
                    <?php endif; ?>
                    
                    <div class="form-group">
                        <label for="name">الاسم</label>
                        <input type="text" id="name" name="name" class="form-control" value="<?php echo $edit_member ? htmlspecialchars($edit_member['name']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="position">المنصب (الإنجليزية)</label>
                        <input type="text" id="position" name="position" class="form-control" value="<?php echo $edit_member ? htmlspecialchars($edit_member['position']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="position_ar">المنصب (العربية)</label>
                        <input type="text" id="position_ar" name="position_ar" class="form-control" value="<?php echo $edit_member ? htmlspecialchars($edit_member['position_ar']) : ''; ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="display_order">ترتيب العرض</label>
                        <input type="number" id="display_order" name="display_order" class="form-control" value="<?php echo $edit_member ? (int)$edit_member['display_order'] : count($team_members) + 1; ?>" min="1">
                    </div>
                    
                    <div class="form-group">
                        <label for="image">الصورة الشخصية</label>
                        <?php if ($edit_member && !empty($edit_member['image'])): ?>
                        <div class="current-image">
                            <img src="<?php echo asset_url('uploads/' . $edit_member['image']); ?>" alt="<?php echo htmlspecialchars($edit_member['name']); ?>" style="max-width: 100px; margin-bottom: 10px;">
                            <p>الصورة الحالية</p>
                        </div>
                        <?php endif; ?>
                        <input type="file" id="image" name="image" class="form-control-file" <?php echo !$edit_member ? 'required' : ''; ?>>
                        <small class="form-text text-muted">يفضل صورة بأبعاد 500×500 بكسل</small>
                    </div>
                    
                    <h3 class="mt-4">روابط التواصل الاجتماعي</h3>
                    
                    <div class="form-group">
                        <label for="facebook"><i class="fab fa-facebook"></i> فيسبوك</label>
                        <input type="url" id="facebook" name="facebook" class="form-control" value="<?php echo $edit_member ? htmlspecialchars($edit_member['facebook']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="twitter"><i class="fab fa-twitter"></i> تويتر</label>
                        <input type="url" id="twitter" name="twitter" class="form-control" value="<?php echo $edit_member ? htmlspecialchars($edit_member['twitter']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="instagram"><i class="fab fa-instagram"></i> انستغرام</label>
                        <input type="url" id="instagram" name="instagram" class="form-control" value="<?php echo $edit_member ? htmlspecialchars($edit_member['instagram']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="linkedin"><i class="fab fa-linkedin"></i> لينكد إن</label>
                        <input type="url" id="linkedin" name="linkedin" class="form-control" value="<?php echo $edit_member ? htmlspecialchars($edit_member['linkedin']) : ''; ?>">
                    </div>
                    
                    <div class="form-group text-center">
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ</button>
                        <?php if ($edit_member): ?>
                        <a href="index.php?page=team" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h2>أعضاء الفريق</h2>
            </div>
            <div class="card-body">
                <?php if (empty($team_members)): ?>
                <div class="alert alert-info">
                    لا يوجد أعضاء في الفريق حالياً. قم بإضافة أعضاء جدد.
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم</th>
                                <th>المنصب</th>
                                <th>الترتيب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($team_members as $member): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($member['image'])): ?>
                                    <img src="<?php echo asset_url('uploads/' . $member['image']); ?>" alt="<?php echo htmlspecialchars($member['name']); ?>" style="width: 50px; height: 50px; object-fit: cover; border-radius: 50%;">
                                    <?php else: ?>
                                    <div class="no-image">لا توجد صورة</div>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($member['name']); ?></td>
                                <td><?php echo htmlspecialchars($current_lang === 'ar' ? $member['position_ar'] : $member['position']); ?></td>
                                <td><?php echo (int)$member['display_order']; ?></td>
                                <td>
                                    <a href="index.php?page=team&edit=<?php echo $member['id']; ?>" class="btn btn-sm btn-primary"><i class="fas fa-edit"></i> تعديل</a>
                                    <form action="" method="post" class="d-inline delete-form" onsubmit="return confirm('هل أنت متأكد من حذف هذا العضو؟');">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="<?php echo $member['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> حذف</button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
