/* Stories Page Styles */
:root {
    --stories-bg-color: #000000;
    --stories-text-color: #ffffff;
    --stories-gold: #d4af37;
    --stories-gold-light: #e5c158;
    --stories-gold-dark: #b38728;
    --stories-card-bg: #111111;
    --stories-card-hover-bg: #1a1a1a;
    --stories-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    --stories-border-radius: 8px;
    --stories-transition: all 0.3s ease;
}

.stories-section {
    background: linear-gradient(to bottom, var(--stories-bg-color), #111111);
    padding: 20px 0 80px; /* Less padding at top, more at bottom */
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.stories-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(212, 175, 55, 0.05), transparent 70%);
    pointer-events: none;
}

.stories-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
    padding-top: 20px; /* Add more space at the top */
}

.stories-title {
    color: var(--stories-gold);
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.stories-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, transparent, var(--stories-gold), transparent);
}

.stories-description {
    color: var(--stories-text-color);
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

/* Stories Grid */
.stories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 20px;
    min-height: 400px;
    margin-top: 40px; /* Add more space below header */
}

.story-card {
    background: linear-gradient(145deg, rgba(17, 17, 17, 0.7), rgba(10, 10, 10, 0.7)); /* More transparent background */
    border-radius: var(--stories-border-radius);
    overflow: hidden;
    box-shadow: var(--stories-shadow);
    transition: var(--stories-transition);
    border: 1px solid rgba(212, 175, 55, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    max-width: 400px; /* Limit width for better proportions */
    margin: 0 auto; /* Center cards */
    min-height: 600px; /* Ensure consistent height */
    backdrop-filter: blur(5px); /* Add blur effect for glass-like appearance */
}

.story-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(212, 175, 55, 0.05), transparent); /* Lighter gradient */
    
    transition: var(--stories-transition);
    pointer-events: none;
}

.story-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
    border-color: rgba(212, 175, 55, 0.3);
}

.story-card:hover::before {
    opacity: 1;
}

.story-video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
}

.story-video-container iframe,
.story-video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: none;
}

.story-thumbnail {
    position: relative;
    height: 400px; /* Taller height for vertical video format */
    overflow: hidden;
    cursor: pointer;
}

.story-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--stories-transition);
}

.story-card:hover .story-thumbnail img {
    transform: scale(1.05);
}

.story-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px; /* Larger play button */
    height: 80px;
    background: rgba(212, 175, 55, 0.6); /* More transparent gold */
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000;
    font-size: 32px; /* Larger icon */
    transition: var(--stories-transition);
    z-index: 2;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3); /* Lighter shadow */
    backdrop-filter: blur(3px); /* Add slight blur for glass effect */
}

.story-card:hover .story-play-button {
    background: rgba(212, 175, 55, 0.8); /* Still slightly transparent when hovered */
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow: 0 0 30px rgba(212, 175, 55, 0.3); /* Gold glow effect */
}

.story-info {
    padding: 25px;
    flex: 1;
    display: flex;
    flex-direction: column;
    background: linear-gradient(to bottom, rgba(0,0,0,0.5), rgba(0,0,0,0.7)); /* Lighter background */
}

.story-title {
    color: var(--stories-gold);
    font-size: 1.5rem; /* Larger title */
    margin-bottom: 15px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* Lighter shadow */
    background: linear-gradient(to right, var(--stories-gold), var(--stories-gold-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.story-description {
    color: var(--stories-text-color);
    opacity: 1; /* Fully visible for better readability */
    font-size: 1rem; /* Larger text */
    line-height: 1.7;
    margin-bottom: 20px;
    flex: 1;
    max-width: 90%; /* Prevent text from stretching too wide */
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* Very subtle shadow */
}

.story-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(212, 175, 55, 0.1); /* Gold border instead of white */
}

.story-date {
    color: var(--stories-text-color);
    opacity: 0.7;
    font-size: 0.85rem;
}

.story-actions {
    display: flex;
    gap: 10px;
}

.btn-share {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--stories-transition);
    cursor: pointer;
    text-decoration: none;
    background: rgba(212, 175, 55, 0.1); /* Gold tint */
    color: var(--stories-gold-light); /* Gold text */
    border: 1px solid rgba(212, 175, 55, 0.2); /* Gold border */
    backdrop-filter: blur(2px); /* Slight blur for glass effect */
}

.btn-share:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    background: rgba(212, 175, 55, 0.2);
}

.btn-share i {
    margin-right: 5px;
}

/* Video Modal */
.video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.video-modal.active {
    opacity: 1;
    visibility: visible;
}

.video-modal-content {
    width: 90%;
    max-width: 500px; /* Narrower for vertical video format */
    max-height: 90vh;
    position: relative;
}

.video-modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.video-modal-close:hover {
    color: var(--stories-gold);
    transform: rotate(90deg);
}

.video-wrapper {
    position: relative;
    padding-bottom: 177.78%; /* 9:16 Aspect Ratio for vertical videos */
    height: 0;
    overflow: hidden;
}

.video-wrapper iframe,
.video-wrapper video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
}

/* Pagination */
.stories-pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 10%;
    margin-bottom: 30px;
}

.page-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.5);
    color: var(--stories-text-color);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--stories-transition);
    border: 1px solid rgba(212, 175, 55, 0.2);
    text-decoration: none;
    font-weight: bold;
    margin: 0 2px;
}

.page-item.active {
    background: linear-gradient(145deg, var(--stories-gold), var(--stories-gold-dark));
    color: #000;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
    transform: scale(1.1);
}

.page-item:hover:not(.active) {
    background: rgba(212, 175, 55, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .stories-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }

    .story-thumbnail {
        height: 350px; /* Slightly shorter on tablets */
    }
}

@media (max-width: 768px) {
    .stories-section {
        padding: 20px 0 50px;
    }

    .stories-title {
        font-size: 2rem;
    }

    .stories-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
    }

    .story-thumbnail {
        height: 320px; /* Even shorter on mobile */
    }

    .story-play-button {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
}

@media (max-width: 576px) {
    .stories-grid {
        grid-template-columns: 1fr;
        max-width: 320px;
        margin: 0 auto 20px;
    }

    .story-card {
        min-height: 550px;
    }

    .story-thumbnail {
        height: 400px; /* Taller again on small mobile for better proportions */
    }

    .story-info {
        padding: 20px 15px;
    }

    .story-title {
        font-size: 1.3rem;
    }

    .story-description {
        font-size: 0.9rem;
        line-height: 1.5;
    }
}
