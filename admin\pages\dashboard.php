<?php
/**
 * Admin Dashboard Page
 */

// Initialize arrays
$recent_votes = [];
$top_nominees = [];
$votes_by_category = [];
$votes_over_time = [];

// Load visits tracking system if not already loaded
if (!function_exists('get_daily_visits')) {
    require_once '../includes/visits.php';
}

// Get visits statistics
$daily_visits = get_daily_visits(7);
$total_visits = get_total_visits();

try {
    // Check if tables exist before querying
    $tables_result = $conn->query("SHOW TABLES");
    $tables = [];
    if ($tables_result) {
        while ($row = $tables_result->fetch_row()) {
            $tables[] = $row[0];
        }
    }

    // Get recent votes if tables exist - use voting_votes table
    if (in_array('voting_votes', $tables) && in_array('users', $tables) && in_array('celebrities', $tables)) {
        $recent_votes_sql = "
            SELECT v.id, v.created_at, u.username, u.full_name, c.name, c.name_ar,
                   COALESCE(c.role, 'Uncategorized') as category
            FROM voting_votes v
            JOIN users u ON v.user_id = u.id
            JOIN celebrities c ON v.nominee_id = c.id
            ORDER BY v.created_at DESC
            LIMIT 5
        ";

        $recent_votes_result = $conn->query($recent_votes_sql);

        if ($recent_votes_result && $recent_votes_result->num_rows > 0) {
            while ($row = $recent_votes_result->fetch_assoc()) {
                $recent_votes[] = $row;
            }
        }
    }

    // Get top nominees if celebrities table exists
    if (in_array('celebrities', $tables)) {
        // Check if votes column exists in celebrities table
        $columns_result = $conn->query("SHOW COLUMNS FROM celebrities");
        $columns = [];
        if ($columns_result) {
            while ($row = $columns_result->fetch_assoc()) {
                $columns[] = $row['Field'];
            }
        }

        // If votes column doesn't exist, we'll count votes from voting_votes table
        if (!in_array('votes', $columns) && in_array('voting_votes', $tables)) {
            $top_nominees_sql = "
                SELECT c.id, c.name, COALESCE(c.name_ar, c.name) as name_ar,
                       COALESCE(c.role, 'Uncategorized') as category, COUNT(v.id) as votes
                FROM celebrities c
                LEFT JOIN voting_votes v ON c.id = v.nominee_id
                GROUP BY c.id
                ORDER BY votes DESC
                LIMIT 5
            ";
        } else {
            // Use votes column if it exists
            $top_nominees_sql = "
                SELECT id, name, COALESCE(name_ar, name) as name_ar,
                       COALESCE(role, 'Uncategorized') as category,
                       COALESCE(votes, 0) as votes
                FROM celebrities
                ORDER BY votes DESC
                LIMIT 5
            ";
        }

        $top_nominees_result = $conn->query($top_nominees_sql);

        if ($top_nominees_result && $top_nominees_result->num_rows > 0) {
            while ($row = $top_nominees_result->fetch_assoc()) {
                $top_nominees[] = $row;
            }
        }

        // Get votes by category for chart
        if (!in_array('votes', $columns) && in_array('voting_votes', $tables)) {
            $votes_by_category_sql = "
                SELECT COALESCE(c.role, 'Uncategorized') as category, COUNT(v.id) as total_votes
                FROM celebrities c
                LEFT JOIN voting_votes v ON c.id = v.nominee_id
                GROUP BY c.role
                ORDER BY total_votes DESC
                LIMIT 5
            ";
        } else {
            $votes_by_category_sql = "
                SELECT COALESCE(role, 'Uncategorized') as category, SUM(COALESCE(votes, 0)) as total_votes
                FROM celebrities
                GROUP BY role
                ORDER BY total_votes DESC
                LIMIT 5
            ";
        }

        $votes_by_category_result = $conn->query($votes_by_category_sql);

        if ($votes_by_category_result && $votes_by_category_result->num_rows > 0) {
            while ($row = $votes_by_category_result->fetch_assoc()) {
                $votes_by_category[] = $row;
            }
        }
    }

    // Get votes over time for chart (last 7 days) - use voting_votes table
    if (in_array('voting_votes', $tables)) {
        $votes_over_time_sql = "
            SELECT DATE(created_at) as vote_date, COUNT(*) as vote_count
            FROM voting_votes
            WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(created_at)
            ORDER BY vote_date
        ";

        $votes_over_time_result = $conn->query($votes_over_time_sql);

        if ($votes_over_time_result && $votes_over_time_result->num_rows > 0) {
            while ($row = $votes_over_time_result->fetch_assoc()) {
                $votes_over_time[] = $row;
            }
        }

        // If no votes in the last 7 days, create empty data for the chart
        if (empty($votes_over_time)) {
            for ($i = 6; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-$i days"));
                $votes_over_time[] = [
                    'vote_date' => $date,
                    'vote_count' => 0
                ];
            }
        }
    }
} catch (Exception $e) {
    // Log error
    error_log("Error getting dashboard data: " . $e->getMessage());
}
?>

<div class="dashboard">
    <div class="dashboard-header">
        <h1 class="page-title">لوحة التحكم</h1>
        <div class="dashboard-actions">
            <div class="date-display">
                <i class="fas fa-calendar-alt"></i>
                <span><?php echo date('l, d F Y'); ?></span>
            </div>
            <button class="btn btn-primary btn-sm" onclick="window.print()">
                <i class="fas fa-print"></i>
                <span>طباعة التقرير</span>
            </button>
        </div>
    </div>

    <!-- Welcome Banner -->
    <div class="welcome-banner">
        <div class="welcome-content">
            <h2>مرحباً، <?php echo htmlspecialchars($user['full_name']); ?>!</h2>
            <p>مرحباً بك في لوحة تحكم مهرجان وسائل التواصل الاجتماعي. هنا يمكنك إدارة جميع جوانب المهرجان.</p>
        </div>
        <div class="welcome-image">
            <i class="fas fa-chart-line"></i>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="stats-overview">
        <h2 class="section-title">
            <i class="fas fa-chart-pie"></i>
            <span>نظرة عامة على الإحصائيات</span>
        </h2>
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-content">
                    <h3>المرشحون</h3>
                    <p class="stat-number"><?php echo $nominee_count; ?></p>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo min(100, ($nominee_count / 100) * 100); ?>%"></div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <div class="stat-content">
                    <h3>الرعاة</h3>
                    <p class="stat-number"><?php echo $sponsor_count; ?></p>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo min(100, ($sponsor_count / 50) * 100); ?>%"></div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="stat-content">
                    <h3>المشاهير</h3>
                    <p class="stat-number"><?php echo $celebrity_count; ?></p>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo min(100, ($celebrity_count / 100) * 100); ?>%"></div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <h3>المستخدمون</h3>
                    <p class="stat-number"><?php echo $user_count; ?></p>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo min(100, ($user_count / 500) * 100); ?>%"></div>
                    </div>
                </div>
            </div>

            <div class="stat-card highlight">
                <div class="stat-icon">
                    <i class="fas fa-vote-yea"></i>
                </div>
                <div class="stat-content">
                    <h3>الأصوات</h3>
                    <p class="stat-number"><?php echo $vote_count; ?></p>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo min(100, ($vote_count / 1000) * 100); ?>%"></div>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-content">
                    <h3>الزيارات</h3>
                    <p class="stat-number"><?php echo number_format($total_visits['total_visits']); ?></p>
                    <div class="stat-progress">
                        <div class="progress-bar" style="width: <?php echo min(100, ($total_visits['total_visits'] / 10000) * 100); ?>%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h2 class="section-title">
            <i class="fas fa-bolt"></i>
            <span>إجراءات سريعة</span>
        </h2>
        <div class="action-buttons">
            <a href="index.php?page=nominees&action=add" class="action-button">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة مرشح</span>
            </a>
            <a href="index.php?page=sponsors&action=add" class="action-button">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة راعي</span>
            </a>
            <a href="index.php?page=celebrities&action=add" class="action-button">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة مشهور</span>
            </a>
            <a href="index.php?page=content" class="action-button">
                <i class="fas fa-edit"></i>
                <span>تعديل المحتوى</span>
            </a>
            <a href="index.php?page=settings" class="action-button">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </div>
    </div>

    <!-- Charts -->
    <div class="dashboard-charts-section">
        <h2 class="section-title">
            <i class="fas fa-chart-bar"></i>
            <span>الرسوم البيانية</span>
        </h2>
        <div class="dashboard-charts">
            <div class="chart-container">
                <div class="chart-header">
                    <h2>الأصوات حسب الفئة</h2>
                    <div class="chart-actions">
                        <button class="btn btn-sm btn-icon" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-icon" title="تصدير">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <h2>الأصوات عبر الزمن</h2>
                    <div class="chart-actions">
                        <button class="btn btn-sm btn-icon" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-icon" title="تصدير">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
                <div class="chart-body">
                    <canvas id="timeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="dashboard-tables-section">
        <h2 class="section-title">
            <i class="fas fa-history"></i>
            <span>النشاط الأخير</span>
        </h2>
        <div class="dashboard-tables">
            <div class="table-container">
                <div class="table-header">
                    <h2>أحدث الأصوات</h2>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-icon" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-icon" title="تصدير">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>

                <?php if (empty($recent_votes)): ?>
                <div class="no-data-container">
                    <i class="fas fa-vote-yea no-data-icon"></i>
                    <p class="no-data">لا توجد أصوات حتى الآن.</p>
                    <a href="index.php?page=votes&action=add" class="btn btn-sm btn-primary">إضافة صوت</a>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>المستخدم</th>
                                <th>المرشح</th>
                                <th>الفئة</th>
                                <th>التاريخ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_votes as $vote): ?>
                            <tr>
                                <td>
                                    <div class="user-info">
                                        <div class="user-avatar">
                                            <?php echo strtoupper(substr($vote['full_name'], 0, 1)); ?>
                                        </div>
                                        <div class="user-details">
                                            <span class="user-name"><?php echo htmlspecialchars($vote['full_name']); ?></span>
                                            <span class="user-username">@<?php echo htmlspecialchars($vote['username']); ?></span>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo htmlspecialchars($current_lang === 'ar' ? $vote['name_ar'] : $vote['name']); ?></td>
                                <td>
                                    <span class="category-badge"><?php echo htmlspecialchars($vote['category']); ?></span>
                                </td>
                                <td>
                                    <div class="date-info">
                                        <span class="date"><?php echo format_date($vote['created_at'], 'M j, Y'); ?></span>
                                        <span class="time"><?php echo format_date($vote['created_at'], 'H:i'); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="table-actions">
                                        <a href="index.php?page=votes&action=view&id=<?php echo $vote['id']; ?>" class="btn btn-sm btn-icon" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>

                <div class="table-footer">
                    <a href="index.php?page=votes" class="btn btn-outline">عرض جميع الأصوات</a>
                </div>
            </div>

            <div class="table-container">
                <div class="table-header">
                    <h2>أفضل المرشحين</h2>
                    <div class="table-actions">
                        <button class="btn btn-sm btn-icon" title="تحديث">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                        <button class="btn btn-sm btn-icon" title="تصدير">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>

                <?php if (empty($top_nominees)): ?>
                <div class="no-data-container">
                    <i class="fas fa-trophy no-data-icon"></i>
                    <p class="no-data">لا يوجد مرشحون حتى الآن.</p>
                    <a href="index.php?page=nominees&action=add" class="btn btn-sm btn-primary">إضافة مرشح</a>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>المرشح</th>
                                <th>الفئة</th>
                                <th>الأصوات</th>
                                <th>النسبة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            // Calculate total votes for percentage
                            $total_nominee_votes = 0;
                            foreach ($top_nominees as $nominee) {
                                $total_nominee_votes += $nominee['votes'];
                            }

                            foreach ($top_nominees as $nominee):
                                $percentage = $total_nominee_votes > 0 ? round(($nominee['votes'] / $total_nominee_votes) * 100) : 0;
                            ?>
                            <tr>
                                <td>
                                    <div class="nominee-info">
                                        <div class="nominee-avatar">
                                            <?php echo strtoupper(substr($nominee['name'], 0, 1)); ?>
                                        </div>
                                        <span><?php echo htmlspecialchars($current_lang === 'ar' ? $nominee['name_ar'] : $nominee['name']); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <span class="category-badge"><?php echo htmlspecialchars($nominee['category']); ?></span>
                                </td>
                                <td><?php echo $nominee['votes']; ?></td>
                                <td>
                                    <div class="progress-container">
                                        <div class="progress-bar" style="width: <?php echo $percentage; ?>%"></div>
                                        <span class="progress-text"><?php echo $percentage; ?>%</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="table-actions">
                                        <a href="index.php?page=nominees&action=view&id=<?php echo $nominee['id']; ?>" class="btn btn-sm btn-icon" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="index.php?page=nominees&action=edit&id=<?php echo $nominee['id']; ?>" class="btn btn-sm btn-icon" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>

                <div class="table-footer">
                    <a href="index.php?page=nominees" class="btn btn-outline">عرض جميع المرشحين</a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="system-status">
        <h2 class="section-title">
            <i class="fas fa-server"></i>
            <span>حالة النظام</span>
        </h2>
        <div class="status-cards">
            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-database"></i>
                </div>
                <div class="status-content">
                    <h3>قاعدة البيانات</h3>
                    <span class="status-badge success">متصل</span>
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="status-content">
                    <h3>الخادم</h3>
                    <span class="status-badge success">يعمل</span>
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-memory"></i>
                </div>
                <div class="status-content">
                    <h3>الذاكرة</h3>
                    <div class="status-progress">
                        <div class="progress-bar" style="width: 45%"></div>
                        <span class="progress-text">45%</span>
                    </div>
                </div>
            </div>

            <div class="status-card">
                <div class="status-icon">
                    <i class="fas fa-hdd"></i>
                </div>
                <div class="status-content">
                    <h3>التخزين</h3>
                    <div class="status-progress">
                        <div class="progress-bar" style="width: 30%"></div>
                        <span class="progress-text">30%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Initialize charts when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Votes by Category Chart
    const categoryChartCtx = document.getElementById('categoryChart').getContext('2d');

    <?php if (empty($votes_by_category)): ?>
    // No data available, show empty chart with message
    new Chart(categoryChartCtx, {
        type: 'pie',
        data: {
            labels: ['لا توجد بيانات'],
            datasets: [{
                data: [1],
                backgroundColor: ['#333333'],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: '#ffffff'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function() {
                            return 'لا توجد بيانات متاحة';
                        }
                    }
                }
            }
        }
    });
    <?php else: ?>
    // Data available, show actual chart
    new Chart(categoryChartCtx, {
        type: 'pie',
        data: {
            labels: [
                <?php foreach ($votes_by_category as $category): ?>
                '<?php echo addslashes($category['category']); ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                data: [
                    <?php foreach ($votes_by_category as $category): ?>
                    <?php echo $category['total_votes']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: [
                    '#d4af37',
                    '#aa8c2c',
                    '#f1e5ac',
                    '#8a7424',
                    '#c9c9c9'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: '#ffffff'
                    }
                }
            }
        }
    });
    <?php endif; ?>

    // Votes Over Time Chart
    const timeChartCtx = document.getElementById('timeChart').getContext('2d');

    <?php if (empty($votes_over_time)): ?>
    // No data available, show empty chart with message
    new Chart(timeChartCtx, {
        type: 'line',
        data: {
            labels: ['اليوم'],
            datasets: [{
                label: 'الأصوات',
                data: [0],
                backgroundColor: 'rgba(212, 175, 55, 0.2)',
                borderColor: '#d4af37',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function() {
                            return 'لا توجد بيانات متاحة';
                        }
                    }
                }
            }
        }
    });
    <?php else: ?>
    // Data available, show actual chart
    new Chart(timeChartCtx, {
        type: 'line',
        data: {
            labels: [
                <?php foreach ($votes_over_time as $vote): ?>
                '<?php echo isset($vote['vote_date']) ? date('M j', strtotime($vote['vote_date'])) : ''; ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                label: 'الأصوات',
                data: [
                    <?php foreach ($votes_over_time as $vote): ?>
                    <?php echo isset($vote['vote_count']) ? $vote['vote_count'] : 0; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: 'rgba(212, 175, 55, 0.2)',
                borderColor: '#d4af37',
                borderWidth: 2,
                tension: 0.3
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                },
                x: {
                    ticks: {
                        color: '#ffffff'
                    },
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    }
                }
            },
            plugins: {
                legend: {
                    labels: {
                        color: '#ffffff'
                    }
                }
            }
        }
    });
    <?php endif; ?>
});
</script>
