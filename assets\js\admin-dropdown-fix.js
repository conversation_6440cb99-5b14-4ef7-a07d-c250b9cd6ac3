/**
 * Special fix for admin dropdown menus
 * This script is loaded after all other scripts to ensure it overrides any conflicting behavior
 */

document.addEventListener('DOMContentLoaded', function() {
    // Fix for content management dropdown
    setTimeout(function() {
        // Find all dropdown toggles in the sidebar
        const dropdownToggles = document.querySelectorAll('.sidebar-nav .dropdown-toggle');
        
        dropdownToggles.forEach(toggle => {
            // Remove any existing click handlers by cloning and replacing the element
            const newToggle = toggle.cloneNode(true);
            toggle.parentNode.replaceChild(newToggle, toggle);
            
            // Add our own click handler
            newToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Toggle active class
                this.classList.toggle('active');
                
                // Toggle dropdown menu
                const dropdownMenu = this.nextElementSibling;
                
                if (dropdownMenu) {
                    if (window.getComputedStyle(dropdownMenu).display === 'block') {
                        // Close dropdown with animation
                        dropdownMenu.style.maxHeight = '0';
                        setTimeout(() => {
                            dropdownMenu.style.display = 'none';
                        }, 300);
                    } else {
                        // Open dropdown with animation
                        dropdownMenu.style.display = 'block';
                        dropdownMenu.style.maxHeight = '0';
                        setTimeout(() => {
                            dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
                        }, 10);
                    }
                }
            });
            
            // Check if this dropdown should be active based on current page
            const dropdownMenu = newToggle.nextElementSibling;
            const activeItem = dropdownMenu ? dropdownMenu.querySelector('.active') : null;
            
            // If there's an active item in the dropdown, expand it
            if (activeItem) {
                newToggle.classList.add('active');
                dropdownMenu.style.display = 'block';
                dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
            }
            
            // Special handling for content management
            const spanElement = newToggle.querySelector('span');
            if (spanElement && (spanElement.textContent.includes('إدارة المحتوى') || spanElement.textContent.includes('Content Management'))) {
                // Check if we're on a content management page
                const currentPath = window.location.pathname + window.location.search;
                if (currentPath.includes('page=content') || currentPath.includes('page=header_content')) {
                    // Manually open the dropdown
                    newToggle.classList.add('active');
                    dropdownMenu.style.display = 'block';
                    dropdownMenu.style.maxHeight = dropdownMenu.scrollHeight + 'px';
                }
            }
        });
    }, 100); // Small delay to ensure DOM is fully loaded
});
