<?php
/**
 * أداة التحسين الشاملة للموقع
 * تجمع جميع أدوات التحسين في مكان واحد
 */

require_once 'config/config.php';
require_once 'config/database.php';

$action = $_GET['action'] ?? 'dashboard';

?>
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 أداة تحسين الموقع - مهرجان وسائل التواصل الاجتماعي</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        h1 {
            text-align: center;
            color: #d4af37;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .card {
            background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(212, 175, 55, 0.2);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            border-color: #d4af37;
            box-shadow: 0 15px 30px rgba(212, 175, 55, 0.2);
        }
        .card h3 {
            color: #d4af37;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        .card p {
            margin-bottom: 20px;
            opacity: 0.9;
            line-height: 1.6;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #d4af37, #f1e5ac);
            color: #000;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #666, #888);
            color: #fff;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-item {
            background: rgba(212, 175, 55, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #d4af37;
            display: block;
        }
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }
        .progress-bar {
            background: rgba(255, 255, 255, 0.1);
            height: 10px;
            border-radius: 5px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            background: linear-gradient(90deg, #d4af37, #f1e5ac);
            height: 100%;
            transition: width 0.3s ease;
        }
        .back-btn {
            margin-bottom: 20px;
        }
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .alert-info {
            background: rgba(52, 144, 220, 0.2);
            border: 1px solid rgba(52, 144, 220, 0.5);
            color: #74c0fc;
        }
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
            color: #51cf66;
        }
        .alert-warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            color: #ffd43b;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if ($action === 'dashboard'): ?>
            <h1>🚀 لوحة تحسين الموقع</h1>
            
            <div class="alert alert-info">
                <strong>ℹ️ مرحباً!</strong> هذه الأداة تساعدك في تحسين أداء موقعك بشكل شامل. اختر الأداة المناسبة من الأسفل.
            </div>
            
            <?php displayWebsiteStats(); ?>
            
            <div class="dashboard">
                <div class="card">
                    <h3>🖼️ تحسين الصور</h3>
                    <p>ضغط وتحسين جميع الصور في الموقع، إنشاء نسخ WebP، وتقليل أحجام الملفات.</p>
                    <a href="?action=optimize-images" class="btn">تحسين الصور</a>
                </div>
                
                <div class="card">
                    <h3>🗄️ تحسين قاعدة البيانات</h3>
                    <p>تحسين جداول قاعدة البيانات، إضافة فهارس، وتنظيف البيانات القديمة.</p>
                    <a href="?action=optimize-database" class="btn">تحسين قاعدة البيانات</a>
                </div>
                
                <div class="card">
                    <h3>⚡ تحسين الأداء</h3>
                    <p>تفعيل الكاش، ضغط الملفات، وتحسين سرعة التحميل.</p>
                    <a href="?action=performance-test" class="btn">اختبار الأداء</a>
                </div>
                
                <div class="card">
                    <h3>🧹 تنظيف الملفات</h3>
                    <p>حذف الملفات المؤقتة، الصور غير المستخدمة، والملفات المكررة.</p>
                    <a href="?action=clean-files" class="btn">تنظيف الملفات</a>
                </div>
                
                <div class="card">
                    <h3>🔒 فحص الأمان</h3>
                    <p>فحص الثغرات الأمنية، تحديث كلمات المرور، وتحسين الحماية.</p>
                    <a href="?action=security-check" class="btn">فحص الأمان</a>
                </div>
                
                <div class="card">
                    <h3>📊 تقرير شامل</h3>
                    <p>تقرير مفصل عن حالة الموقع وتوصيات التحسين.</p>
                    <a href="?action=full-report" class="btn">إنشاء تقرير</a>
                </div>
            </div>
            
            <div class="alert alert-warning">
                <strong>⚠️ تنبيه:</strong> تأكد من عمل نسخة احتياطية من الموقع وقاعدة البيانات قبل تشغيل أدوات التحسين.
            </div>
            
        <?php elseif ($action === 'optimize-images'): ?>
            <div class="back-btn">
                <a href="?action=dashboard" class="btn btn-secondary">← العودة للوحة التحكم</a>
            </div>
            <?php include 'optimize-images.php'; ?>
            
        <?php elseif ($action === 'optimize-database'): ?>
            <div class="back-btn">
                <a href="?action=dashboard" class="btn btn-secondary">← العودة للوحة التحكم</a>
            </div>
            <?php include 'optimize-database.php'; ?>
            
        <?php elseif ($action === 'performance-test'): ?>
            <div class="back-btn">
                <a href="?action=dashboard" class="btn btn-secondary">← العودة للوحة التحكم</a>
            </div>
            <?php performanceTest(); ?>
            
        <?php elseif ($action === 'clean-files'): ?>
            <div class="back-btn">
                <a href="?action=dashboard" class="btn btn-secondary">← العودة للوحة التحكم</a>
            </div>
            <?php cleanFiles(); ?>
            
        <?php elseif ($action === 'security-check'): ?>
            <div class="back-btn">
                <a href="?action=dashboard" class="btn btn-secondary">← العودة للوحة التحكم</a>
            </div>
            <?php securityCheck(); ?>
            
        <?php elseif ($action === 'full-report'): ?>
            <div class="back-btn">
                <a href="?action=dashboard" class="btn btn-secondary">← العودة للوحة التحكم</a>
            </div>
            <?php generateFullReport(); ?>
            
        <?php endif; ?>
    </div>
</body>
</html>

<?php
/**
 * عرض إحصائيات الموقع
 */
function displayWebsiteStats() {
    global $conn;
    
    // إحصائيات قاعدة البيانات
    $users_count = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $votes_count = $conn->query("SELECT COUNT(*) as count FROM votes")->fetch_assoc()['count'];
    $nominees_count = $conn->query("SELECT COUNT(*) as count FROM nominees")->fetch_assoc()['count'];
    
    // حجم الملفات
    $uploads_size = getDirSize('assets/uploads/');
    $total_size = getDirSize('./');
    
    echo '<div class="stats">';
    echo '<div class="stat-item">';
    echo '<span class="stat-number">' . number_format($users_count) . '</span>';
    echo '<div class="stat-label">المستخدمين</div>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-number">' . number_format($votes_count) . '</span>';
    echo '<div class="stat-label">الأصوات</div>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-number">' . number_format($nominees_count) . '</span>';
    echo '<div class="stat-label">المرشحين</div>';
    echo '</div>';
    
    echo '<div class="stat-item">';
    echo '<span class="stat-number">' . formatBytes($uploads_size) . '</span>';
    echo '<div class="stat-label">حجم الملفات</div>';
    echo '</div>';
    echo '</div>';
}

/**
 * حساب حجم المجلد
 */
function getDirSize($dir) {
    $size = 0;
    if (is_dir($dir)) {
        $files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
        foreach ($files as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }
    }
    return $size;
}

/**
 * تنسيق حجم الملف
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB'];
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * اختبار الأداء
 */
function performanceTest() {
    echo "<h2>⚡ اختبار أداء الموقع</h2>";
    
    $start_time = microtime(true);
    
    // اختبار سرعة قاعدة البيانات
    echo "<h3>🗄️ اختبار قاعدة البيانات</h3>";
    testDatabasePerformance();
    
    // اختبار سرعة الملفات
    echo "<h3>📁 اختبار الملفات</h3>";
    testFilePerformance();
    
    // اختبار الذاكرة
    echo "<h3>💾 اختبار الذاكرة</h3>";
    testMemoryUsage();
    
    $end_time = microtime(true);
    $total_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "<div class='alert alert-success'>";
    echo "<strong>✅ اكتمل الاختبار!</strong> إجمالي الوقت: {$total_time}ms";
    echo "</div>";
}

function testDatabasePerformance() {
    global $conn;
    
    $start = microtime(true);
    $conn->query("SELECT COUNT(*) FROM users");
    $db_time = round((microtime(true) - $start) * 1000, 2);
    
    echo "<p>⏱️ وقت استعلام قاعدة البيانات: {$db_time}ms</p>";
    
    if ($db_time < 50) {
        echo "<p>✅ أداء ممتاز</p>";
    } elseif ($db_time < 100) {
        echo "<p>⚠️ أداء جيد</p>";
    } else {
        echo "<p>❌ أداء بطيء - يحتاج تحسين</p>";
    }
}

function testFilePerformance() {
    $start = microtime(true);
    file_get_contents('assets/css/main.css');
    $file_time = round((microtime(true) - $start) * 1000, 2);
    
    echo "<p>⏱️ وقت قراءة الملفات: {$file_time}ms</p>";
    
    if ($file_time < 10) {
        echo "<p>✅ أداء ممتاز</p>";
    } elseif ($file_time < 50) {
        echo "<p>⚠️ أداء جيد</p>";
    } else {
        echo "<p>❌ أداء بطيء - يحتاج تحسين</p>";
    }
}

function testMemoryUsage() {
    $memory_usage = memory_get_usage(true);
    $memory_peak = memory_get_peak_usage(true);
    
    echo "<p>💾 استخدام الذاكرة الحالي: " . formatBytes($memory_usage) . "</p>";
    echo "<p>📈 ذروة استخدام الذاكرة: " . formatBytes($memory_peak) . "</p>";
    
    if ($memory_usage < 32 * 1024 * 1024) { // 32MB
        echo "<p>✅ استخدام ذاكرة ممتاز</p>";
    } elseif ($memory_usage < 64 * 1024 * 1024) { // 64MB
        echo "<p>⚠️ استخدام ذاكرة جيد</p>";
    } else {
        echo "<p>❌ استخدام ذاكرة عالي</p>";
    }
}

function cleanFiles() {
    echo "<h2>🧹 تنظيف الملفات</h2>";
    echo "<p>جاري البحث عن الملفات غير المرغوب فيها...</p>";
    
    // تنظيف ملفات الكاش
    $cache_files = glob('cache/*.cache');
    foreach ($cache_files as $file) {
        unlink($file);
    }
    echo "<p>✅ تم حذف " . count($cache_files) . " ملف كاش</p>";
    
    // تنظيف ملفات الـ logs القديمة
    $log_files = glob('logs/*.log');
    $deleted_logs = 0;
    foreach ($log_files as $file) {
        if (filemtime($file) < strtotime('-7 days')) {
            unlink($file);
            $deleted_logs++;
        }
    }
    echo "<p>✅ تم حذف $deleted_logs ملف log قديم</p>";
    
    echo "<div class='alert alert-success'>🎉 تم تنظيف الملفات بنجاح!</div>";
}

function securityCheck() {
    echo "<h2>🔒 فحص الأمان</h2>";
    
    $issues = [];
    
    // فحص صلاحيات الملفات
    if (is_writable('.htaccess')) {
        $issues[] = "ملف .htaccess قابل للكتابة";
    }
    
    // فحص ملفات الإعدادات
    if (is_readable('includes/config.php')) {
        echo "<p>✅ ملف الإعدادات محمي</p>";
    } else {
        $issues[] = "ملف الإعدادات غير محمي";
    }
    
    // فحص كلمات المرور الضعيفة
    global $conn;
    $weak_passwords = $conn->query("SELECT COUNT(*) as count FROM users WHERE password = MD5('123456') OR password = MD5('password')")->fetch_assoc()['count'];
    
    if ($weak_passwords > 0) {
        $issues[] = "$weak_passwords مستخدم لديه كلمة مرور ضعيفة";
    }
    
    if (empty($issues)) {
        echo "<div class='alert alert-success'>🛡️ لا توجد مشاكل أمنية واضحة</div>";
    } else {
        echo "<div class='alert alert-warning'>";
        echo "<strong>⚠️ تم العثور على مشاكل أمنية:</strong><ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul></div>";
    }
}

function generateFullReport() {
    echo "<h2>📊 التقرير الشامل</h2>";
    
    echo "<h3>📈 إحصائيات عامة</h3>";
    displayWebsiteStats();
    
    echo "<h3>⚡ أداء الموقع</h3>";
    performanceTest();
    
    echo "<h3>🔒 الأمان</h3>";
    securityCheck();
    
    echo "<div class='alert alert-info'>";
    echo "<strong>📋 ملخص التوصيات:</strong><br>";
    echo "• قم بتحسين الصور لتقليل أحجامها<br>";
    echo "• فعل الكاش لتحسين السرعة<br>";
    echo "• راجع إعدادات الأمان بانتظام<br>";
    echo "• احتفظ بنسخ احتياطية دورية";
    echo "</div>";
}
?>
