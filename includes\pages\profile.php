<?php
/**
 * User Profile Page
 */

// Check if user is logged in
if (!is_logged_in()) {
    // Redirect to login page
    $_SESSION['error'] = get_content('profile', 'login_required', 'You must be logged in to view your profile');
    header('Location: ' . base_url('?page=login&redirect=profile'));
    exit;
}

// Add debug information
error_log("Profile page - Session data: " . print_r($_SESSION, true));

// Get user ID from session
$user_id = $_SESSION['user_id'];

// Get user data from database
$user_sql = "SELECT * FROM users WHERE id = ?";
$user_stmt = $conn->prepare($user_sql);
$user_stmt->bind_param("i", $user_id);
$user_stmt->execute();
$user_result = $user_stmt->get_result();

if ($user_result->num_rows === 0) {
    // User not found
    $_SESSION['error'] = get_content('profile', 'user_not_found', 'User not found');
    header('Location: ' . base_url('includes/logout.php'));
    exit;
}

$user = $user_result->fetch_assoc();

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        // Update profile information
        $full_name = trim($_POST['full_name']);
        $username = trim($_POST['username']);
        $email = trim($_POST['email']);

        // Validate input
        if (empty($full_name) || empty($username) || empty($email)) {
            $error_message = get_content('profile', 'fields_required', 'All fields are required');
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = get_content('profile', 'invalid_email', 'Please enter a valid email address');
        } else {
            // Check if username or email already exists (excluding current user)
            $check_sql = "SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?";
            $check_stmt = $conn->prepare($check_sql);
            $check_stmt->bind_param("ssi", $username, $email, $user_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $error_message = get_content('profile', 'username_email_exists', 'Username or email already exists');
            } else {
                // Update user data
                $update_sql = "UPDATE users SET full_name = ?, username = ?, email = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("sssi", $full_name, $username, $email, $user_id);

                if ($update_stmt->execute()) {
                    // Update session data
                    $_SESSION['full_name'] = $full_name;
                    $_SESSION['user_name'] = $username;
                    $_SESSION['email'] = $email;

                    $success_message = get_content('profile', 'profile_updated', 'Profile updated successfully');

                    // Refresh user data
                    $user_stmt->execute();
                    $user_result = $user_stmt->get_result();
                    $user = $user_result->fetch_assoc();
                } else {
                    $error_message = get_content('profile', 'update_failed', 'Failed to update profile');
                }
            }
        }
    } elseif (isset($_POST['change_password'])) {
        // Change password
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];

        // Validate input
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error_message = get_content('profile', 'password_fields_required', 'All password fields are required');
        } elseif ($new_password !== $confirm_password) {
            $error_message = get_content('profile', 'passwords_dont_match', 'New passwords do not match');
        } elseif (strlen($new_password) < 8) {
            $error_message = get_content('profile', 'password_too_short', 'Password must be at least 8 characters long');
        } else {
            // Verify current password
            if (password_verify($current_password, $user['password'])) {
                // Update password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $update_sql = "UPDATE users SET password = ? WHERE id = ?";
                $update_stmt = $conn->prepare($update_sql);
                $update_stmt->bind_param("si", $hashed_password, $user_id);

                if ($update_stmt->execute()) {
                    $success_message = get_content('profile', 'password_updated', 'Password updated successfully');
                } else {
                    $error_message = get_content('profile', 'password_update_failed', 'Failed to update password');
                }
            } else {
                $error_message = get_content('profile', 'current_password_incorrect', 'Current password is incorrect');
            }
        }
    }
}

// Get user votes
$votes = [];

// Add debug information
error_log("Profile page - User ID: " . $user_id);

// Try to get votes from both tables to ensure we don't miss any
$all_votes = [];

// Check if voting_votes table exists
$check_tables = $conn->query("SHOW TABLES LIKE 'voting_votes'");
$voting_tables_exist = $check_tables->num_rows > 0;
error_log("Profile page - voting_votes table exists: " . ($voting_tables_exist ? 'Yes' : 'No'));

if ($voting_tables_exist) {
    try {
        // Use the new voting system tables
        $votes_sql = "SELECT v.*, n.name, n.image, s.name as subcategory_name, c.name as category_name
                    FROM voting_votes v
                    JOIN voting_nominees n ON v.nominee_id = n.id
                    JOIN voting_subcategories s ON n.subcategory_id = s.id
                    JOIN voting_categories c ON s.category_id = c.id
                    WHERE v.user_id = ?
                    ORDER BY v.created_at DESC";
        $votes_stmt = $conn->prepare($votes_sql);
        $votes_stmt->bind_param("i", $user_id);
        $votes_stmt->execute();
        $votes_result = $votes_stmt->get_result();

        error_log("Profile page - Votes query executed. Number of votes found: " . $votes_result->num_rows);

        while ($vote = $votes_result->fetch_assoc()) {
            $all_votes[] = [
                'id' => $vote['id'],
                'nominee_id' => $vote['nominee_id'],
                'name' => $vote['name'],
                'image' => $vote['image'],
                'subcategory_name' => $vote['subcategory_name'],
                'category_name' => $vote['category_name'],
                'created_at' => $vote['created_at'],
                'source' => 'new'
            ];
            error_log("Profile page - Vote found (new): Nominee ID " . $vote['nominee_id'] . ", Name: " . $vote['name']);
        }
    } catch (Exception $e) {
        error_log("Profile page - Error getting votes from new table: " . $e->getMessage());
    }
}

// Check if old votes table exists
$check_old_tables = $conn->query("SHOW TABLES LIKE 'votes'");
$old_tables_exist = $check_old_tables->num_rows > 0;
error_log("Profile page - votes table exists: " . ($old_tables_exist ? 'Yes' : 'No'));

if ($old_tables_exist) {
    try {
        // Use the old voting system tables
        $votes_sql = "SELECT v.*, n.name, n.image, n.category
                    FROM votes v
                    JOIN nominees n ON v.nominee_id = n.id
                    WHERE v.user_id = ?
                    ORDER BY v.created_at DESC";
        $votes_stmt = $conn->prepare($votes_sql);
        $votes_stmt->bind_param("i", $user_id);
        $votes_stmt->execute();
        $votes_result = $votes_stmt->get_result();

        error_log("Profile page - Old votes query executed. Number of votes found: " . $votes_result->num_rows);

        while ($vote = $votes_result->fetch_assoc()) {
            $all_votes[] = [
                'id' => $vote['id'],
                'nominee_id' => $vote['nominee_id'],
                'name' => $vote['name'],
                'image' => $vote['image'],
                'subcategory_name' => $vote['category'],
                'category_name' => '',
                'created_at' => $vote['created_at'],
                'source' => 'old'
            ];
            error_log("Profile page - Old vote found: Nominee ID " . $vote['nominee_id'] . ", Name: " . $vote['name']);
        }
    } catch (Exception $e) {
        error_log("Profile page - Error getting votes from old table: " . $e->getMessage());
    }
}

// Combine votes from both tables
$votes = $all_votes;

error_log("Profile page - Total votes found: " . count($votes));

// Get active tab
$active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'profile';
?>

<section class="profile-section">
    <div class="container">
        <div class="profile-header">
            <h1 class="profile-title"><?php echo get_content('profile', 'my_profile', 'My Profile'); ?></h1>
            <p class="profile-description"><?php echo get_content('profile', 'profile_description', 'Manage your account settings and view your voting history.'); ?></p>
        </div>

        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <div class="profile-container">
            <div class="profile-sidebar">
                <div class="profile-avatar">
                    <?php if (!empty($user['avatar'])): ?>
                    <img src="<?php echo asset_url('uploads/' . $user['avatar']); ?>" alt="<?php echo htmlspecialchars($user['full_name']); ?>">
                    <?php else: ?>
                    <div class="profile-avatar-placeholder">
                        <i class="fas fa-user"></i>
                    </div>
                    <?php endif; ?>
                </div>

                <h2 class="profile-name"><?php echo htmlspecialchars($user['full_name']); ?></h2>
                <p class="profile-username">@<?php echo htmlspecialchars($user['username']); ?></p>

                <div class="profile-stats">
                    <div class="stat-item">
                        <div class="stat-value"><?php echo count($votes); ?></div>
                        <div class="stat-label"><?php echo get_content('profile', 'votes', 'Votes'); ?></div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value"><?php echo date('d M Y', strtotime($user['created_at'])); ?></div>
                        <div class="stat-label"><?php echo get_content('profile', 'joined', 'Joined'); ?></div>
                    </div>
                </div>

                <div class="profile-nav">
                    <a href="?page=profile&tab=profile" class="profile-nav-item <?php echo $active_tab === 'profile' ? 'active' : ''; ?>">
                        <i class="fas fa-user"></i> <?php echo get_content('profile', 'profile_info', 'Profile Information'); ?>
                    </a>
                    <a href="?page=profile&tab=password" class="profile-nav-item <?php echo $active_tab === 'password' ? 'active' : ''; ?>">
                        <i class="fas fa-lock"></i> <?php echo get_content('profile', 'change_password', 'Change Password'); ?>
                    </a>
                    <a href="?page=profile&tab=votes" class="profile-nav-item <?php echo $active_tab === 'votes' ? 'active' : ''; ?>">
                        <i class="fas fa-vote-yea"></i> <?php echo get_content('profile', 'my_votes', 'My Votes'); ?>
                    </a>
                </div>
            </div>

            <div class="profile-content">
                <?php if ($active_tab === 'profile'): ?>
                <h3 class="profile-section-title"><?php echo get_content('profile', 'profile_info', 'Profile Information'); ?></h3>

                <form action="" method="post">
                    <div class="profile-form-group">
                        <label for="full_name" class="profile-form-label"><?php echo get_content('profile', 'full_name', 'Full Name'); ?></label>
                        <input type="text" id="full_name" name="full_name" class="profile-form-input" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                    </div>

                    <div class="profile-form-group">
                        <label for="username" class="profile-form-label"><?php echo get_content('profile', 'username', 'Username'); ?></label>
                        <input type="text" id="username" name="username" class="profile-form-input" value="<?php echo htmlspecialchars($user['username']); ?>" required>
                    </div>

                    <div class="profile-form-group">
                        <label for="email" class="profile-form-label"><?php echo get_content('profile', 'email', 'Email Address'); ?></label>
                        <input type="email" id="email" name="email" class="profile-form-input" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                    </div>

                    <button type="submit" name="update_profile" class="profile-form-submit">
                        <i class="fas fa-save"></i> <?php echo get_content('profile', 'save_changes', 'Save Changes'); ?>
                    </button>
                </form>

                <?php elseif ($active_tab === 'password'): ?>
                <h3 class="profile-section-title"><?php echo get_content('profile', 'change_password', 'Change Password'); ?></h3>

                <form action="" method="post">
                    <div class="profile-form-group">
                        <label for="current_password" class="profile-form-label"><?php echo get_content('profile', 'current_password', 'Current Password'); ?></label>
                        <input type="password" id="current_password" name="current_password" class="profile-form-input" required>
                    </div>

                    <div class="profile-form-group">
                        <label for="new_password" class="profile-form-label"><?php echo get_content('profile', 'new_password', 'New Password'); ?></label>
                        <input type="password" id="new_password" name="new_password" class="profile-form-input" required>
                    </div>

                    <div class="profile-form-group">
                        <label for="confirm_password" class="profile-form-label"><?php echo get_content('profile', 'confirm_password', 'Confirm New Password'); ?></label>
                        <input type="password" id="confirm_password" name="confirm_password" class="profile-form-input" required>
                    </div>

                    <button type="submit" name="change_password" class="profile-form-submit">
                        <i class="fas fa-key"></i> <?php echo get_content('profile', 'update_password', 'Update Password'); ?>
                    </button>
                </form>

                <?php elseif ($active_tab === 'votes'): ?>
                <h3 class="profile-section-title"><?php echo get_content('profile', 'my_votes', 'My Votes'); ?></h3>

                <?php if (empty($votes)): ?>
                <p><?php echo get_content('profile', 'no_votes', 'You have not voted for any nominees yet.'); ?></p>
                <?php else: ?>
                <div class="votes-list">
                    <?php
                    // Debug information
                    error_log("Profile page - Rendering votes list. Number of votes: " . count($votes));

                    if (count($votes) > 0):
                        foreach ($votes as $vote):
                            // Debug information for each vote
                            error_log("Profile page - Rendering vote: " . print_r($vote, true));

                            // Skip votes with missing data
                            if (empty($vote['name'])) {
                                error_log("Profile page - Skipping vote with empty name: " . print_r($vote, true));
                                continue;
                            }
                    ?>
                    <div class="vote-card">
                        <div class="vote-image">
                            <?php if (!empty($vote['image']) && file_exists('assets/uploads/' . $vote['image'])): ?>
                            <img src="<?php echo asset_url('uploads/' . $vote['image']); ?>" alt="<?php echo htmlspecialchars($vote['name']); ?>">
                            <?php else: ?>
                            <div class="image-placeholder">
                                <i class="fas fa-user"></i>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="vote-info">
                            <h4 class="vote-name"><?php echo htmlspecialchars($vote['name']); ?></h4>
                            <div class="vote-category">
                                <?php
                                if (!empty($vote['category_name']) && !empty($vote['subcategory_name'])) {
                                    echo htmlspecialchars($vote['category_name'] . ' / ' . $vote['subcategory_name']);
                                } elseif (!empty($vote['subcategory_name'])) {
                                    echo htmlspecialchars($vote['subcategory_name']);
                                } else {
                                    echo get_content('profile', 'no_category', 'No Category');
                                }
                                ?>
                            </div>
                            <div class="vote-date"><?php echo date('d M Y', strtotime($vote['created_at'])); ?></div>

                            <?php if (isset($vote['source'])): ?>
                            <div class="vote-source" style="display: none;"><?php echo $vote['source']; ?></div>
                            <?php endif; ?>

                            <?php if (isset($vote['nominee_id'])): ?>
                            <div class="vote-actions">
                                <a href="?page=nominee&id=<?php echo $vote['nominee_id']; ?>" class="btn-view-nominee">
                                    <i class="fas fa-eye"></i> <?php echo get_content('profile', 'view_profile', 'View'); ?>
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php
                        endforeach;
                    else:
                    ?>
                    <div class="no-votes-message">
                        <i class="fas fa-info-circle"></i>
                        <p><?php echo get_content('profile', 'no_votes', 'You have not voted for any nominees yet.'); ?></p>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animations
    gsap.from('.profile-title', {
        y: -50,
        opacity: 0,
        duration: 0.8,
        ease: 'power3.out'
    });

    gsap.from('.profile-description', {
        y: -30,
        opacity: 0,
        duration: 0.8,
        delay: 0.2,
        ease: 'power3.out'
    });

    gsap.from('.profile-sidebar', {
        x: -50,
        opacity: 0,
        duration: 0.8,
        delay: 0.4,
        ease: 'power3.out'
    });

    gsap.from('.profile-content', {
        x: 50,
        opacity: 0,
        duration: 0.8,
        delay: 0.4,
        ease: 'power3.out'
    });

    gsap.from('.vote-card', {
        y: 50,
       
        duration: 0.6,
        stagger: 0.1,
        delay: 0.6,
        ease: 'power3.out'
    });
});
</script>
