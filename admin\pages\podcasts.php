<?php
/**
 * Admin Podcasts Management Page
 */

// Check if user is logged in and is admin
if (!is_logged_in() || !is_admin()) {
    header('Location: login.php');
    exit();
}

$message = '';
$message_type = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'add') {
        $title = trim($_POST['title'] ?? '');
        $title_ar = trim($_POST['title_ar'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $description_ar = trim($_POST['description_ar'] ?? '');
        $featured = isset($_POST['featured']) ? 1 : 0;
        $status = $_POST['status'] ?? 'active';

        // Handle file uploads
        $audio_file = '';
        $thumbnail = '';

        // Upload audio file
        if (isset($_FILES['audio_file']) && $_FILES['audio_file']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../assets/uploads/';
            $audio_file = 'podcast_' . time() . '_' . basename($_FILES['audio_file']['name']);
            $audio_path = $upload_dir . $audio_file;

            if (move_uploaded_file($_FILES['audio_file']['tmp_name'], $audio_path)) {
                // Audio uploaded successfully
            } else {
                $message = 'خطأ في رفع ملف الصوت.';
                $message_type = 'danger';
            }
        }

        // Upload thumbnail
        if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../assets/uploads/';
            $thumbnail = 'podcast_thumb_' . time() . '_' . basename($_FILES['thumbnail']['name']);
            $thumb_path = $upload_dir . $thumbnail;

            if (move_uploaded_file($_FILES['thumbnail']['tmp_name'], $thumb_path)) {
                // Thumbnail uploaded successfully
            } else {
                $message = 'خطأ في رفع صورة المعاينة.';
                $message_type = 'danger';
            }
        }

        if (!empty($title) && !empty($title_ar) && !empty($audio_file)) {
            $sql = "INSERT INTO podcasts (title, title_ar, description, description_ar, audio_file, thumbnail, featured, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ssssssss', $title, $title_ar, $description, $description_ar, $audio_file, $thumbnail, $featured, $status);

            if ($stmt->execute()) {
                $message = 'تمت إضافة البودكاست بنجاح.';
                $message_type = 'success';
            } else {
                $message = 'حدث خطأ أثناء إضافة البودكاست: ' . $stmt->error;
                $message_type = 'danger';
            }
        } else {
            $message = 'يرجى ملء الحقول المطلوبة (العنوان بالإنجليزية والعربية وملف الصوت).';
            $message_type = 'danger';
        }
    }

    elseif ($action === 'edit') {
        $id = intval($_POST['id'] ?? 0);
        $title = trim($_POST['title'] ?? '');
        $title_ar = trim($_POST['title_ar'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $description_ar = trim($_POST['description_ar'] ?? '');
        $featured = isset($_POST['featured']) ? 1 : 0;
        $status = $_POST['status'] ?? 'active';

        if ($id > 0 && !empty($title) && !empty($title_ar)) {
            // Get current podcast data
            $current_sql = "SELECT audio_file, thumbnail FROM podcasts WHERE id = ?";
            $current_stmt = $conn->prepare($current_sql);
            $current_stmt->bind_param('i', $id);
            $current_stmt->execute();
            $current_result = $current_stmt->get_result();
            $current_data = $current_result->fetch_assoc();

            $audio_file = $current_data['audio_file'];
            $thumbnail = $current_data['thumbnail'];

            // Handle new audio file upload
            if (isset($_FILES['audio_file']) && $_FILES['audio_file']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../assets/uploads/';
                $new_audio_file = 'podcast_' . time() . '_' . basename($_FILES['audio_file']['name']);
                $audio_path = $upload_dir . $new_audio_file;

                if (move_uploaded_file($_FILES['audio_file']['tmp_name'], $audio_path)) {
                    // Delete old audio file
                    if (!empty($audio_file) && file_exists($upload_dir . $audio_file)) {
                        unlink($upload_dir . $audio_file);
                    }
                    $audio_file = $new_audio_file;
                }
            }

            // Handle new thumbnail upload
            if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = '../assets/uploads/';
                $new_thumbnail = 'podcast_thumb_' . time() . '_' . basename($_FILES['thumbnail']['name']);
                $thumb_path = $upload_dir . $new_thumbnail;

                if (move_uploaded_file($_FILES['thumbnail']['tmp_name'], $thumb_path)) {
                    // Delete old thumbnail
                    if (!empty($thumbnail) && file_exists($upload_dir . $thumbnail)) {
                        unlink($upload_dir . $thumbnail);
                    }
                    $thumbnail = $new_thumbnail;
                }
            }

            $sql = "UPDATE podcasts SET title = ?, title_ar = ?, description = ?, description_ar = ?, audio_file = ?, thumbnail = ?, featured = ?, status = ? WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ssssssssi', $title, $title_ar, $description, $description_ar, $audio_file, $thumbnail, $featured, $status, $id);

            if ($stmt->execute()) {
                $message = 'تم تحديث البودكاست بنجاح.';
                $message_type = 'success';
            } else {
                $message = 'حدث خطأ أثناء تحديث البودكاست: ' . $stmt->error;
                $message_type = 'danger';
            }
        } else {
            $message = 'يرجى ملء الحقول المطلوبة.';
            $message_type = 'danger';
        }
    }

    elseif ($action === 'delete') {
        $id = intval($_POST['id'] ?? 0);

        if ($id > 0) {
            // Get podcast data to delete files
            $podcast_sql = "SELECT audio_file, thumbnail FROM podcasts WHERE id = ?";
            $podcast_stmt = $conn->prepare($podcast_sql);
            $podcast_stmt->bind_param('i', $id);
            $podcast_stmt->execute();
            $podcast_result = $podcast_stmt->get_result();
            $podcast_data = $podcast_result->fetch_assoc();

            $sql = "DELETE FROM podcasts WHERE id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('i', $id);

            if ($stmt->execute()) {
                // Delete associated files
                $upload_dir = '../assets/uploads/';
                if (!empty($podcast_data['audio_file']) && file_exists($upload_dir . $podcast_data['audio_file'])) {
                    unlink($upload_dir . $podcast_data['audio_file']);
                }
                if (!empty($podcast_data['thumbnail']) && file_exists($upload_dir . $podcast_data['thumbnail'])) {
                    unlink($upload_dir . $podcast_data['thumbnail']);
                }

                $message = 'تم حذف البودكاست بنجاح.';
                $message_type = 'success';
            } else {
                $message = 'حدث خطأ أثناء حذف البودكاست: ' . $stmt->error;
                $message_type = 'danger';
            }
        }
    }
}

// Get all podcasts
$podcasts_sql = "SELECT id, title, title_ar, description, description_ar, audio_file, thumbnail, featured, status, created_at FROM podcasts ORDER BY created_at DESC";
$podcasts_result = $conn->query($podcasts_sql);
$podcasts = [];

if ($podcasts_result && $podcasts_result->num_rows > 0) {
    while ($row = $podcasts_result->fetch_assoc()) {
        $podcasts[] = $row;
    }
}
?>

<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-microphone"></i> إدارة البودكاست</h1>
        <p>إدارة حلقات البودكاست والمحتوى الصوتي</p>
    </div>
    <div class="page-actions">
        <button class="btn btn-primary" onclick="openAddModal()">
            <i class="fas fa-plus"></i> إضافة بودكاست جديد
        </button>
    </div>
</div>

<?php if (!empty($message)): ?>
<div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
    <?php echo $message; ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
<?php endif; ?>

<div class="content-wrapper">
    <div class="podcasts-container">
        <?php if (!empty($podcasts)): ?>
            <div class="podcasts-grid">
                <?php foreach ($podcasts as $podcast): ?>
                <div class="podcast-card">
                    <div class="card-header">
                        <div class="podcast-thumbnail">
                            <?php if (!empty($podcast['thumbnail'])): ?>
                            <img src="../assets/uploads/<?php echo $podcast['thumbnail']; ?>" alt="<?php echo htmlspecialchars($podcast['title']); ?>">
                            <?php else: ?>
                            <div class="default-thumbnail">
                                <i class="fas fa-microphone"></i>
                            </div>
                            <?php endif; ?>

                            <?php if ($podcast['featured']): ?>
                            <div class="featured-badge">
                                <i class="fas fa-star"></i>
                            </div>
                            <?php endif; ?>

                            <div class="episode-badge">
                                <span>بودكاست</span>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="podcast-titles">
                            <h3 class="title-en"><?php echo htmlspecialchars($podcast['title']); ?></h3>
                            <h4 class="title-ar"><?php echo htmlspecialchars($podcast['title_ar']); ?></h4>
                        </div>

                        <div class="podcast-meta">
                            <div class="meta-row">
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span><?php echo date('Y/m/d', strtotime($podcast['created_at'])); ?></span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-microphone"></i>
                                    <span>بودكاست صوتي</span>
                                </div>
                            </div>

                            <?php if (!empty($podcast['description'])): ?>
                            <div class="meta-row">
                                <div class="podcast-description">
                                    <p><?php echo htmlspecialchars(substr($podcast['description_ar'], 0, 100)) . '...'; ?></p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="podcast-status">
                            <span class="status-badge status-<?php echo $podcast['status']; ?>">
                                <?php echo $podcast['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="action-buttons">
                            <button class="btn btn-primary btn-sm edit-podcast"
                                    data-id="<?php echo $podcast['id']; ?>"
                                    onclick="openEditModal(<?php echo $podcast['id']; ?>)">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-danger btn-sm delete-podcast"
                                    data-id="<?php echo $podcast['id']; ?>"
                                    data-title="<?php echo htmlspecialchars($podcast['title']); ?>"
                                    onclick="deletePodcast(<?php echo $podcast['id']; ?>, '<?php echo htmlspecialchars($podcast['title']); ?>')">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-microphone"></i>
            </div>
            <h3>لا توجد حلقات بودكاست</h3>
            <p>ابدأ بإضافة أول حلقة بودكاست لمهرجانك</p>
            <button class="btn btn-primary" onclick="openAddModal()">
                <i class="fas fa-plus"></i> إضافة أول حلقة
            </button>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- Add Podcast Modal -->
<div class="modal fade" id="addPodcastModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle"></i> إضافة بودكاست جديد
                </h5>
                <button type="button" class="btn-close" onclick="closeAddModal()"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="addPodcastForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العنوان (إنجليزي) *</label>
                                <input type="text" class="form-control" name="title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العنوان (عربي) *</label>
                                <input type="text" class="form-control" name="title_ar" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الوصف (إنجليزي)</label>
                                <textarea class="form-control" name="description" rows="4" placeholder="وصف البودكاست بالإنجليزية"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الوصف (عربي)</label>
                                <textarea class="form-control" name="description_ar" rows="4" placeholder="وصف البودكاست بالعربية"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">ملف الصوت/الفيديو *</label>
                                <input type="file" class="form-control" name="audio_file" accept="audio/*,video/*" required>
                                <small class="form-text text-muted">يدعم ملفات الصوت والفيديو (MP3, MP4, WAV, etc.)</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">صورة المعاينة</label>
                                <input type="file" class="form-control" name="thumbnail" accept="image/*">
                                <small class="form-text text-muted">صورة تظهر مع البودكاست (اختيارية)</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" name="status">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input type="checkbox" class="form-check-input" name="featured" id="featured">
                                    <label class="form-check-label" for="featured">بودكاست مميز</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة البودكاست</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Podcast Modal -->
<div class="modal fade" id="editPodcastModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل البودكاست</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" enctype="multipart/form-data" id="editPodcastForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="edit_id">

                    <!-- Same form fields as add modal but with edit_ prefix for IDs -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العنوان (إنجليزي) *</label>
                                <input type="text" class="form-control" name="title" id="edit_title" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">العنوان (عربي) *</label>
                                <input type="text" class="form-control" name="title_ar" id="edit_title_ar" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الوصف (إنجليزي)</label>
                                <textarea class="form-control" name="description" id="edit_description" rows="4" placeholder="وصف البودكاست بالإنجليزية"></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الوصف (عربي)</label>
                                <textarea class="form-control" name="description_ar" id="edit_description_ar" rows="4" placeholder="وصف البودكاست بالعربية"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">ملف الصوت/الفيديو (اختياري - لتغيير الملف الحالي)</label>
                                <input type="file" class="form-control" name="audio_file" accept="audio/*,video/*">
                                <small class="form-text text-muted">اتركه فارغاً للاحتفاظ بالملف الحالي</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">صورة المعاينة (اختياري - لتغيير الصورة الحالية)</label>
                                <input type="file" class="form-control" name="thumbnail" accept="image/*">
                                <small class="form-text text-muted">اتركها فارغة للاحتفاظ بالصورة الحالية</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-control" name="status" id="edit_status">
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input type="checkbox" class="form-check-input" name="featured" id="edit_featured">
                                    <label class="form-check-label" for="edit_featured">بودكاست مميز</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Modal functions
function openAddModal() {
    document.getElementById('addPodcastModal').style.display = 'block';
    document.getElementById('addPodcastModal').classList.add('show');
    document.body.classList.add('modal-open');
}

function closeAddModal() {
    document.getElementById('addPodcastModal').style.display = 'none';
    document.getElementById('addPodcastModal').classList.remove('show');
    document.body.classList.remove('modal-open');
    document.getElementById('addPodcastForm').reset();
}

function openEditModal(podcastId) {
    // Here you would fetch podcast data via AJAX and populate the edit form
    document.getElementById('edit_id').value = podcastId;
    document.getElementById('editPodcastModal').style.display = 'block';
    document.getElementById('editPodcastModal').classList.add('show');
    document.body.classList.add('modal-open');
}

function closeEditModal() {
    document.getElementById('editPodcastModal').style.display = 'none';
    document.getElementById('editPodcastModal').classList.remove('show');
    document.body.classList.remove('modal-open');
    document.getElementById('editPodcastForm').reset();
}

function deletePodcast(podcastId, podcastTitle) {
    if (confirm(`هل أنت متأكد من حذف البودكاست "${podcastTitle}"؟\n\nسيتم حذف جميع الملفات المرتبطة بهذه الحلقة نهائياً.`)) {
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${podcastId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Close modal when clicking outside
window.onclick = function(event) {
    const addModal = document.getElementById('addPodcastModal');
    const editModal = document.getElementById('editPodcastModal');

    if (event.target === addModal) {
        closeAddModal();
    }
    if (event.target === editModal) {
        closeEditModal();
    }
}

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const addForm = document.getElementById('addPodcastForm');
    if (addForm) {
        addForm.addEventListener('submit', function(e) {
            const title = this.querySelector('input[name="title"]').value.trim();
            const titleAr = this.querySelector('input[name="title_ar"]').value.trim();
            const audioFile = this.querySelector('input[name="audio_file"]').files[0];

            if (!title || !titleAr || !audioFile) {
                e.preventDefault();
                alert('يرجى ملء الحقول المطلوبة: العنوان بالإنجليزية والعربية وملف الصوت');
                return false;
            }
        });
    }
});
</script>