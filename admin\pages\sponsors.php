<?php
/**
 * Admin Sponsors Management Page
 */

// Process form submission
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $message = 'طلب غير صالح. يرجى المحاولة مرة أخرى.';
        $message_type = 'danger';
    } else {
        // Get form data
        $sponsor_id = isset($_POST['sponsor_id']) ? (int)$_POST['sponsor_id'] : 0;
        $name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $name_ar = isset($_POST['name_ar']) ? trim($_POST['name_ar']) : '';
        $type = isset($_POST['type']) ? trim($_POST['type']) : '';
        $website = isset($_POST['website']) ? trim($_POST['website']) : '';
        $action = isset($_POST['action']) ? $_POST['action'] : '';
        
        // Handle logo upload
        $logo = '';
        
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $logo_tmp = $_FILES['logo']['tmp_name'];
            $logo_name = $_FILES['logo']['name'];
            $logo_ext = pathinfo($logo_name, PATHINFO_EXTENSION);
            $logo = 'sponsor_' . time() . '.' . $logo_ext;
            
            // Create uploads directory if it doesn't exist
            if (!file_exists(UPLOAD_DIR)) {
                mkdir(UPLOAD_DIR, 0777, true);
            }
            
            // Move uploaded file
            if (move_uploaded_file($logo_tmp, UPLOAD_DIR . $logo)) {
                // File uploaded successfully
            } else {
                $message = 'حدث خطأ أثناء تحميل الشعار.';
                $message_type = 'danger';
                $logo = '';
            }
        }
        
        // Process based on action
        if ($action === 'delete') {
            // Delete sponsor
            if ($sponsor_id > 0) {
                // Get sponsor logo to delete
                $get_logo_sql = "SELECT logo FROM sponsors WHERE id = ?";
                $get_logo_stmt = $conn->prepare($get_logo_sql);
                $get_logo_stmt->bind_param('i', $sponsor_id);
                $get_logo_stmt->execute();
                $get_logo_result = $get_logo_stmt->get_result();
                
                if ($get_logo_result && $get_logo_result->num_rows > 0) {
                    $logo_file = $get_logo_result->fetch_assoc()['logo'];
                    
                    // Delete logo file
                    if (!empty($logo_file) && file_exists(UPLOAD_DIR . $logo_file)) {
                        unlink(UPLOAD_DIR . $logo_file);
                    }
                }
                
                // Delete sponsor from database
                $sql = "DELETE FROM sponsors WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('i', $sponsor_id);
                
                if ($stmt->execute()) {
                    $message = 'تم حذف الراعي بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء حذف الراعي: ' . $stmt->error;
                    $message_type = 'danger';
                }
            }
        } elseif ($action === 'edit') {
            // Update sponsor
            if ($sponsor_id > 0 && !empty($name) && !empty($name_ar) && !empty($type)) {
                // Get current sponsor data
                $get_sponsor_sql = "SELECT logo FROM sponsors WHERE id = ?";
                $get_sponsor_stmt = $conn->prepare($get_sponsor_sql);
                $get_sponsor_stmt->bind_param('i', $sponsor_id);
                $get_sponsor_stmt->execute();
                $get_sponsor_result = $get_sponsor_stmt->get_result();
                $current_sponsor = $get_sponsor_result->fetch_assoc();
                
                // Use current logo if no new one uploaded
                if (empty($logo)) {
                    $logo = $current_sponsor['logo'];
                } else {
                    // Delete old logo file
                    if (!empty($current_sponsor['logo']) && file_exists(UPLOAD_DIR . $current_sponsor['logo'])) {
                        unlink(UPLOAD_DIR . $current_sponsor['logo']);
                    }
                }
                
                // Update sponsor in database
                $sql = "UPDATE sponsors SET name = ?, name_ar = ?, type = ?, logo = ?, website = ? WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('sssssi', $name, $name_ar, $type, $logo, $website, $sponsor_id);
                
                if ($stmt->execute()) {
                    $message = 'تم تحديث الراعي بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء تحديث الراعي: ' . $stmt->error;
                    $message_type = 'danger';
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        } elseif ($action === 'add') {
            // Add new sponsor
            if (!empty($name) && !empty($name_ar) && !empty($type) && !empty($logo)) {
                $sql = "INSERT INTO sponsors (name, name_ar, type, logo, website) VALUES (?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param('sssss', $name, $name_ar, $type, $logo, $website);
                
                if ($stmt->execute()) {
                    $message = 'تمت إضافة الراعي بنجاح.';
                    $message_type = 'success';
                } else {
                    $message = 'حدث خطأ أثناء إضافة الراعي: ' . $stmt->error;
                    $message_type = 'danger';
                }
            } else {
                $message = 'يرجى ملء جميع الحقول المطلوبة.';
                $message_type = 'danger';
            }
        }
    }
}

// Get all sponsors
$sponsors_sql = "SELECT * FROM sponsors ORDER BY FIELD(type, 'platinum', 'gold', 'silver'), name";
$sponsors_result = $conn->query($sponsors_sql);
$sponsors = [];

if ($sponsors_result && $sponsors_result->num_rows > 0) {
    while ($row = $sponsors_result->fetch_assoc()) {
        $sponsors[] = $row;
    }
}
?>

<div class="sponsors-management">
    <h1 class="page-title">إدارة الرعاة</h1>
    
    <?php if (!empty($message)): ?>
    <div class="alert alert-<?php echo $message_type; ?>">
        <?php echo $message; ?>
    </div>
    <?php endif; ?>
    
    <!-- Add New Sponsor Button -->
    <div class="action-buttons">
        <button class="btn btn-primary" id="addSponsorBtn">
            <i class="fas fa-plus"></i> إضافة راعي جديد
        </button>
    </div>
    
    <!-- Sponsors List -->
    <div class="card">
        <div class="card-header">
            <h2>قائمة الرعاة</h2>
        </div>
        <div class="card-body">
            <?php if (empty($sponsors)): ?>
            <p class="no-data">لا يوجد رعاة حتى الآن.</p>
            <?php else: ?>
            <div class="table-responsive">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>الشعار</th>
                            <th>الاسم</th>
                            <th>الاسم (عربي)</th>
                            <th>النوع</th>
                            <th>الموقع الإلكتروني</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sponsors as $sponsor): ?>
                        <tr>
                            <td>
                                <?php if (!empty($sponsor['logo']) && file_exists(UPLOAD_DIR . $sponsor['logo'])): ?>
                                <img src="<?php echo asset_url('uploads/' . $sponsor['logo']); ?>" alt="<?php echo htmlspecialchars($sponsor['name']); ?>" class="sponsor-logo">
                                <?php else: ?>
                                <div class="sponsor-logo placeholder">
                                    <i class="fas fa-building"></i>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($sponsor['name']); ?></td>
                            <td><?php echo htmlspecialchars($sponsor['name_ar']); ?></td>
                            <td>
                                <span class="badge badge-<?php echo $sponsor['type']; ?>">
                                    <?php 
                                    switch ($sponsor['type']) {
                                        case 'platinum':
                                            echo 'بلاتيني';
                                            break;
                                        case 'gold':
                                            echo 'ذهبي';
                                            break;
                                        case 'silver':
                                            echo 'فضي';
                                            break;
                                        default:
                                            echo htmlspecialchars($sponsor['type']);
                                    }
                                    ?>
                                </span>
                            </td>
                            <td>
                                <?php if (!empty($sponsor['website'])): ?>
                                <a href="<?php echo htmlspecialchars($sponsor['website']); ?>" target="_blank">
                                    <?php echo htmlspecialchars($sponsor['website']); ?>
                                </a>
                                <?php else: ?>
                                <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary edit-sponsor" 
                                    data-id="<?php echo $sponsor['id']; ?>"
                                    data-name="<?php echo htmlspecialchars($sponsor['name']); ?>"
                                    data-name-ar="<?php echo htmlspecialchars($sponsor['name_ar']); ?>"
                                    data-type="<?php echo htmlspecialchars($sponsor['type']); ?>"
                                    data-website="<?php echo htmlspecialchars($sponsor['website']); ?>"
                                    data-logo="<?php echo htmlspecialchars($sponsor['logo']); ?>">
                                    <i class="fas fa-edit"></i> تحرير
                                </button>
                                <button class="btn btn-sm btn-danger delete-sponsor" 
                                    data-id="<?php echo $sponsor['id']; ?>"
                                    data-name="<?php echo htmlspecialchars($sponsor['name']); ?>">
                                    <i class="fas fa-trash"></i> حذف
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Sponsor Modal -->
<div class="modal" id="addSponsorModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>إضافة راعي جديد</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=sponsors" method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="add">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">الاسم (الإنجليزية)</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="name_ar">الاسم (العربية)</label>
                        <input type="text" id="name_ar" name="name_ar" required dir="rtl">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="type">النوع</label>
                    <select id="type" name="type" required>
                        <option value="">اختر النوع</option>
                        <option value="platinum">بلاتيني</option>
                        <option value="gold">ذهبي</option>
                        <option value="silver">فضي</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="logo">الشعار</label>
                    <input type="file" id="logo" name="logo" accept="image/*" required>
                    <div class="file-preview"></div>
                </div>
                
                <div class="form-group">
                    <label for="website">الموقع الإلكتروني</label>
                    <input type="url" id="website" name="website" placeholder="https://example.com">
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">إضافة راعي</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Sponsor Modal -->
<div class="modal" id="editSponsorModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>تحرير الراعي</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <form action="index.php?page=sponsors" method="post" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="sponsor_id" id="edit_sponsor_id">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="edit_name">الاسم (الإنجليزية)</label>
                        <input type="text" id="edit_name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="edit_name_ar">الاسم (العربية)</label>
                        <input type="text" id="edit_name_ar" name="name_ar" required dir="rtl">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="edit_type">النوع</label>
                    <select id="edit_type" name="type" required>
                        <option value="">اختر النوع</option>
                        <option value="platinum">بلاتيني</option>
                        <option value="gold">ذهبي</option>
                        <option value="silver">فضي</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="edit_logo">الشعار</label>
                    <input type="file" id="edit_logo" name="logo" accept="image/*">
                    <div class="file-preview" id="edit_logo_preview"></div>
                    <small class="form-text">اترك هذا الحقل فارغًا للاحتفاظ بالشعار الحالي.</small>
                </div>
                
                <div class="form-group">
                    <label for="edit_website">الموقع الإلكتروني</label>
                    <input type="url" id="edit_website" name="website" placeholder="https://example.com">
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Sponsor Modal -->
<div class="modal" id="deleteSponsorModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>حذف الراعي</h2>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <p>هل أنت متأكد من أنك تريد حذف الراعي "<span id="delete_sponsor_name"></span>"؟</p>
            <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            
            <form action="index.php?page=sponsors" method="post">
                <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="sponsor_id" id="delete_sponsor_id">
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-danger">حذف</button>
                    <button type="button" class="btn btn-secondary modal-close">إلغاء</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add sponsor modal
    const addSponsorBtn = document.getElementById('addSponsorBtn');
    const addSponsorModal = document.getElementById('addSponsorModal');
    
    if (addSponsorBtn) {
        addSponsorBtn.addEventListener('click', function() {
            addSponsorModal.style.display = 'block';
        });
    }
    
    // Edit sponsor modal
    const editButtons = document.querySelectorAll('.edit-sponsor');
    const editSponsorModal = document.getElementById('editSponsorModal');
    const editSponsorId = document.getElementById('edit_sponsor_id');
    const editName = document.getElementById('edit_name');
    const editNameAr = document.getElementById('edit_name_ar');
    const editType = document.getElementById('edit_type');
    const editWebsite = document.getElementById('edit_website');
    const editLogoPreview = document.getElementById('edit_logo_preview');
    
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const nameAr = this.getAttribute('data-name-ar');
            const type = this.getAttribute('data-type');
            const website = this.getAttribute('data-website');
            const logo = this.getAttribute('data-logo');
            
            editSponsorId.value = id;
            editName.value = name;
            editNameAr.value = nameAr;
            editType.value = type;
            editWebsite.value = website;
            
            // Show logo preview
            editLogoPreview.innerHTML = '';
            if (logo) {
                const img = document.createElement('img');
                img.src = '<?php echo asset_url('uploads/'); ?>' + logo;
                img.alt = name;
                editLogoPreview.appendChild(img);
            }
            
            editSponsorModal.style.display = 'block';
        });
    });
    
    // Delete sponsor modal
    const deleteButtons = document.querySelectorAll('.delete-sponsor');
    const deleteSponsorModal = document.getElementById('deleteSponsorModal');
    const deleteSponsorId = document.getElementById('delete_sponsor_id');
    const deleteSponsorName = document.getElementById('delete_sponsor_name');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            
            deleteSponsorId.value = id;
            deleteSponsorName.textContent = name;
            
            deleteSponsorModal.style.display = 'block';
        });
    });
    
    // Close modals
    const modalCloseButtons = document.querySelectorAll('.modal-close');
    
    modalCloseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            modal.style.display = 'none';
        });
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });
    
    // File input preview
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const previewContainer = this.nextElementSibling;
            
            if (previewContainer && this.files && this.files[0]) {
                const file = this.files[0];
                previewContainer.innerHTML = '';
                
                if (file.type.startsWith('image/')) {
                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.alt = file.name;
                    previewContainer.appendChild(img);
                }
            }
        });
    });
});
</script>
