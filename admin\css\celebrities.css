/* Celebrities Management Styles */
.celebrities-management {
    padding: var(--spacing-md);
}

.action-buttons {
    margin-bottom: var(--spacing-md);
    display: flex;
    justify-content: flex-end;
}

.celebrities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.celebrity-card {
    background-color: var(--color-dark);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    position: relative;
}

.celebrity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.celebrity-card.featured {
    border: 2px solid var(--color-gold);
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--color-gold);
    color: var(--color-black);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.celebrity-image {
    height: 200px;
    position: relative;
    overflow: hidden;
}

.celebrity-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-normal);
}

.celebrity-card:hover .celebrity-image img {
    transform: scale(1.05);
}

.celebrity-image .placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-black);
}

.celebrity-image .placeholder i {
    font-size: 3rem;
    color: var(--color-gray);
}

.celebrity-info {
    padding: var(--spacing-md);
}

.celebrity-info h3 {
    margin: 0 0 var(--spacing-xs);
    font-size: 1.2rem;
    color: var(--color-white);
}

.celebrity-info h4 {
    margin: 0 0 var(--spacing-xs);
    font-size: 1.1rem;
    color: var(--color-white);
    font-weight: normal;
}

.celebrity-info p {
    margin: 0 0 var(--spacing-md);
    font-size: 0.9rem;
    color: var(--color-gray);
}

.celebrity-actions {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-md);
}

/* Form Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: var(--spacing-md);
}

.checkbox-label input[type="checkbox"] {
    margin-right: var(--spacing-xs);
}

/* File Preview */
.file-preview {
    margin-top: var(--spacing-xs);
    max-width: 100%;
    overflow: hidden;
}

.file-preview img {
    max-width: 100%;
    max-height: 150px;
    border-radius: var(--border-radius-sm);
}

.file-preview video {
    max-width: 100%;
    max-height: 150px;
    border-radius: var(--border-radius-sm);
}
