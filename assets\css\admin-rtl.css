/**
 * RTL CSS for Admin Dashboard
 */

/* ===== Base Styles ===== */
body {
    text-align: right;
    direction: rtl;
}

/* ===== Admin Layout ===== */
.admin-sidebar {
    right: 0;
    left: auto;
    border-right: none;
    border-left: 1px solid var(--color-gray);
}

.admin-main {
    margin-left: 0;
    margin-right: var(--sidebar-width);
}

.admin-wrapper.collapsed .admin-main {
    margin-left: 0;
    margin-right: var(--sidebar-collapsed-width);
}

/* ===== Admin Sidebar ===== */
.sidebar-header {
    flex-direction: row-reverse;
}

.sidebar-nav i {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

/* ===== Admin Header ===== */
.header-search input {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.header-search button {
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.header-actions {
    flex-direction: row-reverse;
}

.language-switcher {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

.user-toggle i {
    margin-right: 0;
    margin-left: var(--spacing-xs);
}

.dropdown-menu {
    right: auto;
    left: 0;
}

.dropdown-menu i {
    margin-right: 0;
    margin-left: var(--spacing-sm);
}

/* ===== Forms ===== */
.input-icon i {
    left: auto;
    right: 1rem;
}

.input-icon input {
    padding-left: 0.75rem;
    padding-right: 2.5rem;
}

/* ===== Tables ===== */
.data-table th,
.data-table td {
    text-align: right;
}

/* ===== Dashboard ===== */
.stat-icon {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

/* ===== Content Management ===== */
.content-tab {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

.content-item-actions .btn {
    margin-left: 0;
    margin-right: var(--spacing-xs);
}

/* ===== Settings ===== */
.settings-tabs .tab {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

.info-item {
    flex-direction: row-reverse;
}

/* ===== Modals ===== */
.modal-header {
    flex-direction: row-reverse;
}

/* ===== Media Queries ===== */
@media (max-width: 992px) {
    .admin-sidebar {
        right: 0;
        left: auto;
    }
    
    .admin-main {
        margin-right: var(--sidebar-collapsed-width);
        margin-left: 0;
    }
    
    .admin-sidebar.expanded {
        right: 0;
        left: auto;
    }
}

@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
    }
}
