<?php
/**
 * Login Page
 */

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect to home page
    header('Location: ' . base_url());
    exit;
}

// Get redirect URL if any
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';
$redirect_params = '';
if (isset($_GET['main_category'])) {
    $redirect_params .= '&main_category=' . $_GET['main_category'];
}
if (isset($_GET['subcategory'])) {
    $redirect_params .= '&subcategory=' . $_GET['subcategory'];
}
if (isset($_GET['id'])) {
    $redirect_params .= '&id=' . $_GET['id'];
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']) ? true : false;
    
    // Validate inputs
    $errors = [];
    
    if (empty($email)) {
        $errors['email'] = get_content('login', 'email_required', 'Email is required');
    }
    
    if (empty($password)) {
        $errors['password'] = get_content('login', 'password_required', 'Password is required');
    }
    
    // If no errors, attempt to login
    if (empty($errors)) {
        // Check if user exists
        $sql = "SELECT * FROM users WHERE email = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $user = $result->fetch_assoc();
            
            // Verify password
            if (password_verify($password, $user['password'])) {
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_name'] = $user['username'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['role'] = $user['role'];
                
                // Set remember me cookie if requested
                if ($remember) {
                    $token = bin2hex(random_bytes(32));
                    $expires = time() + (30 * 24 * 60 * 60); // 30 days
                    
                    // Store token in database
                    $sql = "INSERT INTO remember_tokens (user_id, token, expires) VALUES (?, ?, ?)";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("iss", $user['id'], $token, date('Y-m-d H:i:s', $expires));
                    $stmt->execute();
                    
                    // Set cookie
                    setcookie('remember_token', $token, $expires, '/');
                }
                
                // Update last login time
                $sql = "UPDATE users SET last_login = NOW() WHERE id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->bind_param("i", $user['id']);
                $stmt->execute();
                
                // Redirect to appropriate page
                if (!empty($redirect)) {
                    header('Location: ' . base_url('?page=' . $redirect . $redirect_params));
                } else {
                    header('Location: ' . base_url());
                }
                exit;
            } else {
                $errors['login'] = get_content('login', 'invalid_credentials', 'Invalid email or password');
            }
        } else {
            $errors['login'] = get_content('login', 'invalid_credentials', 'Invalid email or password');
        }
    }
}
?>

<!-- Login Page -->
<section class="auth-section">
    <div class="container">
        <div class="auth-container">
            <div class="auth-header">
                <h1><?php echo get_content('login', 'title', 'Login to Your Account'); ?></h1>
                <p><?php echo get_content('login', 'subtitle', 'Welcome back! Please login to access your account.'); ?></p>
            </div>
            
            <?php if (isset($errors['login'])): ?>
            <div class="alert alert-danger">
                <?php echo $errors['login']; ?>
            </div>
            <?php endif; ?>
            
            <div class="auth-social">
                <a href="<?php echo base_url('includes/social-login.php?provider=google'); ?>" class="social-login google">
                    <i class="fab fa-google"></i>
                    <span><?php echo get_content('login', 'google_login', 'Login with Google'); ?></span>
                </a>
                <a href="<?php echo base_url('includes/social-login.php?provider=facebook'); ?>" class="social-login facebook">
                    <i class="fab fa-facebook-f"></i>
                    <span><?php echo get_content('login', 'facebook_login', 'Login with Facebook'); ?></span>
                </a>
            </div>
            
            <div class="auth-divider">
                <span><?php echo get_content('login', 'or', 'OR'); ?></span>
            </div>
            
            <form action="" method="post" class="auth-form">
                <div class="form-group">
                    <label for="email"><?php echo get_content('login', 'email', 'Email Address'); ?></label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-envelope"></i></span>
                        <input type="email" id="email" name="email" class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>" value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" placeholder="<?php echo get_content('login', 'email_placeholder', 'Enter your email'); ?>">
                    </div>
                    <?php if (isset($errors['email'])): ?>
                    <div class="invalid-feedback">
                        <?php echo $errors['email']; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="password"><?php echo get_content('login', 'password', 'Password'); ?></label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                        <input type="password" id="password" name="password" class="form-control <?php echo isset($errors['password']) ? 'is-invalid' : ''; ?>" placeholder="<?php echo get_content('login', 'password_placeholder', 'Enter your password'); ?>">
                        <span class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                    <?php if (isset($errors['password'])): ?>
                    <div class="invalid-feedback">
                        <?php echo $errors['password']; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-options">
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember"><?php echo get_content('login', 'remember_me', 'Remember me'); ?></label>
                    </div>
                    <a href="<?php echo base_url('?page=reset-password'); ?>" class="forgot-password">
                        <?php echo get_content('login', 'forgot_password', 'Forgot Password?'); ?>
                    </a>
                </div>
                
                <div class="form-submit">
                    <button type="submit" name="login" class="btn btn-primary btn-block">
                        <i class="fas fa-sign-in-alt"></i>
                        <?php echo get_content('login', 'login_button', 'Login'); ?>
                    </button>
                </div>
            </form>
            
            <div class="auth-footer">
                <p>
                    <?php echo get_content('login', 'no_account', 'Don\'t have an account?'); ?>
                    <a href="<?php echo base_url('?page=register' . ($redirect ? '&redirect=' . $redirect . $redirect_params : '')); ?>">
                        <?php echo get_content('login', 'register_link', 'Register Now'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
</section>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
