<?php
/**
 * Clear Cache System
 * مسح جميع أنواع الكاش من الموقع
 */

session_start();

// Security check - only allow admin access
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: admin/login.php');
    exit();
}

$cleared_items = [];
$errors = [];

// Function to delete directory contents
function deleteDirectoryContents($dir) {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), array('.', '..'));
    $deleted_count = 0;
    
    foreach ($files as $file) {
        $file_path = $dir . DIRECTORY_SEPARATOR . $file;
        if (is_dir($file_path)) {
            deleteDirectoryContents($file_path);
            rmdir($file_path);
        } else {
            if (unlink($file_path)) {
                $deleted_count++;
            }
        }
    }
    
    return $deleted_count;
}

// Function to get directory size
function getDirectorySize($dir) {
    if (!is_dir($dir)) {
        return 0;
    }
    
    $size = 0;
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($files as $file) {
        $size += $file->getSize();
    }
    
    return $size;
}

// Function to format bytes
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

// Clear different types of cache
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $cache_types = $_POST['cache_types'] ?? [];
    
    foreach ($cache_types as $type) {
        switch ($type) {
            case 'browser_cache':
                // Clear browser cache headers
                header("Cache-Control: no-cache, no-store, must-revalidate");
                header("Pragma: no-cache");
                header("Expires: 0");
                $cleared_items[] = 'Browser Cache Headers';
                break;
                
            case 'temp_files':
                // Clear temporary files
                $temp_dir = sys_get_temp_dir();
                $temp_files = glob($temp_dir . '/php*');
                $deleted = 0;
                foreach ($temp_files as $file) {
                    if (is_file($file) && unlink($file)) {
                        $deleted++;
                    }
                }
                $cleared_items[] = "Temporary Files ($deleted files)";
                break;
                
            case 'session_cache':
                // Clear session files
                if (session_status() === PHP_SESSION_ACTIVE) {
                    session_destroy();
                    session_start();
                }
                $cleared_items[] = 'Session Cache';
                break;
                
            case 'image_cache':
                // Clear image cache if exists
                $image_cache_dir = 'assets/cache/images/';
                if (is_dir($image_cache_dir)) {
                    $deleted = deleteDirectoryContents($image_cache_dir);
                    $cleared_items[] = "Image Cache ($deleted files)";
                } else {
                    $cleared_items[] = 'Image Cache (no cache found)';
                }
                break;
                
            case 'css_js_cache':
                // Clear CSS/JS cache
                $css_cache = glob('assets/css/*.min.css');
                $js_cache = glob('assets/js/*.min.js');
                $deleted = 0;
                
                foreach (array_merge($css_cache, $js_cache) as $file) {
                    if (unlink($file)) {
                        $deleted++;
                    }
                }
                $cleared_items[] = "CSS/JS Cache ($deleted files)";
                break;
                
            case 'database_cache':
                // Clear database query cache (if using MySQL)
                try {
                    require_once 'includes/config.php';
                    $conn->query("RESET QUERY CACHE");
                    $cleared_items[] = 'Database Query Cache';
                } catch (Exception $e) {
                    $errors[] = 'Database Cache: ' . $e->getMessage();
                }
                break;
                
            case 'opcache':
                // Clear OPcache if available
                if (function_exists('opcache_reset')) {
                    opcache_reset();
                    $cleared_items[] = 'OPcache';
                } else {
                    $errors[] = 'OPcache not available';
                }
                break;
                
            case 'all_cache':
                // Clear all cache types
                header("Cache-Control: no-cache, no-store, must-revalidate");
                header("Pragma: no-cache");
                header("Expires: 0");
                
                // Clear all possible cache directories
                $cache_dirs = [
                    'assets/cache/',
                    'cache/',
                    'tmp/',
                    'temp/'
                ];
                
                $total_deleted = 0;
                foreach ($cache_dirs as $dir) {
                    if (is_dir($dir)) {
                        $total_deleted += deleteDirectoryContents($dir);
                    }
                }
                
                if (function_exists('opcache_reset')) {
                    opcache_reset();
                }
                
                $cleared_items[] = "All Cache Types ($total_deleted files)";
                break;
        }
    }
}

// Get current cache sizes
$cache_info = [
    'temp_files' => [
        'size' => 0,
        'count' => 0
    ],
    'image_cache' => [
        'size' => 0,
        'count' => 0
    ],
    'css_js_cache' => [
        'size' => 0,
        'count' => 0
    ]
];

// Calculate temp files
$temp_files = glob(sys_get_temp_dir() . '/php*');
$cache_info['temp_files']['count'] = count($temp_files);
foreach ($temp_files as $file) {
    if (is_file($file)) {
        $cache_info['temp_files']['size'] += filesize($file);
    }
}

// Calculate image cache
if (is_dir('assets/cache/images/')) {
    $cache_info['image_cache']['size'] = getDirectorySize('assets/cache/images/');
    $cache_info['image_cache']['count'] = count(glob('assets/cache/images/*'));
}

// Calculate CSS/JS cache
$css_js_files = array_merge(glob('assets/css/*.min.css'), glob('assets/js/*.min.js'));
$cache_info['css_js_cache']['count'] = count($css_js_files);
foreach ($css_js_files as $file) {
    $cache_info['css_js_cache']['size'] += filesize($file);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مسح الكاش - Social Media Festival</title>
    <link rel="stylesheet" href="assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .cache-container {
            max-width: 1000px;
            margin: 50px auto;
            padding: 30px;
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .cache-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .cache-header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .cache-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .cache-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .cache-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #dee2e6;
            transition: transform 0.3s ease;
        }
        
        .cache-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        
        .cache-card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .cache-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
        }
        
        .cache-form {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 12px;
            border: 2px solid #e9ecef;
        }
        
        .cache-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .cache-option {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .cache-option:hover {
            border-color: #007bff;
            background: #f0f8ff;
        }
        
        .cache-option input[type="checkbox"] {
            margin-left: 12px;
            transform: scale(1.2);
        }
        
        .cache-option label {
            cursor: pointer;
            font-weight: 500;
            color: #495057;
        }
        
        .clear-btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .clear-btn:hover {
            background: linear-gradient(135deg, #c82333, #a71e2a);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(220,53,69,0.3);
        }
        
        .results {
            margin-top: 30px;
            padding: 25px;
            border-radius: 12px;
        }
        
        .success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border: 1px solid #28a745;
            color: #155724;
        }
        
        .error {
            background: linear-gradient(135deg, #f8d7da, #f1b0b7);
            border: 1px solid #dc3545;
            color: #721c24;
        }
        
        .back-link {
            display: inline-block;
            margin-top: 30px;
            padding: 12px 25px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            background: linear-gradient(135deg, #0056b3, #004085);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="cache-container">
        <div class="cache-header">
            <h1><i class="fas fa-broom"></i> مسح الكاش</h1>
            <p>تنظيف وتحسين أداء الموقع</p>
        </div>

        <!-- Cache Information -->
        <div class="cache-grid">
            <div class="cache-card">
                <h3><i class="fas fa-file-alt"></i> الملفات المؤقتة</h3>
                <div class="cache-info">
                    <span>العدد: <?php echo $cache_info['temp_files']['count']; ?></span>
                    <span>الحجم: <?php echo formatBytes($cache_info['temp_files']['size']); ?></span>
                </div>
            </div>
            
            <div class="cache-card">
                <h3><i class="fas fa-images"></i> كاش الصور</h3>
                <div class="cache-info">
                    <span>العدد: <?php echo $cache_info['image_cache']['count']; ?></span>
                    <span>الحجم: <?php echo formatBytes($cache_info['image_cache']['size']); ?></span>
                </div>
            </div>
            
            <div class="cache-card">
                <h3><i class="fas fa-code"></i> كاش CSS/JS</h3>
                <div class="cache-info">
                    <span>العدد: <?php echo $cache_info['css_js_cache']['count']; ?></span>
                    <span>الحجم: <?php echo formatBytes($cache_info['css_js_cache']['size']); ?></span>
                </div>
            </div>
        </div>

        <!-- Clear Cache Form -->
        <form method="POST" class="cache-form">
            <h3><i class="fas fa-trash-alt"></i> اختر نوع الكاش المراد مسحه:</h3>
            
            <div class="cache-options">
                <div class="cache-option">
                    <input type="checkbox" name="cache_types[]" value="browser_cache" id="browser_cache">
                    <label for="browser_cache">كاش المتصفح</label>
                </div>
                
                <div class="cache-option">
                    <input type="checkbox" name="cache_types[]" value="temp_files" id="temp_files">
                    <label for="temp_files">الملفات المؤقتة</label>
                </div>
                
                <div class="cache-option">
                    <input type="checkbox" name="cache_types[]" value="session_cache" id="session_cache">
                    <label for="session_cache">كاش الجلسات</label>
                </div>
                
                <div class="cache-option">
                    <input type="checkbox" name="cache_types[]" value="image_cache" id="image_cache">
                    <label for="image_cache">كاش الصور</label>
                </div>
                
                <div class="cache-option">
                    <input type="checkbox" name="cache_types[]" value="css_js_cache" id="css_js_cache">
                    <label for="css_js_cache">كاش CSS/JS</label>
                </div>
                
                <div class="cache-option">
                    <input type="checkbox" name="cache_types[]" value="database_cache" id="database_cache">
                    <label for="database_cache">كاش قاعدة البيانات</label>
                </div>
                
                <div class="cache-option">
                    <input type="checkbox" name="cache_types[]" value="opcache" id="opcache">
                    <label for="opcache">OPcache</label>
                </div>
                
                <div class="cache-option" style="grid-column: 1 / -1; border: 2px solid #dc3545;">
                    <input type="checkbox" name="cache_types[]" value="all_cache" id="all_cache">
                    <label for="all_cache"><strong>مسح جميع أنواع الكاش</strong></label>
                </div>
            </div>
            
            <button type="submit" class="clear-btn">
                <i class="fas fa-broom"></i> مسح الكاش المحدد
            </button>
        </form>

        <!-- Results -->
        <?php if (!empty($cleared_items)): ?>
        <div class="results success">
            <h3><i class="fas fa-check-circle"></i> تم مسح الكاش بنجاح!</h3>
            <ul>
                <?php foreach ($cleared_items as $item): ?>
                <li><?php echo $item; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <?php if (!empty($errors)): ?>
        <div class="results error">
            <h3><i class="fas fa-exclamation-circle"></i> حدثت أخطاء:</h3>
            <ul>
                <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <a href="admin/index.php" class="back-link">
            <i class="fas fa-arrow-right"></i> العودة للوحة الإدارة
        </a>
    </div>

    <script>
        // Select all cache when "all_cache" is checked
        document.getElementById('all_cache').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('input[name="cache_types[]"]:not(#all_cache)');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
        
        // Auto refresh page info every 30 seconds
        setTimeout(() => {
            if (!document.querySelector('.results')) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
