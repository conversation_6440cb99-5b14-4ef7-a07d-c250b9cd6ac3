/* Profile Page Styles */
:root {
    --profile-bg-color: #000000;
    --profile-text-color: #ffffff;
    --profile-gold: #d4af37;
    --profile-gold-light: #e5c158;
    --profile-gold-dark: #b38728;
    --profile-card-bg: #111111;
    --profile-card-hover-bg: #1a1a1a;
    --profile-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    --profile-border-radius: 8px;
    --profile-transition: all 0.3s ease;
}

.profile-section {
    background: linear-gradient(to bottom, var(--profile-bg-color), #111111);
    padding: 60px 0;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.profile-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(212, 175, 55, 0.05), transparent 70%);
    pointer-events: none;
}

.profile-header {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

.profile-title {
    color: var(--profile-gold);
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.profile-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, transparent, var(--profile-gold), transparent);
}

.profile-description {
    color: var(--profile-text-color);
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

.profile-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 30px;
    margin-bottom: 40px;
}

.profile-sidebar {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    box-shadow: var(--profile-shadow);
    border: 1px solid rgba(212, 175, 55, 0.1);
    padding: 30px;
}

.profile-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin: 0 auto 20px;
    overflow: hidden;
    border: 3px solid var(--profile-gold);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-avatar-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    color: var(--profile-gold);
    font-size: 3rem;
}

.profile-name {
    text-align: center;
    color: var(--profile-gold);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.profile-username {
    text-align: center;
    color: var(--profile-text-color);
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 20px;
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    color: var(--profile-gold);
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-label {
    color: var(--profile-text-color);
    font-size: 0.9rem;
    opacity: 0.8;
}

.profile-nav {
    margin-top: 30px;
}

.profile-nav-item {
    display: block;
    padding: 12px 15px;
    margin-bottom: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--profile-border-radius);
    color: var(--profile-text-color);
    text-decoration: none;
    transition: var(--profile-transition);
    border-left: 3px solid transparent;
}

.profile-nav-item:hover, .profile-nav-item.active {
    background: rgba(212, 175, 55, 0.1);
    border-left-color: var(--profile-gold);
    color: var(--profile-gold);
}

.profile-nav-item i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.profile-content {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    box-shadow: var(--profile-shadow);
    border: 1px solid rgba(212, 175, 55, 0.1);
    padding: 30px;
}

.profile-section-title {
    color: var(--profile-gold);
    font-size: 1.5rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(212, 175, 55, 0.2);
}

.profile-form-group {
    margin-bottom: 20px;
}

.profile-form-label {
    display: block;
    color: var(--profile-text-color);
    margin-bottom: 8px;
    font-weight: 500;
}

.profile-form-input {
    width: 100%;
    padding: 12px 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--profile-border-radius);
    color: var(--profile-text-color);
    font-size: 1rem;
    transition: var(--profile-transition);
}

.profile-form-input:focus {
    border-color: var(--profile-gold);
    outline: none;
    box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.profile-form-submit {
    background: linear-gradient(145deg, var(--profile-gold), var(--profile-gold-dark));
    color: #000;
    border: none;
    padding: 12px 25px;
    border-radius: var(--profile-border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--profile-transition);
}

.profile-form-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.votes-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    min-height: 200px;
}

.no-votes-message {
    grid-column: 1 / -1;
    text-align: center;
    padding: 40px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: var(--profile-border-radius);
    border: 1px dashed rgba(212, 175, 55, 0.3);
}

.no-votes-message i {
    font-size: 3rem;
    color: var(--profile-gold);
    margin-bottom: 15px;
    opacity: 0.7;
}

.no-votes-message p {
    color: var(--profile-text-color);
    font-size: 1.1rem;
    opacity: 0.8;
}

.vote-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--profile-border-radius);
    overflow: hidden;
    transition: var(--profile-transition);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.vote-card:hover {
    transform: translateY(-5px);
    border-color: var(--profile-gold);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.vote-image {
    height: 150px;
    overflow: hidden;
    position: relative;
}

.vote-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.vote-card:hover .vote-image img {
    transform: scale(1.05);
}

.image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    color: var(--profile-gold);
    font-size: 3rem;
}

.vote-info {
    padding: 15px;
    display: flex;
    flex-direction: column;
    height: calc(100% - 150px);
}

.vote-name {
    color: var(--profile-gold);
    font-size: 1.1rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.vote-category {
    color: var(--profile-text-color);
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 10px;
    flex: 1;
}

.vote-date {
    color: var(--profile-text-color);
    font-size: 0.8rem;
    opacity: 0.6;
    margin-bottom: 10px;
}

.vote-actions {
    margin-top: auto;
}

.btn-view-nominee {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--profile-white);
    border-radius: 20px;
    font-size: 0.8rem;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-view-nominee:hover {
    background: rgba(212, 175, 55, 0.2);
    color: var(--profile-gold);
    border-color: rgba(212, 175, 55, 0.3);
    transform: translateY(-2px);
}

.btn-view-nominee i {
    margin-right: 5px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .profile-container {
        grid-template-columns: 1fr;
    }

    .profile-sidebar {
        margin-bottom: 30px;
    }
}

@media (max-width: 768px) {
    .profile-section {
        padding: 40px 0;
    }

    .profile-title {
        font-size: 2rem;
    }

    .votes-list {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 576px) {
    .profile-avatar {
        width: 120px;
        height: 120px;
    }

    .profile-content {
        padding: 20px;
    }

    .votes-list {
        grid-template-columns: 1fr 1fr;
    }
}
