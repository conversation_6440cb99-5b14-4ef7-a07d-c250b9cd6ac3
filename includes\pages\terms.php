<?php
/**
 * Terms and Conditions Page for the Social Media Festival website
 */

// Get content from database
$title = get_content('terms', 'title', 'Terms and Conditions');
$last_updated = get_content('terms', 'last_updated', 'Last Updated: May 22, 2025');
?>

<section class="terms-section">
    <div class="container">
        <div class="section-header">
            <h1><?php echo $title; ?></h1>
            <p class="last-updated"><?php echo $last_updated; ?></p>
        </div>

        <div class="terms-content">
            <div class="terms-section">
                <h2><?php echo get_content('terms', 'introduction_title', 'Introduction'); ?></h2>
                <p><?php echo get_content('terms', 'introduction_text', 'Welcome to the Social Media Festival website. These terms and conditions outline the rules and regulations for the use of our website.'); ?></p>
                <p><?php echo get_content('terms', 'introduction_text2', 'By accessing this website, we assume you accept these terms and conditions in full. Do not continue to use the Social Media Festival website if you do not accept all of the terms and conditions stated on this page.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'definitions_title', 'Definitions'); ?></h2>
                <p><?php echo get_content('terms', 'definitions_text', 'The following terminology applies to these Terms and Conditions, Privacy Statement and Disclaimer Notice and any or all Agreements:'); ?></p>
                
                <ul>
                    <li><?php echo get_content('terms', 'definition_1', '"User", "You" and "Your" refers to you, the person accessing this website and accepting the Company\'s terms and conditions.'); ?></li>
                    <li><?php echo get_content('terms', 'definition_2', '"The Company", "Ourselves", "We", "Our" and "Us", refers to the Social Media Festival.'); ?></li>
                    <li><?php echo get_content('terms', 'definition_3', '"Party", "Parties", or "Us", refers to both the User and ourselves, or either the User or ourselves.'); ?></li>
                </ul>
                
                <p><?php echo get_content('terms', 'definitions_text2', 'All terms refer to the offer, acceptance and consideration of payment necessary to undertake the process of our assistance to the User in the most appropriate manner, whether by formal meetings of a fixed duration, or any other means, for the express purpose of meeting the User\'s needs in respect of provision of the Company\'s stated services/products, in accordance with and subject to, prevailing law.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'license_title', 'License'); ?></h2>
                <p><?php echo get_content('terms', 'license_text', 'Unless otherwise stated, the Social Media Festival and/or its licensors own the intellectual property rights for all material on the Social Media Festival website. All intellectual property rights are reserved.'); ?></p>
                
                <p><?php echo get_content('terms', 'license_text2', 'You may view and/or print pages from the website for your own personal use subject to restrictions set in these terms and conditions.'); ?></p>
                
                <p><?php echo get_content('terms', 'license_restrictions', 'You must not:'); ?></p>
                
                <ul>
                    <li><?php echo get_content('terms', 'license_restriction_1', 'Republish material from this website.'); ?></li>
                    <li><?php echo get_content('terms', 'license_restriction_2', 'Sell, rent or sub-license material from the website.'); ?></li>
                    <li><?php echo get_content('terms', 'license_restriction_3', 'Reproduce, duplicate or copy material from the website.'); ?></li>
                    <li><?php echo get_content('terms', 'license_restriction_4', 'Redistribute content from this website (unless content is specifically made for redistribution).'); ?></li>
                </ul>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'user_content_title', 'User Content'); ?></h2>
                <p><?php echo get_content('terms', 'user_content_text', 'In these Terms and Conditions, "User Content" shall mean any audio, video, text, images or other material you choose to display on this website. By displaying your User Content, you grant the Social Media Festival a non-exclusive, worldwide, irrevocable, royalty-free, sublicensable license to use, reproduce, adapt, publish, translate and distribute it in any and all media.'); ?></p>
                
                <p><?php echo get_content('terms', 'user_content_text2', 'Your User Content must be your own and must not be infringing on any third party\'s rights. The Social Media Festival reserves the right to remove any of your content from this website at any time without notice.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'voting_title', 'Voting Rules'); ?></h2>
                <p><?php echo get_content('terms', 'voting_text', 'The Social Media Festival voting system is subject to the following rules:'); ?></p>
                
                <ul>
                    <li><?php echo get_content('terms', 'voting_rule_1', 'Users must register an account to participate in voting.'); ?></li>
                    <li><?php echo get_content('terms', 'voting_rule_2', 'Each registered user is entitled to one vote per category.'); ?></li>
                    <li><?php echo get_content('terms', 'voting_rule_3', 'The Social Media Festival reserves the right to disqualify votes that appear to be fraudulent or manipulated.'); ?></li>
                    <li><?php echo get_content('terms', 'voting_rule_4', 'The Social Media Festival\'s decision regarding voting results is final.'); ?></li>
                </ul>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'liability_title', 'Limitation of Liability'); ?></h2>
                <p><?php echo get_content('terms', 'liability_text', 'In no event shall the Social Media Festival, nor any of its officers, directors and employees, be liable to you for anything arising out of or in any way connected with your use of this website, whether such liability is under contract, tort or otherwise, and the Social Media Festival, including its officers, directors and employees shall not be liable for any indirect, consequential or special liability arising out of or in any way related to your use of this website.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'indemnification_title', 'Indemnification'); ?></h2>
                <p><?php echo get_content('terms', 'indemnification_text', 'You hereby indemnify to the fullest extent the Social Media Festival from and against any and all liabilities, costs, demands, causes of action, damages and expenses (including reasonable attorney\'s fees) arising out of or in any way related to your breach of any of the provisions of these Terms.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'severability_title', 'Severability'); ?></h2>
                <p><?php echo get_content('terms', 'severability_text', 'If any provision of these Terms is found to be unenforceable or invalid under any applicable law, such unenforceability or invalidity shall not render these Terms unenforceable or invalid as a whole, and such provisions shall be deleted without affecting the remaining provisions herein.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'variation_title', 'Variation of Terms'); ?></h2>
                <p><?php echo get_content('terms', 'variation_text', 'The Social Media Festival is permitted to revise these Terms at any time as it sees fit, and by using this website you are expected to review such Terms on a regular basis to ensure you understand all terms and conditions governing use of this website.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'assignment_title', 'Assignment'); ?></h2>
                <p><?php echo get_content('terms', 'assignment_text', 'The Social Media Festival shall be permitted to assign, transfer, and subcontract its rights and/or obligations under these Terms without any notification or consent required. However, you shall not be permitted to assign, transfer, or subcontract any of your rights and/or obligations under these Terms.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'entire_agreement_title', 'Entire Agreement'); ?></h2>
                <p><?php echo get_content('terms', 'entire_agreement_text', 'These Terms, including any legal notices and disclaimers contained on this website, constitute the entire agreement between the Social Media Festival and you in relation to your use of this website, and supersede all prior agreements and understandings with respect to the same.'); ?></p>
            </div>

            <div class="terms-section">
                <h2><?php echo get_content('terms', 'governing_law_title', 'Governing Law & Jurisdiction'); ?></h2>
                <p><?php echo get_content('terms', 'governing_law_text', 'These Terms will be governed by and construed in accordance with the laws of Egypt, and you submit to the non-exclusive jurisdiction of the courts located in Egypt for the resolution of any disputes.'); ?></p>
            </div>
        </div>
    </div>
</section>

<style>
    .terms-section {
        padding: 80px 0;
        background-color: var(--color-black);
        color: var(--color-white);
    }

    .terms-section .section-header {
        text-align: center;
        margin-bottom: 50px;
    }

    .terms-section h1 {
        color: var(--color-gold);
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .last-updated {
        color: var(--color-light-gray);
        font-style: italic;
    }

    .terms-content {
        max-width: 800px;
        margin: 0 auto;
    }

    .terms-section {
        margin-bottom: 40px;
    }

    .terms-section h2 {
        color: var(--color-gold);
        font-size: 1.8rem;
        margin-bottom: 20px;
        border-bottom: 1px solid rgba(212, 175, 55, 0.3);
        padding-bottom: 10px;
    }

    .terms-section p {
        margin-bottom: 15px;
        line-height: 1.6;
    }

    .terms-section ul {
        margin-left: 20px;
        margin-bottom: 20px;
    }

    .terms-section li {
        margin-bottom: 10px;
        line-height: 1.6;
    }

    .terms-section strong {
        color: var(--color-gold);
    }

    /* RTL Support */
    html[dir="rtl"] .terms-section ul {
        margin-left: 0;
        margin-right: 20px;
    }
</style>
