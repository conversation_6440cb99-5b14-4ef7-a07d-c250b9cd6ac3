/**
 * Service Worker للكاش والأداء
 * يحسن سرعة الموقع بشكل كبير
 */

const CACHE_NAME = 'social-media-festival-v1.0';
const STATIC_CACHE = 'static-v1.0';
const DYNAMIC_CACHE = 'dynamic-v1.0';

// الملفات المهمة للكاش
const STATIC_ASSETS = [
    '/',
    '/index.php',
    '/assets/css/main.css',
    '/assets/js/main.js',
    '/assets/js/lazy-load.js',
    '/assets/js/image-optimizer.js',
    '/assets/js/performance-optimizer.js',
    '/assets/img/logo.png',
    '/assets/fonts/Cairo-Regular.woff2',
    '/assets/fonts/Poppins-Regular.woff2'
];

// الملفات التي لا نريد كاشها
const EXCLUDE_URLS = [
    '/admin/',
    '/api/',
    '/.php',
    '/includes/vote.php',
    '/includes/upload.php',
    '/includes/compare.php'
];

// تثبيت Service Worker
self.addEventListener('install', event => {
    console.log('🔧 Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('📦 Caching static assets...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('✅ Static assets cached successfully');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Failed to cache static assets:', error);
            })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        // حذف الكاش القديم
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('🗑️ Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker activated');
                return self.clients.claim();
            })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);
    
    // تجاهل الطلبات المستثناة
    if (shouldExclude(url.pathname)) {
        return;
    }

    // استراتيجية مختلفة حسب نوع الملف
    if (isStaticAsset(request)) {
        event.respondWith(cacheFirst(request));
    } else if (isImage(request)) {
        event.respondWith(cacheFirst(request));
    } else if (isVideo(request)) {
        event.respondWith(networkFirst(request));
    } else {
        event.respondWith(networkFirst(request));
    }
});

// فحص الملفات المستثناة
function shouldExclude(pathname) {
    return EXCLUDE_URLS.some(excludeUrl => pathname.includes(excludeUrl));
}

// فحص الملفات الثابتة
function isStaticAsset(request) {
    return request.url.match(/\.(css|js|woff|woff2|ttf|otf|eot)$/);
}

// فحص الصور
function isImage(request) {
    return request.url.match(/\.(jpg|jpeg|png|gif|webp|svg|ico)$/);
}

// فحص الفيديوهات
function isVideo(request) {
    return request.url.match(/\.(mp4|webm|ogg|avi|mov)$/);
}

// استراتيجية Cache First (للملفات الثابتة)
async function cacheFirst(request) {
    try {
        // البحث في الكاش أولاً
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            console.log('📦 Serving from cache:', request.url);
            return cachedResponse;
        }

        // إذا لم توجد في الكاش، جلب من الشبكة
        console.log('🌐 Fetching from network:', request.url);
        const networkResponse = await fetch(request);
        
        // حفظ في الكاش للمرة القادمة
        if (networkResponse.status === 200) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.error('❌ Cache first failed:', error);
        return new Response('Offline', { status: 503 });
    }
}

// استراتيجية Network First (للمحتوى الديناميكي)
async function networkFirst(request) {
    try {
        // محاولة الجلب من الشبكة أولاً
        console.log('🌐 Fetching from network:', request.url);
        const networkResponse = await fetch(request);
        
        // حفظ في الكاش الديناميكي
        if (networkResponse.status === 200) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        // إذا فشلت الشبكة، البحث في الكاش
        console.log('📦 Network failed, trying cache:', request.url);
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // إذا لم توجد في الكاش، إرجاع صفحة offline
        return getOfflinePage(request);
    }
}

// صفحة Offline
function getOfflinePage(request) {
    if (request.destination === 'document') {
        return new Response(`
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>غير متصل - مهرجان وسائل التواصل الاجتماعي</title>
                <style>
                    body {
                        font-family: 'Cairo', sans-serif;
                        text-align: center;
                        padding: 50px;
                        background: linear-gradient(135deg, #000000, #1a1a1a);
                        color: #ffffff;
                        min-height: 100vh;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-direction: column;
                    }
                    .offline-icon {
                        font-size: 4rem;
                        color: #d4af37;
                        margin-bottom: 20px;
                    }
                    h1 {
                        color: #d4af37;
                        margin-bottom: 20px;
                    }
                    p {
                        font-size: 1.2rem;
                        margin-bottom: 30px;
                        opacity: 0.8;
                    }
                    .retry-btn {
                        background: linear-gradient(135deg, #d4af37, #f1e5ac);
                        color: #000;
                        padding: 15px 30px;
                        border: none;
                        border-radius: 25px;
                        font-size: 1.1rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: transform 0.3s ease;
                    }
                    .retry-btn:hover {
                        transform: translateY(-2px);
                    }
                </style>
            </head>
            <body>
                <div class="offline-icon">📱</div>
                <h1>غير متصل بالإنترنت</h1>
                <p>يبدو أنك غير متصل بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.</p>
                <button class="retry-btn" onclick="window.location.reload()">إعادة المحاولة</button>
            </body>
            </html>
        `, {
            headers: { 'Content-Type': 'text/html; charset=utf-8' }
        });
    }
    
    return new Response('Offline', { status: 503 });
}

// تنظيف الكاش القديم
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'CLEAN_CACHE') {
        cleanOldCache();
    }
});

async function cleanOldCache() {
    const cache = await caches.open(DYNAMIC_CACHE);
    const requests = await cache.keys();
    
    // حذف الملفات القديمة (أكثر من 7 أيام)
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    
    for (const request of requests) {
        const response = await cache.match(request);
        const dateHeader = response.headers.get('date');
        
        if (dateHeader) {
            const responseDate = new Date(dateHeader).getTime();
            if (responseDate < oneWeekAgo) {
                await cache.delete(request);
                console.log('🗑️ Deleted old cache entry:', request.url);
            }
        }
    }
}

console.log('🎉 Service Worker loaded successfully!');
