/**
 * Enhanced RTL CSS for Arabic language support
 */

/* ===== Base Styles ===== */
body {
    text-align: right;
    direction: rtl;
}

input, textarea, select {
    text-align: right;
}

/* ===== Header ===== */
.header-inner {
    flex-direction: row-reverse;
}

.nav-menu {
    flex-direction: row-reverse;
}

.nav-menu a::after {
    right: 0;
    left: auto;
}

.language-switcher {
    margin-right: 0;
    margin-left: var(--spacing-md);
}

.language-switcher a {
    margin-left: 0;
    margin-right: 2px;
}

.language-switcher a.active {
    margin-left: 0;
    margin-right: 2px;
}

.auth-buttons {
    flex-direction: row-reverse;
}

.btn-login,
.btn-register {
    margin-left: 0;
    margin-right: var(--spacing-sm);
}

.user-toggle i {
    margin-right: 0;
    margin-left: var(--spacing-xs);
}

.dropdown-menu {
    right: auto;
    left: 0;
    text-align: right;
}

/* ===== Hero Section ===== */
.hero-content {
    text-align: right;
}

.hero-title, .hero-subtitle {
    text-align: right;
}

.btn-vote-hero {
    direction: rtl;
}

.btn-vote-hero i {
    margin-right: 0;
    margin-left: 8px;
}

/* ===== Sections ===== */
.section-title, .section-subtitle {
    text-align: right;
}

/* ===== Nominees Section ===== */
.nominee-info {
    direction: rtl;
    text-align: right;
}

.nominee-category {
    text-align: right;
}

.votes-label {
    margin-left: 0;
    margin-right: var(--spacing-xs);
}

/* ===== Categories Section ===== */
.category-info {
    text-align: right;
}

/* ===== Stories Section ===== */
.stories-carousel {
    direction: rtl;
}

.story-info {
    direction: rtl;
    text-align: right;
}

/* ===== Sponsors Section ===== */
.sponsors-grid {
    direction: rtl;
}

.category-title {
    text-align: right;
}

/* ===== CEO Section ===== */
.ceo-info {
    text-align: right;
}

.ceo-title h2::after {
    left: auto;
    right: 0;
}

.ceo-social {
    justify-content: flex-start;
}

/* ===== Team Section ===== */
.member-info {
    text-align: right;
}

.member-social {
    justify-content: center;
}

/* ===== Invitations Section ===== */
.invitation-info {
    text-align: right;
}

/* ===== Footer ===== */
.footer-top {
    text-align: right;
}

.footer-nav ul,
.footer-contact ul {
    padding-right: 0;
}

.footer-contact i {
    margin-right: 0;
    margin-left: var(--spacing-xs);
}

.social-links {
    flex-direction: row-reverse;
}

.social-links a {
    margin-right: 0;
    margin-left: var(--spacing-sm);
}

.newsletter-form input {
    border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
}

.newsletter-form button {
    border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
}

.footer-links a {
    margin-left: 0;
    margin-right: var(--spacing-md);
}

/* ===== Auth Pages ===== */
.auth-header {
    text-align: right;
}

.auth-form label {
    text-align: right;
}

.input-icon {
    left: auto;
    right: 15px;
}

.auth-form .form-control {
    padding: 12px 45px 12px 15px;
    text-align: right;
}

.password-toggle {
    right: auto;
    left: 15px;
}

.form-options {
    flex-direction: row-reverse;
}

.remember-me input[type="checkbox"] {
    margin-right: 0;
    margin-left: 8px;
}

.terms-checkbox {
    text-align: right;
}

.terms-checkbox input[type="checkbox"] {
    margin-right: 0;
    margin-left: 8px;
}

.auth-footer {
    text-align: right;
}

.social-login i {
    margin-right: 0;
    margin-left: 10px;
}

/* ===== Vote Page ===== */
.main-categories-section .categories-scroll {
    direction: rtl;
}

.subcategories-section .subcategories-scroll {
    direction: rtl;
}

.subcategories-section::after {
    right: auto;
    left: 0;
    background: linear-gradient(to left, transparent, rgba(10, 10, 10, 0.9));
}

.nominee-info h3::after {
    left: auto;
    right: 0;
}

.nominee-votes {
    flex-direction: row-reverse;
}

.nominee-votes i {
    margin-right: 0;
    margin-left: 5px;
}

.btn-vote-small i, .btn-login-small i {
    margin-right: 0;
    margin-left: 5px;
}

/* ===== Media Queries ===== */
@media (max-width: 768px) {
    .header-inner {
        flex-direction: row-reverse;
        justify-content: space-between;
    }

    .nav-menu {
        right: -100%;
        left: auto;
        text-align: right;
    }

    .nav-menu.active {
        right: 0;
        left: auto;
    }

    .nav-menu li {
        width: 100%;
        text-align: right;
        padding-right: 20px;
    }

    .header-actions {
        flex-direction: row-reverse;
        align-items: center;
        justify-content: flex-start;
    }

    .language-switcher {
        margin-left: 0;
        margin-bottom: 0;
        margin-right: 10px;
    }

    .auth-buttons {
        flex-direction: row-reverse;
    }

    .user-menu {
        margin-right: 10px;
    }

    .dropdown-menu {
        right: auto;
        left: 0;
    }

    .footer-links a {
        margin: 0 var(--spacing-xs);
    }
}
