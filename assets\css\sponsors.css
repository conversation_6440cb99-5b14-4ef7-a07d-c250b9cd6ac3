/* Sponsors Page Styles */
:root {
    --sponsors-bg-color: #000000;
    --sponsors-text-color: #ffffff;
    --sponsors-gold: #d4af37;
    --sponsors-gold-light: #e5c158;
    --sponsors-gold-dark: #b38728;
    --sponsors-card-bg: #111111;
    --sponsors-card-hover-bg: #1a1a1a;
    --sponsors-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    --sponsors-border-radius: 8px;
    --sponsors-transition: all 0.3s ease;
}

.sponsors-section {
    background: linear-gradient(to bottom, var(--sponsors-bg-color), #111111);
    padding: 60px 0;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.sponsors-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(212, 175, 55, 0.1), transparent 70%),
                radial-gradient(circle at bottom left, rgba(212, 175, 55, 0.05), transparent 70%);
    pointer-events: none;
}

.sponsors-header {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

.sponsors-title {
    color: var(--sponsors-gold);
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    display: inline-block;
}

.sponsors-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(to right, transparent, var(--sponsors-gold), transparent);
}

.sponsors-description {
    color: var(--sponsors-text-color);
    max-width: 800px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
}

/* Sponsors Categories */
.sponsors-categories {
    margin-bottom: 40px;
}

.category-title {
    color: var(--sponsors-gold);
    font-size: 1.8rem;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
    display: inline-block;
}

.category-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--sponsors-gold);
}

.category-container {
    margin-bottom: 60px;
}

.category-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.category-icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    color: var(--sponsors-gold);
    font-size: 1.8rem;
}

/* Sponsors Grid */
.sponsors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.sponsor-card {
    background: linear-gradient(145deg, var(--sponsors-card-bg), #0a0a0a);
    border-radius: var(--sponsors-border-radius);
    overflow: hidden;
    box-shadow: var(--sponsors-shadow);
    transition: var(--sponsors-transition);
    border: 1px solid rgba(212, 175, 55, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.sponsor-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, rgba(212, 175, 55, 0.1), transparent);
    opacity: 0;
    transition: var(--sponsors-transition);
    pointer-events: none;
}

.sponsor-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
    border-color: rgba(212, 175, 55, 0.3);
}

.sponsor-card:hover::before {
    opacity: 1;
}

.sponsor-logo {
    height: 180px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    position: relative;
    overflow: hidden;
}

.sponsor-logo img {
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
    transition: var(--sponsors-transition);
    filter: grayscale(20%);
}

.sponsor-card:hover .sponsor-logo img {
    transform: scale(1.05);
    filter: grayscale(0%);
}

.sponsor-info {
    padding: 20px;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.sponsor-name {
    color: var(--sponsors-gold);
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.sponsor-description {
    color: var(--sponsors-text-color);
    opacity: 0.8;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 15px;
    flex: 1;
}

.sponsor-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sponsor-category {
    color: var(--sponsors-text-color);
    opacity: 0.7;
    font-size: 0.8rem;
}

.sponsor-actions {
    display: flex;
    gap: 10px;
}

.btn-website {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: var(--sponsors-transition);
    cursor: pointer;
    text-decoration: none;
    background: linear-gradient(145deg, var(--sponsors-gold), var(--sponsors-gold-dark));
    color: #000;
    border: none;
}

.btn-website:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.btn-website i {
    margin-right: 5px;
}

/* Platinum Sponsors */
.platinum-sponsors {
    margin-bottom: 60px;
}

.platinum-title {
    text-align: center;
    margin-bottom: 30px;
}

.platinum-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
}

.platinum-card {
    background: linear-gradient(145deg, #1a1a1a, #0a0a0a);
    border-radius: var(--sponsors-border-radius);
    overflow: hidden;
    box-shadow: var(--sponsors-shadow);
    transition: var(--sponsors-transition);
    border: 1px solid rgba(212, 175, 55, 0.2);
    position: relative;
    padding: 20px;
}

.platinum-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(to right, var(--sponsors-gold), var(--sponsors-gold-dark));
}

.platinum-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.6);
}

.platinum-logo {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.platinum-logo img {
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
}

.platinum-info {
    text-align: center;
}

.platinum-name {
    color: var(--sponsors-gold);
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.platinum-description {
    color: var(--sponsors-text-color);
    opacity: 0.8;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 20px;
}

.platinum-actions {
    margin-top: 20px;
}

/* Become a Sponsor */
.become-sponsor {
    background: linear-gradient(145deg, rgba(30, 30, 30, 0.7), rgba(10, 10, 10, 0.9));
    border-radius: var(--sponsors-border-radius);
    padding: 40px;
    text-align: center;
    margin-top: 60px;
    border: 1px solid rgba(212, 175, 55, 0.1);
    box-shadow: var(--sponsors-shadow);
}

.become-sponsor-title {
    color: var(--sponsors-gold);
    font-size: 2rem;
    margin-bottom: 20px;
}

.become-sponsor-description {
    color: var(--sponsors-text-color);
    max-width: 700px;
    margin: 0 auto 30px;
    font-size: 1.1rem;
    line-height: 1.6;
}

.btn-become-sponsor {
    background: linear-gradient(145deg, var(--sponsors-gold), var(--sponsors-gold-dark));
    color: #000;
    border: none;
    padding: 12px 30px;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--sponsors-transition);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.btn-become-sponsor:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4);
}

.btn-become-sponsor i {
    margin-right: 10px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sponsors-grid {
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    }

    .platinum-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .become-sponsor {
        padding: 30px;
    }
}

@media (max-width: 768px) {
    .sponsors-section {
        padding: 40px 0;
    }

    .sponsors-title {
        font-size: 2rem;
    }

    .sponsors-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 20px;
    }

    .sponsor-logo {
        height: 150px;
    }

    .platinum-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columns for tablets */
        gap: 20px;
        max-width: 100%;
        margin: 0 auto;
    }

    .platinum-card {
        padding: 15px;
    }

    .platinum-logo {
        height: 100px;
        margin-bottom: 15px;
    }

    .platinum-name {
        font-size: 1.3rem;
    }

    .become-sponsor {
        padding: 25px;
    }

    .become-sponsor-title {
        font-size: 1.7rem;
    }
}

@media (max-width: 576px) {
    .sponsors-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columns on mobile */
        gap: 15px;
        max-width: 100%;
        margin: 0 auto 40px;
    }

    .sponsor-logo {
        height: 120px; /* Smaller height for mobile */
        padding: 10px;
    }

    .sponsor-info {
        padding: 15px 10px;
    }

    .sponsor-name {
        font-size: 1rem;
        margin-bottom: 5px;
    }

    .sponsor-description {
        font-size: 0.8rem;
        margin-bottom: 10px;
        line-height: 1.4;
    }

    .sponsor-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .sponsor-actions {
        width: 100%;
    }

    .btn-website {
        width: 100%;
        padding: 6px 10px;
        font-size: 0.75rem;
    }

    .platinum-grid {
        grid-template-columns: repeat(2, 1fr); /* 2 columns on mobile */
        gap: 15px;
    }

    .platinum-card {
        padding: 10px;
    }

    .platinum-logo {
        height: 80px;
        margin-bottom: 10px;
    }

    .platinum-name {
        font-size: 1.1rem;
        margin-bottom: 5px;
    }

    .platinum-description {
        font-size: 0.8rem;
        line-height: 1.4;
        margin-bottom: 15px;
    }

    .category-header {
        margin-bottom: 20px;
    }

    .category-title {
        font-size: 1.5rem;
    }

    .category-icon {
        width: 30px;
        height: 30px;
        font-size: 1.5rem;
        margin-right: 10px;
    }

    .become-sponsor {
        padding: 20px;
    }
}

/* Extra small devices */
@media (max-width: 375px) {
    .sponsors-grid, .platinum-grid {
        grid-template-columns: repeat(2, 1fr); /* Keep 2 columns even on very small devices */
        gap: 10px;
    }

    .sponsor-logo, .platinum-logo {
        height: 90px;
    }

    .sponsor-info, .platinum-info {
        padding: 10px 8px;
    }

    .sponsor-name, .platinum-name {
        font-size: 0.9rem;
    }

    .sponsor-description, .platinum-description {
        font-size: 0.75rem;
        line-height: 1.3;
    }

    .btn-website {
        padding: 5px 8px;
        font-size: 0.7rem;
    }
}
