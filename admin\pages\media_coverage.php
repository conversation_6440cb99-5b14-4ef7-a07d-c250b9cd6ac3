<?php
/**
 * Media Coverage Management Page
 */

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $result = add_media_coverage();
                break;
            case 'edit':
                $result = edit_media_coverage();
                break;
            case 'delete':
                $result = delete_media_coverage();
                break;
            case 'toggle_status':
                $result = toggle_media_coverage_status();
                break;
        }
        
        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    }
}

// Get all media coverage
$media_coverage = get_all_media_coverage();

function add_media_coverage() {
    global $conn;
    
    try {
        $title = trim($_POST['title']);
        $title_ar = trim($_POST['title_ar']);
        $description = trim($_POST['description'] ?? '');
        $description_ar = trim($_POST['description_ar'] ?? '');
        $type = $_POST['type'];
        $link = trim($_POST['link'] ?? '');
        $featured = isset($_POST['featured']) ? 1 : 0;
        $sort_order = intval($_POST['sort_order'] ?? 0);
        
        // Handle image upload
        $image = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../assets/uploads/';
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (!in_array($file_extension, $allowed_extensions)) {
                return ['success' => false, 'message' => 'نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP'];
            }
            
            if ($_FILES['image']['size'] > 5 * 1024 * 1024) { // 5MB
                return ['success' => false, 'message' => 'حجم الملف كبير جداً. الحد الأقصى 5MB'];
            }
            
            $image = 'media_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
            
            if (!move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image)) {
                return ['success' => false, 'message' => 'خطأ في رفع الصورة'];
            }
        }
        
        $sql = "INSERT INTO media_coverage (title, title_ar, description, description_ar, image, type, link, featured, sort_order) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('sssssssii', $title, $title_ar, $description, $description_ar, $image, $type, $link, $featured, $sort_order);
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم إضافة التغطية الإعلامية بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في إضافة التغطية الإعلامية'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

function edit_media_coverage() {
    global $conn;
    
    try {
        $id = intval($_POST['id']);
        $title = trim($_POST['title']);
        $title_ar = trim($_POST['title_ar']);
        $description = trim($_POST['description'] ?? '');
        $description_ar = trim($_POST['description_ar'] ?? '');
        $type = $_POST['type'];
        $link = trim($_POST['link'] ?? '');
        $featured = isset($_POST['featured']) ? 1 : 0;
        $sort_order = intval($_POST['sort_order'] ?? 0);
        
        // Handle image upload
        $image_sql = '';
        $image_param = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = '../assets/uploads/';
            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            
            if (!in_array($file_extension, $allowed_extensions)) {
                return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
            }
            
            if ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
            }
            
            // Delete old image
            $old_result = $conn->query("SELECT image FROM media_coverage WHERE id = $id");
            if ($old_result && $old_row = $old_result->fetch_assoc()) {
                $old_image = $upload_dir . $old_row['image'];
                if (file_exists($old_image)) {
                    unlink($old_image);
                }
            }
            
            $image = 'media_' . time() . '_' . rand(1000, 9999) . '.' . $file_extension;
            
            if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_dir . $image)) {
                $image_sql = ', image = ?';
                $image_param = $image;
            }
        }
        
        $sql = "UPDATE media_coverage SET title = ?, title_ar = ?, description = ?, description_ar = ?, type = ?, link = ?, featured = ?, sort_order = ?" . $image_sql . " WHERE id = ?";
        $stmt = $conn->prepare($sql);
        
        if ($image_param) {
            $stmt->bind_param('ssssssiisi', $title, $title_ar, $description, $description_ar, $type, $link, $featured, $sort_order, $image_param, $id);
        } else {
            $stmt->bind_param('ssssssiii', $title, $title_ar, $description, $description_ar, $type, $link, $featured, $sort_order, $id);
        }
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم تحديث التغطية الإعلامية بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في تحديث التغطية الإعلامية'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

function delete_media_coverage() {
    global $conn;
    
    try {
        $id = intval($_POST['id']);
        
        // Get image name to delete file
        $result = $conn->query("SELECT image FROM media_coverage WHERE id = $id");
        if ($result && $row = $result->fetch_assoc()) {
            $image_file = '../assets/uploads/' . $row['image'];
            if (file_exists($image_file)) {
                unlink($image_file);
            }
        }
        
        $sql = "DELETE FROM media_coverage WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم حذف التغطية الإعلامية بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في حذف التغطية الإعلامية'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

function toggle_media_coverage_status() {
    global $conn;
    
    try {
        $id = intval($_POST['id']);
        
        $sql = "UPDATE media_coverage SET status = CASE WHEN status = 'active' THEN 'inactive' ELSE 'active' END WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param('i', $id);
        
        if ($stmt->execute()) {
            return ['success' => true, 'message' => 'تم تغيير حالة التغطية بنجاح'];
        } else {
            return ['success' => false, 'message' => 'خطأ في تغيير حالة التغطية'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'خطأ: ' . $e->getMessage()];
    }
}

function get_all_media_coverage() {
    global $conn;
    
    $sql = "SELECT * FROM media_coverage ORDER BY type ASC, sort_order ASC, id DESC";
    $result = $conn->query($sql);
    
    $coverage = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $coverage[] = $row;
        }
    }
    
    return $coverage;
}
?>

<div class="admin-page-header">
    <h1><i class="fas fa-tv"></i> إدارة التغطية الإعلامية</h1>
    <button class="btn btn-primary" onclick="openAddModal()">
        <i class="fas fa-plus"></i> إضافة تغطية جديدة
    </button>
</div>

<?php if (isset($success_message)): ?>
<div class="alert alert-success">
    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
</div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
<div class="alert alert-error">
    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-bullhorn"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo count(array_filter($media_coverage, function($c) { return $c['type'] === 'advertising'; })); ?></h3>
            <p>التغطية الإعلانية</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-tv"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo count(array_filter($media_coverage, function($c) { return $c['type'] === 'tv'; })); ?></h3>
            <p>التغطية التلفزيونية</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-star"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo count(array_filter($media_coverage, function($c) { return $c['featured'] == 1; })); ?></h3>
            <p>التغطية المميزة</p>
        </div>
    </div>
    <div class="stat-card">
        <div class="stat-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-content">
            <h3><?php echo count(array_filter($media_coverage, function($c) { return $c['status'] === 'active'; })); ?></h3>
            <p>النشطة</p>
        </div>
    </div>
</div>

<!-- Media Coverage Table -->
<div class="admin-card">
    <div class="card-header">
        <h3><i class="fas fa-list"></i> قائمة التغطية الإعلامية</h3>
        <div class="card-actions">
            <select id="typeFilter" onchange="filterByType()">
                <option value="">جميع الأنواع</option>
                <option value="advertising">إعلانية</option>
                <option value="tv">تلفزيونية</option>
            </select>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="admin-table" id="coverageTable">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>العنوان</th>
                        <th>النوع</th>
                        <th>مميز</th>
                        <th>الترتيب</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($media_coverage as $coverage): ?>
                    <tr data-type="<?php echo $coverage['type']; ?>">
                        <td>
                            <div class="coverage-image-preview">
                                <?php if (!empty($coverage['image'])): ?>
                                <img src="../assets/uploads/<?php echo $coverage['image']; ?>"
                                     alt="<?php echo htmlspecialchars($coverage['title']); ?>"
                                     onerror="this.src='../assets/img/default-media.jpg'">
                                <?php else: ?>
                                <div class="no-image">
                                    <i class="fas fa-image"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td>
                            <div class="coverage-title">
                                <strong><?php echo htmlspecialchars($coverage['title']); ?></strong>
                                <br>
                                <small><?php echo htmlspecialchars($coverage['title_ar']); ?></small>
                            </div>
                        </td>
                        <td>
                            <span class="type-badge <?php echo $coverage['type']; ?>">
                                <?php echo $coverage['type'] === 'advertising' ? 'إعلانية' : 'تلفزيونية'; ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($coverage['featured']): ?>
                            <span class="status-badge featured">مميز</span>
                            <?php else: ?>
                            <span class="status-badge">عادي</span>
                            <?php endif; ?>
                        </td>
                        <td><?php echo $coverage['sort_order']; ?></td>
                        <td>
                            <button class="status-toggle <?php echo $coverage['status']; ?>" 
                                    onclick="toggleStatus(<?php echo $coverage['id']; ?>)">
                                <?php echo $coverage['status'] === 'active' ? 'نشط' : 'غير نشط'; ?>
                            </button>
                        </td>
                        <td><?php echo date('Y-m-d', strtotime($coverage['created_at'])); ?></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-sm btn-primary" onclick="editCoverage(<?php echo $coverage['id']; ?>)" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <?php if (!empty($coverage['link'])): ?>
                                <a href="<?php echo $coverage['link']; ?>" target="_blank" class="btn btn-sm btn-info" title="عرض الرابط">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <?php endif; ?>
                                <button class="btn btn-sm btn-danger" onclick="deleteCoverage(<?php echo $coverage['id']; ?>)" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php if (empty($media_coverage)): ?>
                    <tr>
                        <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                            <i class="fas fa-tv" style="font-size: 3rem; margin-bottom: 15px; display: block; opacity: 0.3;"></i>
                            <h4>لا توجد تغطية إعلامية</h4>
                            <p>ابدأ بإضافة أول تغطية إعلامية للمهرجان</p>
                            <button class="btn btn-primary" onclick="openAddModal()" style="margin-top: 15px;">
                                <i class="fas fa-plus"></i> إضافة تغطية جديدة
                            </button>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Modal -->
<div id="coverageModal" class="modal">
    <div class="modal-content large">
        <div class="modal-header">
            <h3 id="modalTitle">إضافة تغطية إعلامية</h3>
            <span class="close" onclick="closeModal()">&times;</span>
        </div>
        <form id="coverageForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" id="formAction" value="add">
            <input type="hidden" name="id" id="coverageId">

            <div class="form-row">
                <div class="form-group">
                    <label for="title">العنوان (English) <span class="required">*</span></label>
                    <input type="text" name="title" id="title" required placeholder="Enter title in English">
                </div>
                <div class="form-group">
                    <label for="title_ar">العنوان (العربية) <span class="required">*</span></label>
                    <input type="text" name="title_ar" id="title_ar" required placeholder="أدخل العنوان بالعربية">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="description">الوصف (English)</label>
                    <textarea name="description" id="description" rows="3" placeholder="Enter description in English"></textarea>
                </div>
                <div class="form-group">
                    <label for="description_ar">الوصف (العربية)</label>
                    <textarea name="description_ar" id="description_ar" rows="3" placeholder="أدخل الوصف بالعربية"></textarea>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="type">نوع التغطية <span class="required">*</span></label>
                    <select name="type" id="type" required>
                        <option value="">اختر النوع</option>
                        <option value="advertising">إعلانية</option>
                        <option value="tv">تلفزيونية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="sort_order">ترتيب العرض</label>
                    <input type="number" name="sort_order" id="sort_order" value="0" min="0" placeholder="0">
                </div>
            </div>

            <div class="form-group">
                <label for="link">الرابط (اختياري)</label>
                <input type="url" name="link" id="link" placeholder="https://example.com">
                <small>رابط للمقال أو الفيديو الأصلي</small>
            </div>

            <div class="form-group">
                <label for="image">الصورة <span class="required">*</span></label>
                <input type="file" name="image" id="image" accept="image/*">
                <small>الحد الأقصى: 5MB. الأنواع المدعومة: JPG, PNG, GIF, WebP. الأبعاد المثلى: 300x200 بكسل</small>
                <div id="imagePreview" class="image-preview"></div>
            </div>

            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="featured" id="featured">
                    <span class="checkmark"></span>
                    تغطية مميزة (ستظهر في المقدمة)
                </label>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ التغطية
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Coverage data for editing
const coverageData = <?php echo json_encode($media_coverage); ?>;

function openAddModal() {
    document.getElementById('modalTitle').textContent = 'إضافة تغطية إعلامية';
    document.getElementById('formAction').value = 'add';
    document.getElementById('coverageForm').reset();
    document.getElementById('coverageId').value = '';
    document.getElementById('imagePreview').innerHTML = '';
    document.getElementById('coverageModal').style.display = 'block';
}

function editCoverage(id) {
    const coverage = coverageData.find(c => c.id == id);
    if (!coverage) {
        alert('لم يتم العثور على البيانات');
        return;
    }

    document.getElementById('modalTitle').textContent = 'تعديل التغطية الإعلامية';
    document.getElementById('formAction').value = 'edit';
    document.getElementById('coverageId').value = id;

    document.getElementById('title').value = coverage.title || '';
    document.getElementById('title_ar').value = coverage.title_ar || '';
    document.getElementById('description').value = coverage.description || '';
    document.getElementById('description_ar').value = coverage.description_ar || '';
    document.getElementById('type').value = coverage.type || '';
    document.getElementById('link').value = coverage.link || '';
    document.getElementById('sort_order').value = coverage.sort_order || 0;
    document.getElementById('featured').checked = coverage.featured == 1;

    // Show current image
    const imagePreview = document.getElementById('imagePreview');
    if (coverage.image) {
        imagePreview.innerHTML = `<img src="../assets/uploads/${coverage.image}" alt="Current image">`;
    } else {
        imagePreview.innerHTML = '';
    }

    document.getElementById('coverageModal').style.display = 'block';
}

function deleteCoverage(id) {
    if (confirm('هل أنت متأكد من حذف هذه التغطية؟\n\nسيتم حذف الصورة أيضاً ولا يمكن التراجع عن هذا الإجراء.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="id" value="${id}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function toggleStatus(id) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.innerHTML = `
        <input type="hidden" name="action" value="toggle_status">
        <input type="hidden" name="id" value="${id}">
    `;
    document.body.appendChild(form);
    form.submit();
}

function filterByType() {
    const filter = document.getElementById('typeFilter').value;
    const rows = document.querySelectorAll('#coverageTable tbody tr');
    let visibleCount = 0;

    rows.forEach(row => {
        const type = row.getAttribute('data-type');
        if (filter === '' || type === filter) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Update table message if no results
    const tbody = document.querySelector('#coverageTable tbody');
    const existingMessage = tbody.querySelector('.no-results-message');

    if (visibleCount === 0 && filter !== '') {
        if (!existingMessage) {
            const messageRow = document.createElement('tr');
            messageRow.className = 'no-results-message';
            messageRow.innerHTML = `
                <td colspan="8" style="text-align: center; padding: 40px; color: #666;">
                    <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 10px; display: block;"></i>
                    لا توجد نتائج للفلتر المحدد
                </td>
            `;
            tbody.appendChild(messageRow);
        }
    } else if (existingMessage) {
        existingMessage.remove();
    }
}

function closeModal() {
    document.getElementById('coverageModal').style.display = 'none';
}

// Image preview
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('imagePreview');

    if (file) {
        // Validate file size
        if (file.size > 5 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
            this.value = '';
            preview.innerHTML = '';
            return;
        }

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
            alert('نوع الملف غير مدعوم. الأنواع المدعومة: JPG, PNG, GIF, WebP');
            this.value = '';
            preview.innerHTML = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview">`;
        };
        reader.readAsDataURL(file);
    } else {
        preview.innerHTML = '';
    }
});

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('coverageModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// Form validation
document.getElementById('coverageForm').addEventListener('submit', function(e) {
    const title = document.getElementById('title').value.trim();
    const titleAr = document.getElementById('title_ar').value.trim();
    const type = document.getElementById('type').value;
    const image = document.getElementById('image').files[0];
    const isEdit = document.getElementById('formAction').value === 'edit';

    if (!title || !titleAr || !type) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة (العنوان بالإنجليزية والعربية ونوع التغطية)');
        return;
    }

    if (!isEdit && !image) {
        e.preventDefault();
        alert('يرجى اختيار صورة للتغطية');
        return;
    }

    if (image && image.size > 5 * 1024 * 1024) {
        e.preventDefault();
        alert('حجم الصورة كبير جداً. الحد الأقصى 5MB');
        return;
    }

    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    submitBtn.disabled = true;

    // Re-enable button after a delay (in case of errors)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
});

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>

<style>
.coverage-image-preview {
    width: 60px;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e0e0e0;
    background: #f8f9fa;
}

.coverage-image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1.2rem;
}

.coverage-title {
    max-width: 200px;
}

.coverage-title strong {
    color: #333;
    font-size: 0.95rem;
    display: block;
    margin-bottom: 4px;
}

.coverage-title small {
    color: #666;
    font-size: 0.85rem;
    font-style: italic;
}

.type-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.type-badge.advertising {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1976d2;
    border: 1px solid #2196f3;
}

.type-badge.tv {
    background: linear-gradient(135deg, #f3e5f5, #e1bee7);
    color: #7b1fa2;
    border: 1px solid #9c27b0;
}

.status-badge {
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.featured {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #f57c00;
    border: 1px solid #ff9800;
}

.status-toggle {
    padding: 6px 14px;
    border: none;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
}

.status-toggle.active {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #28a745;
}

.status-toggle.active:hover {
    background: linear-gradient(135deg, #c3e6cb, #b8dacc);
    transform: translateY(-1px);
}

.status-toggle.inactive {
    background: linear-gradient(135deg, #f8d7da, #f1b0b7);
    color: #721c24;
    border: 1px solid #dc3545;
}

.status-toggle.inactive:hover {
    background: linear-gradient(135deg, #f1b0b7, #e2a6ad);
    transform: translateY(-1px);
}

.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-buttons .btn {
    padding: 8px 12px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

.card-actions select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
}

.required {
    color: #dc3545;
    font-weight: bold;
}

.modal-content.large {
    max-width: 900px;
    width: 90%;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: normal !important;
}

.checkbox-label input[type="checkbox"] {
    margin-left: 10px;
    width: auto;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.image-preview {
    margin-top: 15px;
    max-width: 200px;
}

.image-preview img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    border: 1px solid #ddd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .modal-content.large {
        width: 95%;
        margin: 20px auto;
    }

    .coverage-title {
        max-width: 150px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 5px;
    }
}
</style>

<!-- Add/Edit Modal -->
<div id="coverageModal" class="modal">
    <div class="modal-content large">
        <div class="modal-header">
            <h3 id="modalTitle">إضافة تغطية إعلامية</h3>
            <span class="close" onclick="closeModal()">&times;</span>
        </div>
        <form id="coverageForm" method="POST" enctype="multipart/form-data">
            <input type="hidden" name="action" id="formAction" value="add">
            <input type="hidden" name="id" id="coverageId">

            <div class="form-row">
                <div class="form-group">
                    <label for="title">العنوان (English) <span class="required">*</span></label>
                    <input type="text" name="title" id="title" required>
                </div>
                <div class="form-group">
                    <label for="title_ar">العنوان (العربية) <span class="required">*</span></label>
                    <input type="text" name="title_ar" id="title_ar" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="type">نوع التغطية <span class="required">*</span></label>
                    <select name="type" id="type" required>
                        <option value="">اختر النوع</option>
                        <option value="advertising">إعلانية</option>
                        <option value="tv">تلفزيونية</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="sort_order">ترتيب العرض</label>
                    <input type="number" name="sort_order" id="sort_order" value="0" min="0">
                </div>
            </div>

            <div class="form-group">
                <label for="image">الصورة <span class="required">*</span></label>
                <input type="file" name="image" id="image" accept="image/*">
                <small>الحد الأقصى: 5MB. الأنواع المدعومة: JPG, PNG, GIF, WebP</small>
            </div>

            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="featured" id="featured">
                    <span class="checkmark"></span>
                    تغطية مميزة
                </label>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </form>
    </div>
</div>
