<?php
/**
 * Register Page
 */

// Check if user is already logged in
if (is_logged_in()) {
    // Redirect to home page
    header('Location: ' . base_url());
    exit;
}

// Get redirect URL if any
$redirect = isset($_GET['redirect']) ? $_GET['redirect'] : '';
$redirect_params = '';
if (isset($_GET['main_category'])) {
    $redirect_params .= '&main_category=' . $_GET['main_category'];
}
if (isset($_GET['subcategory'])) {
    $redirect_params .= '&subcategory=' . $_GET['subcategory'];
}
if (isset($_GET['id'])) {
    $redirect_params .= '&id=' . $_GET['id'];
}

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['register'])) {
    $full_name = $_POST['full_name'] ?? '';
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $agree_terms = isset($_POST['agree_terms']) ? true : false;
    
    // Validate inputs
    $errors = [];
    
    if (empty($full_name)) {
        $errors['full_name'] = get_content('register', 'full_name_required', 'Full name is required');
    }
    
    if (empty($username)) {
        $errors['username'] = get_content('register', 'username_required', 'Username is required');
    } elseif (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
        $errors['username'] = get_content('register', 'username_invalid', 'Username can only contain letters, numbers, and underscores');
    } else {
        // Check if username already exists
        $sql = "SELECT id FROM users WHERE username = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $errors['username'] = get_content('register', 'username_taken', 'Username is already taken');
        }
    }
    
    if (empty($email)) {
        $errors['email'] = get_content('register', 'email_required', 'Email is required');
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = get_content('register', 'email_invalid', 'Please enter a valid email address');
    } else {
        // Check if email already exists
        $sql = "SELECT id FROM users WHERE email = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result && $result->num_rows > 0) {
            $errors['email'] = get_content('register', 'email_taken', 'Email is already registered');
        }
    }
    
    if (empty($password)) {
        $errors['password'] = get_content('register', 'password_required', 'Password is required');
    } elseif (strlen($password) < 8) {
        $errors['password'] = get_content('register', 'password_short', 'Password must be at least 8 characters long');
    }
    
    if ($password !== $confirm_password) {
        $errors['confirm_password'] = get_content('register', 'passwords_mismatch', 'Passwords do not match');
    }
    
    if (!$agree_terms) {
        $errors['agree_terms'] = get_content('register', 'terms_required', 'You must agree to the terms and conditions');
    }
    
    // If no errors, register the user
    if (empty($errors)) {
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert user into database
        $sql = "INSERT INTO users (full_name, username, email, password, role, created_at) VALUES (?, ?, ?, ?, 'user', NOW())";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("ssss", $full_name, $username, $email, $hashed_password);
        
        if ($stmt->execute()) {
            $user_id = $stmt->insert_id;
            
            // Set session variables
            $_SESSION['user_id'] = $user_id;
            $_SESSION['user_name'] = $username;
            $_SESSION['full_name'] = $full_name;
            $_SESSION['email'] = $email;
            $_SESSION['role'] = 'user';
            
            // Redirect to appropriate page
            if (!empty($redirect)) {
                header('Location: ' . base_url('?page=' . $redirect . $redirect_params));
            } else {
                header('Location: ' . base_url());
            }
            exit;
        } else {
            $errors['register'] = get_content('register', 'registration_failed', 'Registration failed. Please try again.');
        }
    }
}
?>

<!-- Register Page -->
<section class="auth-section">
    <div class="container">
        <div class="auth-container">
            <div class="auth-header">
                <h1><?php echo get_content('register', 'title', 'Create an Account'); ?></h1>
                <p><?php echo get_content('register', 'subtitle', 'Join us and start voting for your favorites!'); ?></p>
            </div>
            
            <?php if (isset($errors['register'])): ?>
            <div class="alert alert-danger">
                <?php echo $errors['register']; ?>
            </div>
            <?php endif; ?>
            
            <div class="auth-social">
                <a href="<?php echo base_url('includes/social-login.php?provider=google'); ?>" class="social-login google">
                    <i class="fab fa-google"></i>
                    <span><?php echo get_content('register', 'google_register', 'Register with Google'); ?></span>
                </a>
                <a href="<?php echo base_url('includes/social-login.php?provider=facebook'); ?>" class="social-login facebook">
                    <i class="fab fa-facebook-f"></i>
                    <span><?php echo get_content('register', 'facebook_register', 'Register with Facebook'); ?></span>
                </a>
            </div>
            
            <div class="auth-divider">
                <span><?php echo get_content('register', 'or', 'OR'); ?></span>
            </div>
            
            <form action="" method="post" class="auth-form">
                <div class="form-group">
                    <label for="full_name"><?php echo get_content('register', 'full_name', 'Full Name'); ?></label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-user"></i></span>
                        <input type="text" id="full_name" name="full_name" class="form-control <?php echo isset($errors['full_name']) ? 'is-invalid' : ''; ?>" value="<?php echo isset($full_name) ? htmlspecialchars($full_name) : ''; ?>" placeholder="<?php echo get_content('register', 'full_name_placeholder', 'Enter your full name'); ?>">
                    </div>
                    <?php if (isset($errors['full_name'])): ?>
                    <div class="invalid-feedback">
                        <?php echo $errors['full_name']; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="username"><?php echo get_content('register', 'username', 'Username'); ?></label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-user-tag"></i></span>
                        <input type="text" id="username" name="username" class="form-control <?php echo isset($errors['username']) ? 'is-invalid' : ''; ?>" value="<?php echo isset($username) ? htmlspecialchars($username) : ''; ?>" placeholder="<?php echo get_content('register', 'username_placeholder', 'Choose a username'); ?>">
                    </div>
                    <?php if (isset($errors['username'])): ?>
                    <div class="invalid-feedback">
                        <?php echo $errors['username']; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="email"><?php echo get_content('register', 'email', 'Email Address'); ?></label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-envelope"></i></span>
                        <input type="email" id="email" name="email" class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>" value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" placeholder="<?php echo get_content('register', 'email_placeholder', 'Enter your email'); ?>">
                    </div>
                    <?php if (isset($errors['email'])): ?>
                    <div class="invalid-feedback">
                        <?php echo $errors['email']; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="password"><?php echo get_content('register', 'password', 'Password'); ?></label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                        <input type="password" id="password" name="password" class="form-control <?php echo isset($errors['password']) ? 'is-invalid' : ''; ?>" placeholder="<?php echo get_content('register', 'password_placeholder', 'Create a password'); ?>">
                        <span class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                    <?php if (isset($errors['password'])): ?>
                    <div class="invalid-feedback">
                        <?php echo $errors['password']; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password"><?php echo get_content('register', 'confirm_password', 'Confirm Password'); ?></label>
                    <div class="input-group">
                        <span class="input-icon"><i class="fas fa-lock"></i></span>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control <?php echo isset($errors['confirm_password']) ? 'is-invalid' : ''; ?>" placeholder="<?php echo get_content('register', 'confirm_password_placeholder', 'Confirm your password'); ?>">
                        <span class="password-toggle" onclick="togglePassword('confirm_password')">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                    <?php if (isset($errors['confirm_password'])): ?>
                    <div class="invalid-feedback">
                        <?php echo $errors['confirm_password']; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-group terms-checkbox <?php echo isset($errors['agree_terms']) ? 'is-invalid' : ''; ?>">
                    <input type="checkbox" id="agree_terms" name="agree_terms" <?php echo isset($agree_terms) && $agree_terms ? 'checked' : ''; ?>>
                    <label for="agree_terms">
                        <?php echo get_content('register', 'agree_terms', 'I agree to the'); ?>
                        <a href="<?php echo base_url('?page=terms'); ?>" target="_blank"><?php echo get_content('register', 'terms_link', 'Terms and Conditions'); ?></a>
                    </label>
                    <?php if (isset($errors['agree_terms'])): ?>
                    <div class="invalid-feedback">
                        <?php echo $errors['agree_terms']; ?>
                    </div>
                    <?php endif; ?>
                </div>
                
                <div class="form-submit">
                    <button type="submit" name="register" class="btn btn-primary btn-block">
                        <i class="fas fa-user-plus"></i>
                        <?php echo get_content('register', 'register_button', 'Create Account'); ?>
                    </button>
                </div>
            </form>
            
            <div class="auth-footer">
                <p>
                    <?php echo get_content('register', 'have_account', 'Already have an account?'); ?>
                    <a href="<?php echo base_url('?page=login' . ($redirect ? '&redirect=' . $redirect . $redirect_params : '')); ?>">
                        <?php echo get_content('register', 'login_link', 'Login'); ?>
                    </a>
                </p>
            </div>
        </div>
    </div>
</section>

<script>
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = input.nextElementSibling.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}
</script>
