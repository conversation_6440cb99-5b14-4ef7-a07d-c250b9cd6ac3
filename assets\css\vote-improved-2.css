/* Nominees Section */
.nominees-section {
    margin-bottom: 15px;
    background: #1a1a1a;
    padding: 10px;
    border-radius: 8px;
    border: 1px solid #333;
}

.nominees-section h2 {
    color: white;
    font-size: 1.2rem;
    margin-bottom: 10px;
    text-align: center;
}

.nominees-container {
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 5px;
    scrollbar-width: thin;
    scrollbar-color: #ffd700 #333;
}

.nominees-container::-webkit-scrollbar {
    width: 5px;
}

.nominees-container::-webkit-scrollbar-track {
    background: #333;
    border-radius: 5px;
}

.nominees-container::-webkit-scrollbar-thumb {
    background-color: #ffd700;
    border-radius: 5px;
}

.nominees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.nominee-card {
    background: #222;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #333;
    height: 100%;
    position: relative;
}

.nominee-card:hover {
    transform: translateY(-3px);
    border-color: #ffd700;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.nominee-link {
    display: block;
    text-decoration: none;
    height: 100%;
    color: white;
}

.nominee-image {
    height: 180px;
    overflow: hidden;
    position: relative;
}

.nominee-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.nominee-card:hover .nominee-image img {
    transform: scale(1.05);
}

.nominee-rank {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(255, 215, 0, 0.8);
    color: black;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.nominee-info {
    padding: 8px;
    text-align: center;
}

.nominee-info h3 {
    color: #ffd700;
    font-size: 0.9rem;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nominee-votes {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.nominee-votes i {
    color: #ffd700;
    font-size: 0.9rem;
    margin-right: 3px;
}

.votes-count {
    color: white;
    font-size: 0.9rem;
    font-weight: bold;
    margin-right: 3px;
}

.votes-label {
    color: #aaa;
    font-size: 0.7rem;
}

.vote-status {
    margin: 3px 0;
}

.voted-badge {
    display: inline-block;
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
    padding: 2px 5px;
    border-radius: 10px;
    font-size: 0.7rem;
}

.voted-badge i {
    margin-right: 2px;
}

.quick-vote-form {
    margin-top: 5px;
}

.btn-vote-small {
    background-color: #ffd700;
    color: black;
    border: none;
    padding: 3px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
}

.btn-vote-small i {
    margin-right: 3px;
}

.btn-vote-small:hover {
    background-color: #ffdf4d;
    transform: translateY(-2px);
}

.btn-vote-small.voted {
    background-color: #28a745;
    cursor: default;
}

.btn-vote-small.voted:hover {
    transform: none;
}

.btn-login-small {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 3px 8px;
    font-size: 0.8rem;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    text-decoration: none;
}

.btn-login-small i {
    margin-right: 3px;
}

.btn-login-small:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
    }
    
    .nominee-image {
        height: 160px;
    }
}

@media (max-width: 768px) {
    .main-category-item {
        width: 70px;
    }
    
    .category-icon {
        width: 45px;
        height: 45px;
    }
    
    .subcategory-item {
        width: 70px;
    }
    
    .subcategory-icon {
        width: 35px;
        height: 35px;
    }
    
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    }
    
    .nominee-image {
        height: 140px;
    }
    
    .nominee-info h3 {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .main-category-item {
        width: 60px;
    }
    
    .category-icon {
        width: 40px;
        height: 40px;
    }
    
    .main-category-item h3 {
        font-size: 0.65rem;
    }
    
    .subcategory-item {
        width: 60px;
    }
    
    .subcategory-icon {
        width: 30px;
        height: 30px;
    }
    
    .subcategory-item h3 {
        font-size: 0.65rem;
    }
    
    .nominees-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
    }
    
    .nominee-image {
        height: 130px;
    }
    
    .nominee-info {
        padding: 5px;
    }
    
    .nominee-info h3 {
        font-size: 0.75rem;
    }
    
    .votes-count {
        font-size: 0.8rem;
    }
    
    .votes-label {
        font-size: 0.6rem;
    }
    
    .btn-vote-small, .btn-login-small {
        font-size: 0.7rem;
        padding: 2px 5px;
    }
}
