/**
 * CEO Page <PERSON>Script
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add social media preview
    initSocialMediaPreview();
    
    // Add character counter for bio
    initCharacterCounter();
});

/**
 * Add social media preview
 */
function initSocialMediaPreview() {
    const socialInputs = document.querySelectorAll('input[type="url"]');
    
    socialInputs.forEach(input => {
        // Create preview icon if it doesn't exist
        let previewIcon = input.parentElement.querySelector('.social-preview');
        if (!previewIcon) {
            previewIcon = document.createElement('a');
            previewIcon.className = 'social-preview';
            previewIcon.target = '_blank';
            previewIcon.rel = 'noopener noreferrer';
            previewIcon.style.marginLeft = '10px';
            previewIcon.style.fontSize = '1.2rem';
            previewIcon.style.color = '#aaa';
            previewIcon.style.textDecoration = 'none';
            previewIcon.style.display = 'none';
            
            // Get the icon class from the label
            const label = input.parentElement.querySelector('label');
            if (label) {
                const icon = label.querySelector('i');
                if (icon) {
                    const iconClass = icon.className;
                    previewIcon.innerHTML = `<i class="${iconClass}"></i>`;
                }
            }
            
            input.parentElement.appendChild(previewIcon);
        }
        
        // Update preview on input change
        input.addEventListener('input', function() {
            const url = this.value.trim();
            
            if (url) {
                previewIcon.href = url;
                previewIcon.style.display = 'inline-block';
            } else {
                previewIcon.style.display = 'none';
            }
        });
        
        // Trigger the event on load
        const event = new Event('input');
        input.dispatchEvent(event);
    });
}

/**
 * Add character counter for bio
 */
function initCharacterCounter() {
    const bioTextareas = document.querySelectorAll('#bio, #bio_ar');
    
    bioTextareas.forEach(textarea => {
        // Create counter if it doesn't exist
        let counter = textarea.parentElement.querySelector('.character-counter');
        if (!counter) {
            counter = document.createElement('div');
            counter.className = 'character-counter';
            counter.style.textAlign = 'right';
            counter.style.fontSize = '0.85rem';
            counter.style.color = '#aaa';
            counter.style.marginTop = '5px';
            
            textarea.parentElement.appendChild(counter);
        }
        
        // Update counter on input change
        textarea.addEventListener('input', function() {
            const count = this.value.length;
            const maxLength = 500; // Recommended max length
            
            counter.textContent = `${count} / ${maxLength} حرف`;
            
            if (count > maxLength) {
                counter.style.color = '#dc3545';
            } else {
                counter.style.color = '#aaa';
            }
        });
        
        // Trigger the event on load
        const event = new Event('input');
        textarea.dispatchEvent(event);
    });
}

// Add CSS for social media preview animation
const style = document.createElement('style');
style.textContent = `
    .social-preview {
        transition: all 0.3s ease;
    }
    
    .social-preview:hover {
        color: var(--color-gold) !important;
        transform: scale(1.2);
    }
`;
document.head.appendChild(style);
