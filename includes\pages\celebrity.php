<?php
/**
 * Celebrity Profile Page
 */

// Check if ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    // Redirect to celebrities page if no ID is provided
    header('Location: ' . base_url('?page=celebrities'));
    exit;
}

$celebrity_id = (int)$_GET['id'];

// Get celebrity details
$sql = "SELECT * FROM celebrities WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $celebrity_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // Redirect to celebrities page if celebrity not found
    header('Location: ' . base_url('?page=celebrities'));
    exit;
}

$celebrity = $result->fetch_assoc();

// Get related celebrities (same role, excluding current celebrity)
$sql = "SELECT * FROM celebrities WHERE role = ? AND id != ? ORDER BY RAND() LIMIT 4";
$stmt = $conn->prepare($sql);
$stmt->bind_param("si", $celebrity['role'], $celebrity_id);
$stmt->execute();
$related_result = $stmt->get_result();
$related_celebrities = [];

if ($related_result && $related_result->num_rows > 0) {
    while ($row = $related_result->fetch_assoc()) {
        $related_celebrities[] = $row;
    }
}

// If no related celebrities with same role, get random celebrities
if (empty($related_celebrities)) {
    $sql = "SELECT * FROM celebrities WHERE id != ? ORDER BY RAND() LIMIT 4";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("i", $celebrity_id);
    $stmt->execute();
    $random_result = $stmt->get_result();

    if ($random_result && $random_result->num_rows > 0) {
        while ($row = $random_result->fetch_assoc()) {
            $related_celebrities[] = $row;
        }
    }
}

// Page title
$page_title = $current_lang === 'ar' ? $celebrity['name_ar'] : $celebrity['name'];
?>

<!-- Add celebrity.css to head -->
<link rel="stylesheet" href="<?php echo asset_url('css/celebrity.css'); ?>">

<!-- Celebrity Profile Page -->
<section class="celebrity-profile-section">
    <div class="container">
        <div class="celebrity-profile-header">
            <a href="<?php echo base_url('?page=celebrities'); ?>" class="back-to-celebrities">
                <i class="fas fa-arrow-left"></i> <?php echo get_content('celebrity', 'back_to_celebrities', 'Back to Festival Stars'); ?>
            </a>
            <h1 class="celebrities-title"><?php echo get_content('celebrity', 'profile_title', 'Celebrity Profile'); ?></h1>
        </div>

        <div class="celebrity-profile-container">
            <div class="celebrity-profile-image">
                <img src="<?php echo asset_url('uploads/' . ($celebrity['image'] ?? 'default-celebrity.jpg')); ?>" alt="<?php echo htmlspecialchars($current_lang === 'ar' ? $celebrity['name_ar'] : $celebrity['name']); ?>">
                <div class="celebrity-profile-role">
                    <?php echo $current_lang === 'ar' ? $celebrity['role_ar'] : $celebrity['role']; ?>
                </div>
            </div>

            <div class="celebrity-profile-info">
                <h2 class="celebrity-profile-name"><?php echo $current_lang === 'ar' ? $celebrity['name_ar'] : $celebrity['name']; ?></h2>

                <div class="celebrity-profile-bio">
                    <?php echo $current_lang === 'ar' ? $celebrity['bio_ar'] : $celebrity['bio']; ?>
                </div>

                <div class="celebrity-profile-social">
                    <?php if (!empty($celebrity['instagram'])): ?>
                    <a href="https://instagram.com/<?php echo $celebrity['instagram']; ?>" target="_blank" class="celebrity-profile-social-icon">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($celebrity['twitter'])): ?>
                    <a href="https://twitter.com/<?php echo $celebrity['twitter']; ?>" target="_blank" class="celebrity-profile-social-icon">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($celebrity['facebook'])): ?>
                    <a href="https://facebook.com/<?php echo $celebrity['facebook']; ?>" target="_blank" class="celebrity-profile-social-icon">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <?php endif; ?>

                    <?php if (!empty($celebrity['youtube'])): ?>
                    <a href="https://youtube.com/<?php echo $celebrity['youtube']; ?>" target="_blank" class="celebrity-profile-social-icon">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <?php endif; ?>
                </div>

                <div class="celebrity-profile-actions">
                    <a href="<?php echo base_url('?page=vote'); ?>" class="celebrity-profile-btn btn-vote">
                        <i class="fas fa-vote-yea"></i> <?php echo get_content('celebrity', 'vote_now', 'Vote Now'); ?>
                    </a>

                    <a href="#" class="celebrity-profile-btn btn-share" id="share-profile">
                        <i class="fas fa-share-alt"></i> <?php echo get_content('celebrity', 'share_profile', 'Share Profile'); ?>
                    </a>

                    <?php if (!empty($celebrity['video'])): ?>
                    <a href="#video-section" class="celebrity-profile-btn btn-watch">
                        <i class="fas fa-play"></i> <?php echo get_content('celebrity', 'watch_video', 'Watch Video'); ?>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if (!empty($celebrity['video'])): ?>
        <div class="celebrity-profile-video" id="video-section">
            <h2><?php echo get_content('celebrity', 'videos', 'Videos'); ?></h2>
            <div class="celebrity-profile-video-container">
                <video controls poster="<?php echo asset_url('images/' . ($celebrity['image'] ?? 'default-celebrity.jpg')); ?>">
                    <source src="<?php echo asset_url('videos/' . $celebrity['video']); ?>" type="video/mp4">
                    <?php echo get_content('celebrity', 'video_not_supported', 'Your browser does not support the video tag.'); ?>
                </video>
            </div>
            <div class="video-caption">
                <p><?php echo get_content('celebrity', 'video_caption', 'Watch exclusive content from'); ?> <?php echo $current_lang === 'ar' ? $celebrity['name_ar'] : $celebrity['name']; ?></p>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($related_celebrities)): ?>
        <div class="related-celebrities">
            <div class="related-celebrities-title">
                <h2><?php echo get_content('celebrity', 'related_celebrities', 'Related Stars'); ?></h2>
            </div>

            <div class="related-celebrities-grid">
                <?php foreach ($related_celebrities as $related): ?>
                <div class="celebrity-card">
                    <div class="celebrity-image">
                        <img src="<?php echo asset_url('uploads/' . ($related['image'] ?? 'default-celebrity.jpg')); ?>" alt="<?php echo htmlspecialchars($current_lang === 'ar' ? $related['name_ar'] : $related['name']); ?>">
                        <div class="celebrity-category">
                            <?php echo $current_lang === 'ar' ? $related['role_ar'] : $related['role']; ?>
                        </div>
                    </div>
                    <div class="celebrity-info">
                        <h3 class="celebrity-name"><?php echo $current_lang === 'ar' ? $related['name_ar'] : $related['name']; ?></h3>
                        <div class="celebrity-description">
                            <?php
                            $bio = $current_lang === 'ar' ?
                                ($related['bio_ar'] ?? '') :
                                ($related['bio'] ?? '');
                            echo mb_substr($bio, 0, 100) . (mb_strlen($bio) > 100 ? '...' : '');
                            ?>
                        </div>
                        <div class="celebrity-meta">
                            <div class="celebrity-social">
                                <?php if (!empty($related['instagram'])): ?>
                                <a href="https://instagram.com/<?php echo $related['instagram']; ?>" target="_blank" class="social-icon">
                                    <i class="fab fa-instagram"></i>
                                </a>
                                <?php endif; ?>
                            </div>
                            <div class="celebrity-actions">
                                <a href="<?php echo base_url('?page=celebrity&id=' . $related['id']); ?>" class="btn-view">
                                    <i class="fas fa-user"></i> <?php echo get_content('celebrities', 'view_profile', 'View Profile'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Share functionality
    const shareButton = document.getElementById('share-profile');

    if (shareButton) {
        shareButton.addEventListener('click', function(e) {
            e.preventDefault();
            const title = '<?php echo addslashes($current_lang === 'ar' ? $celebrity['name_ar'] : $celebrity['name']); ?>';
            const url = window.location.href;

            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                }).catch(console.error);
            } else {
                // Fallback for browsers that don't support Web Share API
                prompt('<?php echo get_content('celebrity', 'copy_link', 'Copy this link to share:'); ?>', url);
            }
        });
    }

    // Add animations
    if (typeof gsap !== 'undefined') {
        // Main profile animations
        gsap.from('.celebrity-profile-image', {
            x: -100,
            opacity: 0,
            duration: 1,
            ease: 'power2.out'
        });

        gsap.from('.celebrity-profile-info', {
            x: 100,
            opacity: 0,
            duration: 1,
            ease: 'power2.out'
        });

        // Video section animation
        gsap.from('.celebrity-profile-video', {
            y: 50,
            opacity: 0,
            duration: 0.8,
            delay: 0.5,
            ease: 'power2.out'
        });

        // Related celebrities animation
        const relatedCards = document.querySelectorAll('.celebrity-card');

        relatedCards.forEach((card, index) => {
            gsap.from(card, {
                y: 50,
                opacity: 0,
                duration: 0.8,
                delay: 0.8 + (index * 0.1),
                ease: 'power2.out'
            });
        });

        // Social icons animation
        const socialIcons = document.querySelectorAll('.celebrity-profile-social-icon');

        socialIcons.forEach((icon, index) => {
            gsap.from(icon, {
                scale: 0,
                opacity: 0,
                duration: 0.5,
                delay: 0.3 + (index * 0.1),
                ease: 'back.out(1.7)'
            });
        });

        // Action buttons animation
        const actionButtons = document.querySelectorAll('.celebrity-profile-btn');

        actionButtons.forEach((button, index) => {
            gsap.from(button, {
                y: 20,
                opacity: 0,
                duration: 0.5,
                delay: 0.5 + (index * 0.1),
                ease: 'power1.out'
            });
        });
    }

    // Smooth scroll to video section
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 100,
                    behavior: 'smooth'
                });
            }
        });
    });
});
</script>
