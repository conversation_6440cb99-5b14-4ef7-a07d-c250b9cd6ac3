/**
 * محسن الصور - تحسين جودة وسرعة تحميل الصور
 */

class ImageOptimizer {
    constructor() {
        this.webpSupported = this.checkWebPSupport();
        this.init();
    }

    init() {
        this.optimizeImages();
        this.setupResponsiveImages();
        this.preloadCriticalImages();
    }

    // فحص دعم WebP
    checkWebPSupport() {
        return new Promise((resolve) => {
            const webP = new Image();
            webP.onload = webP.onerror = function () {
                resolve(webP.height === 2);
            };
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }

    // تحسين جميع الصور
    async optimizeImages() {
        const webpSupported = await this.webpSupported;
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            this.optimizeImage(img, webpSupported);
        });
    }

    // تحسين صورة واحدة
    optimizeImage(img, webpSupported) {
        // إضافة loading="lazy" للصور
        if (!img.hasAttribute('loading')) {
            img.setAttribute('loading', 'lazy');
        }

        // تحسين الجودة حسب حجم الشاشة
        this.setResponsiveSrc(img);

        // إضافة WebP إذا كان مدعوم
        if (webpSupported && img.src && !img.src.includes('.webp')) {
            this.tryWebPVersion(img);
        }

        // إضافة placeholder أثناء التحميل
        this.addPlaceholder(img);

        // تحسين الأداء
        img.decoding = 'async';
    }

    // تجربة نسخة WebP
    tryWebPVersion(img) {
        const webpSrc = img.src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
        
        // فحص وجود نسخة WebP
        const testImg = new Image();
        testImg.onload = () => {
            img.src = webpSrc;
            console.log('✅ WebP version loaded:', webpSrc);
        };
        testImg.onerror = () => {
            console.log('❌ WebP version not found, using original:', img.src);
        };
        testImg.src = webpSrc;
    }

    // إعداد الصور المتجاوبة
    setResponsiveSrc(img) {
        const originalSrc = img.src || img.dataset.src;
        if (!originalSrc) return;

        // تحديد الحجم المناسب حسب الشاشة
        const screenWidth = window.innerWidth;
        let targetWidth;

        if (screenWidth <= 480) {
            targetWidth = 480;
        } else if (screenWidth <= 768) {
            targetWidth = 768;
        } else if (screenWidth <= 1200) {
            targetWidth = 1200;
        } else {
            targetWidth = 1920;
        }

        // إنشاء srcset للأحجام المختلفة
        if (!img.srcset && originalSrc.includes('uploads/')) {
            const baseName = originalSrc.replace(/\.(jpg|jpeg|png|webp)$/i, '');
            const extension = originalSrc.match(/\.(jpg|jpeg|png|webp)$/i)?.[0] || '.jpg';
            
            const srcset = [
                `${baseName}_480${extension} 480w`,
                `${baseName}_768${extension} 768w`,
                `${baseName}_1200${extension} 1200w`,
                `${originalSrc} 1920w`
            ].join(', ');
            
            img.srcset = srcset;
            img.sizes = '(max-width: 480px) 480px, (max-width: 768px) 768px, (max-width: 1200px) 1200px, 1920px';
        }
    }

    // إضافة placeholder
    addPlaceholder(img) {
        if (!img.complete && !img.classList.contains('has-placeholder')) {
            img.classList.add('lazy-placeholder', 'has-placeholder');
            
            img.addEventListener('load', () => {
                img.classList.remove('lazy-placeholder');
                img.classList.add('lazy-loaded');
            }, { once: true });

            img.addEventListener('error', () => {
                img.classList.remove('lazy-placeholder');
                img.classList.add('lazy-error');
                console.error('❌ Image failed to load:', img.src);
            }, { once: true });
        }
    }

    // تحميل مسبق للصور المهمة
    preloadCriticalImages() {
        // الصور في الـ viewport الأول
        const criticalImages = document.querySelectorAll('img[data-critical], .hero img, .logo img');
        
        criticalImages.forEach(img => {
            if (img.dataset.src) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = img.dataset.src;
                document.head.appendChild(link);
                console.log('🚀 Preloading critical image:', img.dataset.src);
            }
        });
    }

    // تحسين الصور الجديدة (للمحتوى الديناميكي)
    async optimizeNewImages(container = document) {
        const webpSupported = await this.webpSupported;
        const newImages = container.querySelectorAll('img:not(.optimized)');
        
        newImages.forEach(img => {
            this.optimizeImage(img, webpSupported);
            img.classList.add('optimized');
        });
    }
}

// تشغيل محسن الصور
document.addEventListener('DOMContentLoaded', function() {
    console.log('🖼️ Initializing Image Optimizer...');
    window.imageOptimizer = new ImageOptimizer();
});

// إعادة تحسين عند تغيير حجم الشاشة
let resizeTimeout;
window.addEventListener('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        if (window.imageOptimizer) {
            window.imageOptimizer.setupResponsiveImages();
        }
    }, 250);
});

// CSS إضافي للتحسين
const optimizerStyles = `
<style>
/* تحسين عرض الصور */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Placeholder للصور */
.lazy-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    min-height: 200px;
}

@keyframes shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* تأثير التحميل */
.lazy-loaded {
    animation: fadeInImage 0.3s ease-in;
}

@keyframes fadeInImage {
    from { opacity: 0; transform: scale(1.05); }
    to { opacity: 1; transform: scale(1); }
}

/* خطأ في التحميل */
.lazy-error {
    background: #f5f5f5;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    font-size: 0.9rem;
}

.lazy-error::before {
    content: "فشل تحميل الصورة";
}

/* تحسين الأداء */
img[loading="lazy"] {
    content-visibility: auto;
}
</style>
`;

document.head.insertAdjacentHTML('beforeend', optimizerStyles);
